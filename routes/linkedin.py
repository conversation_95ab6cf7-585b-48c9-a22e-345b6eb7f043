import os
import requests
from flask import Blueprint, redirect, request
from db import save_profile
from dotenv import load_dotenv
from flask import session
from bson import ObjectId
from routes.auth import login_required
from flask import flash, url_for  # Ensure url_for is also imported here

load_dotenv()

bp = Blueprint("linkedin", __name__, url_prefix='/linkedin')

CLIENT_ID = os.getenv("LINKEDIN_CLIENT_ID")
CLIENT_SECRET = os.getenv("LINKEDIN_CLIENT_SECRET")
REDIRECT_URI = os.getenv("LINKEDIN_REDIRECT_URI")


@bp.route("/login")
@login_required
def login():
    import logging
    logging.info(f"LinkedIn login - Session before redirect: {dict(session)}")
    
    # Get user_id from session
    user_id = session.get("user_id", "")
    
    # Include user_id in the state parameter
    state = f"agenticoauth_{user_id}"
    
    auth_url = (
        "https://www.linkedin.com/oauth/v2/authorization"
        f"?response_type=code"
        f"&client_id={CLIENT_ID}"
        f"&redirect_uri={REDIRECT_URI}"
        f"&state={state}"
        "&scope=w_member_social"
    )
    return redirect(auth_url)


@bp.route("/callback")
def callback():
    try:
        code = request.args.get("code")
        state = request.args.get("state", "")
        
        if not code:
            return "Authorization code not found", 400

        # Log the session state at the beginning of the callback
        import logging
        logging.info(f"LinkedIn callback - Session at start: {dict(session)}")
        
        # Try to extract user_id from state parameter
        user_id_from_state = None
        if state.startswith("agenticoauth_"):
            user_id_from_state = state.split("_")[1]
            logging.info(f"Extracted user_id from state: {user_id_from_state}")

        token_url = "https://www.linkedin.com/oauth/v2/accessToken"
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": REDIRECT_URI,
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET
        }

        token_resp = requests.post(token_url, data=token_data, headers={'Content-Type': 'application/x-www-form-urlencoded'})
        token_json = token_resp.json()
        access_token = token_json.get("access_token")

        if not access_token:
            return f"Token exchange failed: {token_json}", 400

        headers = {"Authorization": f"Bearer {access_token}"}
        profile_resp = requests.get("https://api.linkedin.com/v2/me", headers=headers)
        profile_data = profile_resp.json()

        # Create profile data
        profile_record = {
            "platform": "linkedin",
            "profile_id": profile_data.get("id"),
            "username": f"{profile_data.get('localizedFirstName', '')} {profile_data.get('localizedLastName', '')}",
            "access_token": access_token,
            "refresh_token": token_json.get("refresh_token", ""),
            "expires_at": token_json.get("expires_in", 0),
            "raw_profile": profile_data
        }

        # Check if user is logged in via session
        user_id = session.get("user_id")
        
        # If no user_id in session, try to use the one from state
        if not user_id and user_id_from_state:
            user_id = user_id_from_state
            logging.info(f"Using user_id from state parameter: {user_id}")
            session["user_id"] = user_id
        
        if not user_id:
            logging.error("LinkedIn callback - No user_id in session or state")
            flash("Please log in to connect your LinkedIn account")
            return redirect(url_for('auth.send_otp', next=request.url))
        
        logging.info(f"LinkedIn callback - Using user_id: {user_id} of type {type(user_id)}")
        
        # Convert user_id to ObjectId for MongoDB
        from bson import ObjectId
        try:
            profile_record["user_id"] = ObjectId(user_id)
        except Exception as e:
            logging.error(f"Failed to convert user_id to ObjectId: {str(e)}")
            return "Invalid user ID", 400
        
        # Save the profile
        save_profile(profile_record)
        
        # Set a flash message for success
        flash("LinkedIn account connected successfully!")
        
        # Get CSRF token to include in the form
        from flask import current_app
        csrf_token = session.get('csrf_token', '')
        
        # Return success message with a form that posts to connect route
        return f"""
        <div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">
            <h2 style="color: #0077B5;">✅ LinkedIn account connected successfully!</h2>
            <p>Your LinkedIn account has been connected to your profile.</p>
            <form action="{url_for('connect')}" method="get">
                <input type="hidden" name="csrf_token" value="{csrf_token}">
                <button type="submit" style="margin-top: 20px; padding: 10px 20px; background-color: #0077B5; color: white; border: none; border-radius: 5px; cursor: pointer;">Back to Connect Page</button>
            </form>
        </div>
        """
        
    except Exception as e:
        import logging
        logging.exception(f"Error in LinkedIn callback: {str(e)}")
        return f"An error occurred: {str(e)}", 500
