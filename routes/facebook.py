import os
import requests
from flask import Blueprint, redirect, request, url_for, session, flash, current_app
from db import save_profile
from dotenv import load_dotenv
from bson import ObjectId
from routes.auth import login_required

load_dotenv()

bp = Blueprint("facebook", __name__, url_prefix='/facebook')

CLIENT_ID = os.getenv("FB_CLIENT_ID")
CLIENT_SECRET = os.getenv("FB_CLIENT_SECRET")
REDIRECT_URI = os.getenv("FB_REDIRECT_URI")

SCOPES = "pages_show_list pages_read_engagement pages_manage_posts pages_show_list"


@bp.route("/login")
@login_required
def login():
    import logging
    logging.info(f"Facebook login - Session before redirect: {dict(session)}")
    
    # Get user_id from session
    user_id = session.get("user_id", "")
    
    # Include user_id in the state parameter
    state = f"agenticoauth_{user_id}"
    
    auth_url = (
        "https://www.facebook.com/v17.0/dialog/oauth"
        f"?client_id={CLIENT_ID}"
        f"&redirect_uri={REDIRECT_URI}"
        f"&scope={SCOPES.replace(' ', '%2C')}"
        f"&response_type=code"
        f"&state={state}"
    )
    return redirect(auth_url)


@bp.route("/callback")
def callback():
    try:
        code = request.args.get("code")
        state = request.args.get("state", "")
        
        if not code:
            return "Authorization code not found", 400

        # Log the session state at the beginning of the callback
        import logging
        logging.info(f"Facebook callback - Session at start: {dict(session)}")
        
        # Try to extract user_id from state parameter
        user_id_from_state = None
        if state.startswith("agenticoauth_"):
            user_id_from_state = state.split("_")[1]
            logging.info(f"Extracted user_id from state: {user_id_from_state}")

        # Exchange code for access token
        token_url = "https://graph.facebook.com/v17.0/oauth/access_token"
        params = {
            "client_id": CLIENT_ID,
            "redirect_uri": REDIRECT_URI,
            "client_secret": CLIENT_SECRET,
            "code": code
        }

        token_resp = requests.get(token_url, params=params)
        token_json = token_resp.json()

        access_token = token_json.get("access_token")
        if not access_token:
            return f"Token exchange failed: {token_json}", 400

        # Fetch user profile
        user_url = "https://graph.facebook.com/me"
        profile_resp = requests.get(user_url, params={"access_token": access_token})
        profile_data = profile_resp.json()

        profile_record = {
            "platform": "facebook",
            "profile_id": profile_data.get("id"),
            "username": profile_data.get("name"),
            "access_token": access_token,
            "refresh_token": "",
            "expires_at": token_json.get("expires_in", 0),
            "raw_profile": profile_data
        }
        
        # Check if user is logged in via session
        user_id = session.get("user_id")
        
        # If no user_id in session, try to use the one from state
        if not user_id and user_id_from_state:
            user_id = user_id_from_state
            logging.info(f"Using user_id from state parameter: {user_id}")
            # Set the user_id in the session to maintain it
            session["user_id"] = user_id
        
        if not user_id:
            logging.error("Facebook callback - No user_id in session or state")
            flash("Please log in to connect your Facebook account")
            return redirect(url_for('auth.send_otp', next=request.url))
        
        logging.info(f"Facebook callback - Using user_id: {user_id} of type {type(user_id)}")
        
        # Convert user_id to ObjectId for MongoDB
        try:
            profile_record["user_id"] = ObjectId(user_id)
        except Exception as e:
            logging.error(f"Failed to convert user_id to ObjectId: {str(e)}")
            return "Invalid user ID", 400
        
        # Save the profile
        save_profile(profile_record)
        
        # Set a flash message for success
        flash("Facebook account connected successfully!")
        
        # Get CSRF token to include in the form
        csrf_token = session.get('csrf_token', '')
        
        # Log the session before returning the HTML
        logging.info(f"Facebook callback - Session before returning HTML: {dict(session)}")
        
        # Return success message with a form that posts to connect route
        return f"""
        <div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">
            <h2 style="color: #1877F2;">✅ Facebook account connected successfully!</h2>
            <p>Your Facebook account has been connected to your profile.</p>
            <form action="{url_for('connect')}" method="get">
                <input type="hidden" name="csrf_token" value="{csrf_token}">
                <button type="submit" style="margin-top: 20px; padding: 10px 20px; background-color: #1877F2; color: white; border: none; border-radius: 5px; cursor: pointer;">Back to Connect Page</button>
            </form>
        </div>
        """
        
    except Exception as e:
        import logging
        logging.exception(f"Error in Facebook callback: {str(e)}")
        return f"An error occurred: {str(e)}", 500
