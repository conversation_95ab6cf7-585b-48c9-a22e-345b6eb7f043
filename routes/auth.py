import os
import random
from flask import Blueprint, request, session, render_template, redirect, url_for, flash
from twilio.rest import Client
from db import save_otp, verify_login, get_user_by_mobile, update_user_info, get_user
from dotenv import load_dotenv
from functools import wraps
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

load_dotenv()

bp = Blueprint("auth", __name__, url_prefix="/auth")

limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        import logging
        logging.info(f"login_required check - Session: {dict(session)}")
        
        if "user_id" not in session:
            logging.error("login_required - No user_id in session")
            # Save the requested URL for redirecting after login
            from flask import request, url_for, flash
            next_url = request.url
            flash("Please log in to access this page")
            return redirect(url_for("auth.send_otp", next=next_url))
        
        # Verify that the user exists in the database
        from db import get_user
        user_id = session.get("user_id")
        user = get_user(user_id)
        
        if not user:
            logging.error(f"login_required - User with ID {user_id} not found in database")
            # Clear the session and redirect to login
            session.clear()
            from flask import flash
            flash("Your session has expired. Please log in again.")
            return redirect(url_for("auth.send_otp", next=request.url))
        
        logging.info(f"login_required - User is logged in with ID: {user_id}")
        return f(*args, **kwargs)
    return decorated_function


@bp.route("/send-otp", methods=["GET", "POST"])
@limiter.limit("5 per minute")
def send_otp():
    if request.method == "GET":
        return render_template("index.html")

    try:
        mobile = request.form.get("mobile")
        if not mobile:
            return "Mobile number is required", 400

        # Generate OTP
        otp = str(random.randint(100000, 999999))

        # Save OTP in otp_verification collection
        user_id = save_otp(mobile, otp, type="login")
        
        # Check if user is new
        is_new_user = user_id is None

        # Send OTP via Twilio
        client = Client(os.getenv("TWILIO_SID"), os.getenv("TWILIO_AUTH"))
        message = client.messages.create(
            body=f"Your login OTP is {otp}",
            from_=os.getenv("TWILIO_PHONE"),
            to=mobile
        )
        
        import logging
        logging.info(f"OTP sent to {mobile}, Twilio SID: {message.sid}")

        return render_template("enter_otp.html", mobile=mobile, is_new_user=is_new_user)

    except Exception as e:
        import logging
        logging.exception(f"OTP send error: {str(e)}")
        return "Failed to send OTP", 500


@bp.route("/verify-otp", methods=["POST"])
def verify_otp():
    mobile = request.form["mobile"]
    otp = request.form["otp"]
    
    # Get additional user info if provided
    name = request.form.get("name")
    email = request.form.get("email")
    
    # Verify OTP
    user_id = verify_login(mobile, otp)
    
    if user_id:
        # Set user session
        session["user_id"] = str(user_id)  # Ensure it's a string
        session.permanent = True  # Enable session expiration
        
        import logging
        logging.info(f"User logged in - User ID: {user_id}")
        logging.info(f"Session after login: {dict(session)}")
        
        # Check if user has name and email
        user = get_user(user_id)
        if (not user.get("name") or not user.get("email")) and not (name or email):
            # Redirect to complete profile page
            return redirect(url_for("auth.complete_profile"))
        
        # Update user info if provided
        if name or email:
            update_user_info(user_id, name, email)
            print(f"Updated user {user_id} with name: {name}, email: {email}")
        
        # Redirect to next URL or default
        next_url = request.args.get("next", "/connect")
        return redirect(next_url)
    else:
        import logging
        logging.error(f"Invalid OTP for mobile: {mobile}")
        return "Invalid OTP", 401


@bp.before_app_request
def configure_session():
    if not hasattr(bp, '_session_configured'):
        # Set secure cookie settings
        from flask import current_app
        current_app.config.update(
            SESSION_COOKIE_SECURE=False,  # Set to True in production with HTTPS
            SESSION_COOKIE_HTTPONLY=True,
            SESSION_COOKIE_SAMESITE='Lax',
            PERMANENT_SESSION_LIFETIME=1800  # 30 minutes
        )
        bp._session_configured = True


@bp.route("/logout")
def logout():
    import logging
    logging.info(f"Logout called - Session before logout: {dict(session)}")
    
    # Clear all session data
    session.pop("user_id", None)
    session.clear()
    
    logging.info(f"Session after logout: {dict(session)}")
    return redirect("/")


@bp.route("/complete-profile", methods=["GET", "POST"])
@login_required
def complete_profile():
    if request.method == "GET":
        # Get user data
        user_id = session.get("user_id")
        user = get_user(user_id)
        
        if not user:
            return redirect(url_for("auth.send_otp"))
        
        # Check if profile is already complete
        if user.get("name") and user.get("email"):
            return redirect("/connect")
        
        return render_template("complete_profile.html", user=user)
    
    elif request.method == "POST":
        # Get form data
        name = request.form.get("name")
        email = request.form.get("email")
        
        if not name or not email:
            flash("Please provide both name and email", "error")
            return redirect(url_for("auth.complete_profile"))
        
        # Update user info
        user_id = session.get("user_id")
        update_user_info(user_id, name, email)
        
        flash("Profile updated successfully", "success")
        
        # Redirect to next URL or default
        next_url = request.args.get("next", "/connect")
        return redirect(next_url)
