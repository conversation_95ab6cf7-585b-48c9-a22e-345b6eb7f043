
import logging
from flask import Blueprint, request, jsonify, session
from bson import ObjectId
import requests
import hmac, hashlib, base64, time, os
from db import users  # reuse existing collection handle

# -------- Service Auth (HMAC) --------
# Supports two modes:
# 1) Per-user secret stored in Mongo under users: { _id, service_api: {key_id, secret} }
# 2) Global service secret via env SERVICE_API_KEY_ID + SERVICE_API_SECRET (fallback)
#
# Client (n8n) must send headers:
#   X-User-Id: <mongodb id string>
#   X-Key-Id: <key id>         # optional if using global
#   X-Timestamp: <epoch seconds>
#   Authorization: HMAC <base64(hmac_sha256(secret, f"{keyId}.{timestamp}.{rawBody}"))>
#
# The signature window defaults to +/- 5 minutes.
#
def _get_user_service_secret(user_id: str, key_id: str | None):
    try:
        doc = users.find_one({"_id": ObjectId(user_id)}, {"service_api": 1})
        api = (doc or {}).get("service_api") or {}
        # support structure: {key_id, secret} or {keys: [{key_id, secret}]}
        if api.get("secret") and (not key_id or api.get("key_id") == key_id):
            return api.get("key_id") or "user-default", api.get("secret")
        for k in (api.get("keys") or []):
            if not key_id or k.get("key_id") == key_id:
                return k.get("key_id"), k.get("secret")
    except Exception:
        pass
    return None, None

def _verify_service_signature(raw_body: bytes):
    hdr_auth = request.headers.get("Authorization", "")
    hdr_user = request.headers.get("X-User-Id", "")
    hdr_key  = request.headers.get("X-Key-Id", "")
    hdr_ts   = request.headers.get("X-Timestamp", "")
    if not (hdr_auth and hdr_user and hdr_ts):
        return None, {"error": "Missing Authorization, X-User-Id or X-Timestamp headers"}

    try:
        ts = int(hdr_ts)
    except Exception:
        return None, {"error": "Invalid X-Timestamp"}

    # Replay window (5 minutes)
    if abs(int(time.time()) - ts) > 300:
        return None, {"error": "Timestamp outside allowable window"}

    # Extract expected base64 signature
    if not hdr_auth.startswith("HMAC "):
        return None, {"error": "Authorization must start with 'HMAC '"}
    provided_sig_b64 = hdr_auth.split(" ", 1)[1].strip()

    # Prefer per-user secret; else fall back to global
    key_id = hdr_key or os.getenv("SERVICE_API_KEY_ID", "global")
    secret = None

    # Per-user
    _kid, secret = _get_user_service_secret(hdr_user, hdr_key or None)

    # Global fallback
    if not secret:
        secret = os.getenv("SERVICE_API_SECRET")

    if not secret:
        return None, {"error": "No service API secret configured"}

    msg = f"{key_id}.{ts}.".encode("utf-8") + raw_body
    mac = hmac.new(secret.encode("utf-8"), msg, hashlib.sha256).digest()
    expected_sig_b64 = base64.b64encode(mac).decode("utf-8")

    # Constant-time compare
    if not hmac.compare_digest(provided_sig_b64, expected_sig_b64):
        return None, {"error": "Invalid signature"}

    # Success -> return user_id to use for token lookup
    return hdr_user, None


from db import get_profile
from routes.auth import login_required

bp = Blueprint("publish_api", __name__, url_prefix="/api/v1")

SUPPORTED_PLATFORMS = {"facebook", "instagram", "x", "linkedin", "youtube"}

def _join_text(text: str | None, hashtags: list[str] | None) -> str:
    text = (text or "").strip()
    if hashtags:
        tags = [t if t.startswith("#") else f"#{t}" for t in hashtags]
        text = (text + ("\n" if text else "") + " ".join(tags)).strip()
    return text

def _ok(data, status=200):
    return jsonify(data), status

def _bad(data, status=400):
    return jsonify(data), status

def _get_token(user_id: str, platform: str):
    """Pull platform token/profile from Mongo via existing helper."""
    prof = get_profile(user_id, platform)
    if not prof:
        return None, {"error": f"No connected {platform} profile for user {user_id}"}
    # Normalized view
    return {
        "platform": prof.get("platform"),
        "profile_id": str(prof.get("profile_id") or ""),
        "access_token": prof.get("access_token") or "",
        "refresh_token": prof.get("refresh_token") or "",
        "expires_at": prof.get("expires_at"),
        "raw_profile": prof.get("raw_profile") or {},
        "instagram_business_account_id": prof.get("instagram_business_account_id"),
        "linkedin_author_urn": prof.get("author_urn") or prof.get("linkedin_author_urn"),
    }, None

# ---------------------- Adapters ----------------------

def _post_facebook(page_id: str, access_token: str, message: str, link: str | None, media: list[dict] | None):
    base = "https://graph.facebook.com/v20.0"
    try:
        if media:
            m0 = media[0]
            if m0.get("type") == "image" and m0.get("url"):
                resp = requests.post(
                    f"{base}/{page_id}/photos",
                    data={"caption": message, "url": m0["url"], "access_token": access_token},
                    timeout=60
                )
                return resp.ok, resp.json()
            # video flow not implemented
            return False, {"error": "Facebook video not implemented"}
        payload = {"message": message, "access_token": access_token}
        if link:
            payload["link"] = link
        resp = requests.post(f"{base}/{page_id}/feed", data=payload, timeout=60)
        return resp.ok, resp.json()
    except Exception as e:
        logging.exception("FB post failed")
        return False, {"exception": str(e)}

def _post_instagram(ig_user_id: str, access_token: str, caption: str, media: list[dict] | None):
    base = "https://graph.facebook.com/v20.0"
    try:
        if not media or media[0].get("type") != "image":
            return False, {"error": "Instagram image_url required (video flow not implemented)"}
        image_url = media[0]["url"]
        r1 = requests.post(
            f"{base}/{ig_user_id}/media",
            data={"image_url": image_url, "caption": caption, "access_token": access_token},
            timeout=60
        )
        d1 = r1.json()
        if not (r1.ok and "id" in d1):
            return False, {"step": "media", "response": d1}
        creation_id = d1["id"]
        r2 = requests.post(
            f"{base}/{ig_user_id}/media_publish",
            data={"creation_id": creation_id, "access_token": access_token},
            timeout=60
        )
        return r2.ok, {"creation_id": creation_id, "publish": r2.json()}
    except Exception as e:
        logging.exception("IG post failed")
        return False, {"exception": str(e)}

def _post_x(bearer_token: str, text: str):
    try:
        if not text:
            return False, {"error": "Text required for X"}
        url = "https://api.x.com/2/tweets"
        resp = requests.post(url, json={"text": text},
                             headers={"Authorization": f"Bearer {bearer_token}", "Content-Type": "application/json"},
                             timeout=60)
        j = resp.json()
        ok = resp.ok and j.get("data", {}).get("id")
        return bool(ok), j
    except Exception as e:
        logging.exception("X post failed")
        return False, {"exception": str(e)}

def _post_linkedin(access_token: str, author_urn: str, text: str, link: str | None):
    try:
        endpoint = "https://api.linkedin.com/v2/ugcPosts"
        media_category = "NONE"
        media_payload = []
        if link:
            media_category = "ARTICLE"
            media_payload = [{
                "status": "READY",
                "description": {"text": text or ""},
                "originalUrl": link,
                "title": {"text": " "},
            }]
        body = {
            "author": author_urn,
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {"text": text or (" " if not link else "")},
                    "shareMediaCategory": media_category,
                    "media": media_payload
                }
            },
            "visibility": {"com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"}
        }
        resp = requests.post(endpoint, json=body,
                             headers={"Authorization": f"Bearer {access_token}", "X-Restli-Protocol-Version": "2.0.0"},
                             timeout=60)
        ok = resp.status_code in (201, 202)
        return ok, {"status_code": resp.status_code, "headers": dict(resp.headers), "body": resp.text}
    except Exception as e:
        logging.exception("LinkedIn post failed")
        return False, {"exception": str(e)}

# ---------------------- Endpoint ----------------------

@bp.route("/publish", methods=["POST"])
def publish():
    """Unified publish endpoint.
    Expected JSON:
    {
      "platforms": ["facebook","instagram","x","linkedin","youtube"],
      "content": {"text": "...", "link": "https://...", "hashtags": ["a","b"]},
      "media": [{"type": "image", "url": "https://..." }],
      "profile_ids": {"facebook": "<PAGE_ID>", "instagram": "<IG_BUSINESS_ID>", "linkedin_urn": "urn:li:organization:xxx"}
    }
    """
    raw = request.get_data(cache=True) or b''
    user_id, auth_err = _verify_service_signature(raw)
    if auth_err:
        return _bad({"ok": False, "auth": auth_err}, status=401)
    data = request.get_json(silent=True) or {}
    platforms = list(dict.fromkeys((data.get("platforms") or [])))  # de-dupe
    if not platforms or any(p not in SUPPORTED_PLATFORMS for p in platforms):
        return _bad({"ok": False, "error": "Invalid or missing 'platforms' array"})
    content = data.get("content") or {}
    text = _join_text(content.get("text"), content.get("hashtags"))
    link = content.get("link")
    media = data.get("media") or []
    profile_ids = data.get("profile_ids") or {}

    results = []
    overall_ok = True

    for p in platforms:
        token, err = _get_token(user_id, p)
        if err:
            results.append({"platform": p, "ok": False, "details": err})
            overall_ok = False
            continue

        try:
            if p == "facebook":
                page_id = profile_ids.get("facebook") or token["profile_id"]
                ok, details = _post_facebook(page_id, token["access_token"], text, link, media)

            elif p == "instagram":
                ig_id = profile_ids.get("instagram") or token.get("instagram_business_account_id") or token["profile_id"]
                ok, details = _post_instagram(ig_id, token["access_token"], text, media)

            elif p == "x":
                ok, details = _post_x(token["access_token"], text)

            elif p == "linkedin":
                author_urn = profile_ids.get("linkedin_urn") or token.get("linkedin_author_urn")
                if not author_urn:
                    # Try to infer person URN from profile_id
                    if token["profile_id"]:
                        author_urn = f"urn:li:person:{token['profile_id']}"
                if not author_urn:
                    results.append({"platform": p, "ok": False, "details": {"error": "Missing LinkedIn author URN"}})
                    overall_ok = False
                    continue
                ok, details = _post_linkedin(token["access_token"], author_urn, text, link)

            elif p == "youtube":
                ok, details = False, {"error": "YouTube upload not implemented in unified endpoint"}

            else:
                ok, details = False, {"error": f"Unsupported platform {p}"}

            results.append({"platform": p, "ok": ok, "details": details})
            if not ok:
                overall_ok = False

        except Exception as e:
            logging.exception(f"Error publishing to {p}")
            results.append({"platform": p, "ok": False, "details": {"exception": str(e)}})
            overall_ok = False

    status_code = 200 if overall_ok else 207  # 207 Multi-Status for partial failures
    return _ok({"ok": overall_ok, "results": results}, status=status_code)