import os
import requests
from flask import Blueprint, redirect, request, url_for, session, flash
from db import save_profile, get_user
from dotenv import load_dotenv
from bson import ObjectId
from routes.auth import login_required

load_dotenv()

bp = Blueprint("twitter", __name__, url_prefix='/twitter')

CLIENT_ID = os.getenv("TWITTER_CLIENT_ID")
CLIENT_SECRET = os.getenv("TWITTER_CLIENT_SECRET")
REDIRECT_URI = os.getenv("TWITTER_REDIRECT_URI")

SCOPES = "tweet.read tweet.write users.read offline.access"


@bp.route("/login")
@login_required
def login():
    import logging
    logging.info(f"Twitter login - Session before redirect: {dict(session)}")
    
    # Get user_id from session
    user_id = session.get("user_id", "")
    
    # Include user_id in the state parameter
    state = f"agenticoauth_{user_id}"
    
    auth_url = (
        "https://twitter.com/i/oauth2/authorize"
        f"?response_type=code"
        f"&client_id={CLIENT_ID}"
        f"&redirect_uri={REDIRECT_URI}"
        f"&scope={SCOPES.replace(' ', '%20')}"
        f"&state={state}"
        f"&code_challenge=agentic123"
        f"&code_challenge_method=plain"
    )
    return redirect(auth_url)


@bp.route("/callback")
def callback():
    try:
        code = request.args.get("code")
        state = request.args.get("state", "")
        if not code:
            return "Authorization code not found", 400
        import logging
        logging.info(f"Twitter callback - Session at start: {dict(session)}")
        user_id_from_state = None
        if state.startswith("agenticoauth_"):
            user_id_from_state = state.split("_")[1]
            logging.info(f"Extracted user_id from state: {user_id_from_state}")

        # Process OAuth token exchange
        token_url = "https://api.twitter.com/2/oauth2/token"
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": REDIRECT_URI,
            "client_id": CLIENT_ID,
            "code_verifier": "agentic123"
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        response = requests.post(token_url, data=token_data, headers=headers, auth=(CLIENT_ID, CLIENT_SECRET))
        response.raise_for_status()
        token_json = response.json()

        access_token = token_json.get("access_token")
        if not access_token:
            return f"Token exchange failed: {token_json}", 400

        # Get user data from Twitter
        user_resp = requests.get(
            "https://api.twitter.com/2/users/me",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        user_data = user_resp.json()

        # Create profile record
        profile_record = {
            "platform": "twitter",
            "profile_id": user_data.get("data", {}).get("id"),
            "username": user_data.get("data", {}).get("username"),
            "access_token": access_token,
            "refresh_token": token_json.get("refresh_token", ""),
            "expires_at": token_json.get("expires_in", 0),
            "raw_profile": user_data
        }
        
        # Check if user is logged in via session
        user_id = session.get("user_id")
        
        # If no user_id in session, try to use the one from state
        if not user_id and user_id_from_state:
            user_id = user_id_from_state
            logging.info(f"Using user_id from state parameter: {user_id}")
            session["user_id"] = user_id
        
        if not user_id:
            logging.error("Twitter callback - No user_id in session or state")
            flash("Please log in to connect your Twitter account")
            return redirect(url_for('auth.send_otp', next=request.url))
        
        logging.info(f"Twitter callback - Using user_id: {user_id} of type {type(user_id)}")
        
        # Convert user_id to ObjectId for MongoDB
        try:
            profile_record["user_id"] = ObjectId(user_id)
        except Exception as e:
            logging.error(f"Failed to convert user_id to ObjectId: {str(e)}")
            return "Invalid user ID", 400
        
        # Save the profile
        save_profile(profile_record)
        
        # Set a flash message for success
        flash("Twitter account connected successfully!")
        
        # Get CSRF token to include in the form
        csrf_token = session.get('csrf_token', '')
        
        # Return success message with a form that posts to connect route
        return f"""
        <div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">
            <h2 style="color: #1DA1F2;">✅ Twitter account connected successfully!</h2>
            <p>Your Twitter account has been connected to your profile.</p>
            <form action="{url_for('connect')}" method="get">
                <input type="hidden" name="csrf_token" value="{csrf_token}">
                <button type="submit" style="margin-top: 20px; padding: 10px 20px; background-color: #1DA1F2; color: white; border: none; border-radius: 5px; cursor: pointer;">Back to Connect Page</button>
            </form>
        </div>
        """
        
    except Exception as e:
        import logging
        logging.exception(f"Error in Twitter callback: {str(e)}")
        return f"An error occurred: {str(e)}", 500
