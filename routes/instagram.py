from flask import Blueprint, redirect, request, url_for, flash, session
from db import save_profile
from bson import ObjectId
from routes.auth import login_required
import os
import requests
from dotenv import load_dotenv

load_dotenv()

CLIENT_ID = os.getenv("INSTAGRAM_CLIENT_ID")
CLIENT_SECRET = os.getenv("INSTAGRAM_CLIENT_SECRET")
REDIRECT_URI = os.getenv("INSTAGRAM_REDIRECT_URI")
SCOPES = "user_profile,user_media"

bp = Blueprint("instagram", __name__, url_prefix='/instagram')

@bp.route("/login")
@login_required
def login():
    import logging
    logging.info(f"Instagram login - Session before redirect: {dict(session)}")
    user_id = session.get("user_id", "")
    state = f"agenticoauth_{user_id}"
    auth_url = (
        "https://api.instagram.com/oauth/authorize"
        f"?client_id={CLIENT_ID}"
        f"&redirect_uri={REDIRECT_URI}"
        f"&scope={SCOPES.replace(',', '%2C')}"
        f"&response_type=code"
        f"&state={state}"
    )
    return redirect(auth_url)

@bp.route("/callback")
def callback():
    try:
        code = request.args.get("code")
        state = request.args.get("state", "")
        if not code:
            return "Authorization code not found", 400
        import logging
        logging.info(f"Instagram callback - Session at start: {dict(session)}")
        user_id_from_state = None
        if state.startswith("agenticoauth_"):
            user_id_from_state = state.split("_")[1]
            logging.info(f"Extracted user_id from state: {user_id_from_state}")
        # Exchange code for access token
        token_url = "https://api.instagram.com/oauth/access_token"
        data = {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "grant_type": "authorization_code",
            "redirect_uri": REDIRECT_URI,
            "code": code
        }
        token_resp = requests.post(token_url, data=data)
        token_json = token_resp.json()
        access_token = token_json.get("access_token")
        if not access_token:
            return f"Token exchange failed: {token_json}", 400
        # Fetch user profile using Instagram Basic Display API
        profile_url = (
            f"https://graph.instagram.com/me"
            f"?fields=id,username&access_token={access_token}"
        )
        profile_resp = requests.get(profile_url)
        profile_data = profile_resp.json()
        profile_record = {
            "platform": "instagram",
            "profile_id": profile_data.get("id"),
            "username": profile_data.get("username"),
            "access_token": access_token,
            "refresh_token": "",
            "expires_at": token_json.get("expires_in", 0),
            "raw_profile": profile_data
        }
        user_id = session.get("user_id")
        if not user_id and user_id_from_state:
            user_id = user_id_from_state
            logging.info(f"Using user_id from state parameter: {user_id}")
            session["user_id"] = user_id
        if not user_id:
            logging.error("Instagram callback - No user_id in session or state")
            flash("Please log in to connect your Instagram account")
            return redirect(url_for('auth.send_otp', next=request.url))
        logging.info(f"Instagram callback - Using user_id: {user_id} of type {type(user_id)}")
        try:
            profile_record["user_id"] = ObjectId(user_id)
        except Exception as e:
            logging.error(f"Failed to convert user_id to ObjectId: {str(e)}")
            return "Invalid user ID", 400
        save_profile(profile_record)
        flash("Instagram account connected successfully!")
        csrf_token = session.get('csrf_token', '')
        logging.info(f"Instagram callback - Session before returning HTML: {dict(session)}")
        return f"""
        <div style=\"text-align: center; font-family: Arial, sans-serif; margin-top: 50px;\">
            <h2 style=\"color: #E1306C;\">✅ Instagram account connected successfully!</h2>
            <p>Your Instagram account has been connected to your profile.</p>
            <form action=\"{url_for('connect')}\" method=\"get\">
                <input type=\"hidden\" name=\"csrf_token\" value=\"{csrf_token}\">
                <button type=\"submit\" style=\"margin-top: 20px; padding: 10px 20px; background-color: #E1306C; color: white; border: none; border-radius: 5px; cursor: pointer;\">Back to Connect Page</button>
            </form>
        </div>
        """
    except Exception as e:
        import logging
        logging.exception(f"Error in Instagram callback: {str(e)}")
        return f"An error occurred: {str(e)}", 500
