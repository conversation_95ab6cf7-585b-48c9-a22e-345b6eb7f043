#!/usr/bin/env python3
"""
Test script to verify Twilio credentials and functionality
"""
import os
from twilio.rest import Client
from dotenv import load_dotenv

load_dotenv()

def test_twilio_credentials():
    """Test Twilio credentials and account status"""
    try:
        # Get credentials from environment
        account_sid = os.getenv("TWILIO_SID")
        auth_token = os.getenv("TWILIO_AUTH")
        from_phone = os.getenv("TWILIO_PHONE")
        
        print(f"Testing Twilio credentials...")
        print(f"Account SID: {account_sid}")
        print(f"From Phone: {from_phone}")
        
        # Initialize client
        client = Client(account_sid, auth_token)
        
        # Test 1: Get account info
        print("\n1. Testing account access...")
        account = client.api.accounts(account_sid).fetch()
        print(f"Account Status: {account.status}")
        print(f"Account Type: {account.type}")
        
        # Test 2: List phone numbers
        print("\n2. Testing phone numbers...")
        phone_numbers = client.incoming_phone_numbers.list(limit=5)
        print(f"Available phone numbers: {len(phone_numbers)}")
        for number in phone_numbers:
            print(f"  - {number.phone_number} ({number.capabilities})")
        
        # Test 3: Check balance (if available)
        try:
            balance = client.balance.fetch()
            print(f"\n3. Account Balance: {balance.balance} {balance.currency}")
        except Exception as e:
            print(f"\n3. Balance check failed: {e}")
        
        print("\n✅ Twilio credentials are valid!")
        return True
        
    except Exception as e:
        print(f"\n❌ Twilio test failed: {e}")
        return False

def test_sms_send(to_phone=None):
    """Test sending SMS (optional)"""
    if not to_phone:
        print("\nSkipping SMS test (no phone number provided)")
        return
        
    try:
        account_sid = os.getenv("TWILIO_SID")
        auth_token = os.getenv("TWILIO_AUTH")
        from_phone = os.getenv("TWILIO_PHONE")
        
        client = Client(account_sid, auth_token)
        
        print(f"\n4. Testing SMS send to {to_phone}...")
        message = client.messages.create(
            body="Test message from your app",
            from_=from_phone,
            to=to_phone
        )
        
        print(f"✅ SMS sent successfully! SID: {message.sid}")
        return True
        
    except Exception as e:
        print(f"❌ SMS test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Twilio Diagnostic Tool")
    print("=" * 40)
    
    # Test credentials
    if test_twilio_credentials():
        # Optionally test SMS (uncomment and add your phone number)
        # test_sms_send("+**********")  # Replace with your phone number
        pass
    
    print("\n" + "=" * 40)
    print("Diagnostic complete!")
