from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

load_dotenv()

client = MongoClient(os.getenv("MONGO_URI"))
db = client["ai_moderation"]

# Collections
users = db["users"]
social_profiles = db["user_social_profiles"]
otp_verification = db["otp_verification"]  # New collection for OTP verification

# Step 1. Save OTP in otp_verification collection
def save_otp(mobile, otp, type="login"):
    """
    Save OTP in otp_verification collection
    Returns the user_id if user exists, None otherwise
    """
    try:
        # Ensure mobile number is properly formatted (E.164 format)
        if not mobile.startswith('+'):
            mobile = '+' + mobile
            
        # Check if user exists to get chatId
        existing_user = users.find_one({"mobile": mobile})
        chat_id = existing_user.get("ChatId") if existing_user else None
        
        # Create new OTP record
        otp_record = {
            "phone": mobile,
            "otp": otp,
            "type": type,
            "is_used": False,
            "chatId": chat_id,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Insert OTP record
        otp_verification.insert_one(otp_record)
        
        print(f"OTP saved for mobile: {mobile}")
        return str(existing_user["_id"]) if existing_user else None
            
    except Exception as e:
        import logging
        logging.exception(f"Error in save_otp: {str(e)}")
        raise  # Re-raise the exception to be caught by the caller

# Step 2. Verify login and return user_id
def verify_login(mobile, otp):
    """
    Verify OTP and mark it as used
    Returns user_id if verification successful, None otherwise
    """
    try:
        # Ensure mobile number is properly formatted (E.164 format)
        if not mobile.startswith('+'):
            mobile = '+' + mobile
        
        # Find the OTP record
        otp_record = otp_verification.find_one({
            "phone": mobile, 
            "otp": otp, 
            "type": "login", 
            "is_used": False
        })
        
        if not otp_record:
            return None
        
        # Delete the OTP record instead of marking it as used
        otp_verification.delete_one({"_id": otp_record["_id"]})
        
        import logging
        logging.info(f"Deleted OTP record for mobile: {mobile}")
        
        # Get or create user
        user = users.find_one({"mobile": mobile})
        
        if user:
            # Update last login time
            update_data = {"last_login": datetime.utcnow()}
            
            # If chatId exists in OTP record and user doesn't have chatId,
            # update the user with the chatId from OTP record
            if otp_record.get("chatId") and not user.get("chatId"):
                update_data["chatId"] = otp_record["chatId"]
                logging.info(f"Updating user {user['_id']} with chatId from OTP record")
            
            users.update_one(
                {"_id": user["_id"]},
                {"$set": update_data}
            )
            return str(user["_id"])
        else:
            # Create new user if not exists
            new_user = {
                "mobile": mobile,
                "verified": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "last_login": datetime.utcnow()
            }
            
            # If chatId exists in OTP record, add it to user
            if otp_record.get("chatId"):
                new_user["chatId"] = otp_record["chatId"]
                logging.info(f"Setting chatId for new user from OTP record")
                
            result = users.insert_one(new_user)
            return str(result.inserted_id)
            
    except Exception as e:
        import logging
        logging.exception(f"Error in verify_login: {str(e)}")
        return None

# Step 3. Retrieve user (optional use)
def get_user(user_id):
    """Get user by ID"""
    try:
        # Convert string ID to ObjectId if needed
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        return users.find_one({"_id": user_id})
    except Exception as e:
        import logging
        logging.error(f"Error in get_user: {str(e)}")
        return None

# Step 4. Save OAuth profile with user_id binding
def save_profile(profile_data):
    """
    Save or update a social profile with user_id binding
    """
    import logging
    from bson import ObjectId
    from datetime import datetime, timedelta
    
    try:
        # Ensure user_id is a valid ObjectId
        if "user_id" in profile_data:
            if isinstance(profile_data["user_id"], str):
                profile_data["user_id"] = ObjectId(profile_data["user_id"])
        
        logging.info(f"Saving profile with user_id: {profile_data['user_id']} of type {type(profile_data['user_id'])}")
        
        # Create filter criteria for upsert
        filter_criteria = {
            "user_id": profile_data["user_id"],
            "platform": profile_data["platform"]
        }
        
        # Add timestamp
        profile_data["updated_at"] = datetime.utcnow()
        if "created_at" not in profile_data:
            profile_data["created_at"] = datetime.utcnow()
        
        # Perform the upsert
        result = social_profiles.update_one(
            filter_criteria,
            {"$set": profile_data},
            upsert=True
        )
        
        logging.info(f"Profile save result - matched: {result.matched_count}, modified: {result.modified_count}, upserted: {result.upserted_id}")
        return result
    except Exception as e:
        logging.exception(f"Error in save_profile: {str(e)}")
        raise

def get_profile(user_id, platform):
    """Get social profile for a user and platform"""
    try:
        # Convert string ID to ObjectId if needed
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)

        return social_profiles.find_one({
            "user_id": user_id,
            "platform": platform
        })
    except Exception as e:
        import logging
        logging.error(f"Error in get_profile: {str(e)}")
        return None

def update_token(profile_id, new_token, expires_at):
    """Update access token for a social profile"""
    social_profiles.update_one(
        {"_id": ObjectId(profile_id)},
        {"$set": {
            "access_token": new_token,
            "expires_at": expires_at,
            "updated_at": datetime.utcnow()
        }}
    )

def get_user_by_mobile(mobile):
    """Get user by mobile number"""
    try:
        # Ensure mobile number is properly formatted (E.164 format)
        if not mobile.startswith('+'):
            mobile = '+' + mobile
        return users.find_one({"mobile": mobile})
    except Exception as e:
        import logging
        logging.error(f"Error in get_user_by_mobile: {str(e)}")
        return None

def update_user_info(user_id, name=None, email=None):
    """Update user's name and email"""
    try:
        # Convert string ID to ObjectId if needed
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        
        # Prepare update data
        update_data = {"updated_at": datetime.utcnow()}
        if name:
            update_data["name"] = name
        if email:
            update_data["email"] = email
        
        # Update user document
        result = users.update_one(
            {"_id": user_id},
            {"$set": update_data}
        )
        
        import logging
        if result.modified_count > 0:
            logging.info(f"Updated user info for user {user_id}")
        else:
            logging.warning(f"No changes made to user info for user {user_id}")
        
        return result
    except Exception as e:
        import logging
        logging.exception(f"Error in update_user_info: {str(e)}")
        return None

def update_telegram_chat_id(user_id, telegram_chat_id):
    """
    Update user's Telegram chat ID
    
    Args:
        user_id: User ID (string or ObjectId)
        telegram_chat_id: Telegram chat ID
    
    Returns:
        Result of the update operation
    """
    try:
        # Convert user_id to ObjectId if it's a string
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        
        # Update user document - use the same field name as in the database (chatId)
        result = users.update_one(
            {"_id": user_id},
            {"$set": {"chatId": telegram_chat_id, "updated_at": datetime.utcnow()}}
        )
        
        import logging
        if result.modified_count > 0:
            logging.info(f"Updated chatId for user {user_id}")
        else:
            logging.warning(f"Failed to update chatId for user {user_id}")
        
        return result
    except Exception as e:
        import logging
        logging.exception(f"Error in update_telegram_chat_id: {str(e)}")
        return None

def update_chat_state(chat_id, state):
    """
    Update the state in chat_state collection
    
    Args:
        chat_id: The chat ID to find the document
        state: The new state value
        
    Returns:
        True if successful, False otherwise
    """
    try:
        import logging
        logging.info(f"Attempting to update chat state to '{state}' for chatId: {chat_id}")
        
        # Get chat_state collection
        chat_state = db["chat_state"]
        
        # First check if a document with this chatId already exists
        existing_doc = chat_state.find_one({"chatId": chat_id})
        
        if existing_doc:
            # Update existing document
            result = chat_state.update_one(
                {"chatId": chat_id},
                {
                    "$set": {
                        "state": state,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            if result.modified_count > 0:
                logging.info(f"Updated existing chat state to '{state}' for chatId: {chat_id}")
                return True
            else:
                logging.warning(f"No changes made to existing chat state for chatId: {chat_id}")
                return False
        else:
            # Insert new document
            result = chat_state.insert_one({
                "chatId": chat_id,
                "state": state,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            if result.inserted_id:
                logging.info(f"Created new chat state with state '{state}' for chatId: {chat_id}")
                return True
            else:
                logging.warning(f"Failed to create new chat state for chatId: {chat_id}")
                return False
            
    except Exception as e:
        logging.exception(f"Error updating chat state: {str(e)}")
        return False

# === Service API Key provisioning (per-user HMAC) ===
import secrets

def ensure_service_api(user_id, key_id: str = "n8n-default"):
    """
    Ensure a per-user service_api secret exists for HMAC auth.
    If missing, create { service_api: { key_id, secret } }.
    Returns a dict: {created: bool, key_id: str, secret: str|None}
    (secret only returned when created now; we don't re-read or return plaintext secrets otherwise)
    """
    try:
        # Normalize ObjectId
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)

        doc = users.find_one({"_id": user_id}, {"service_api": 1})
        sa = (doc or {}).get("service_api") if doc else None

        # If already present and has a secret or keys array, do nothing
        if isinstance(sa, dict) and (sa.get("secret") or (sa.get("keys") and len(sa.get("keys")) > 0)):
            # try to return an existing key_id if present
            existing_key = sa.get("key_id") or (sa.get("keys")[0]["key_id"] if sa.get("keys") else key_id)
            return {"created": False, "key_id": existing_key, "secret": None}

        # Create a new secret
        secret = secrets.token_urlsafe(32)  # ~43 chars
        users.update_one(
            {"_id": user_id},
            {"$set": {"service_api": {"key_id": key_id, "secret": secret, "created_at": datetime.utcnow(), "last_rotated_at": datetime.utcnow()}}},
            upsert=False
        )
        return {"created": True, "key_id": key_id, "secret": secret}
    except Exception as e:
        import logging
        logging.exception(f"ensure_service_api error: {e}")
        return {"created": False, "key_id": key_id, "secret": None}

def rotate_service_api(user_id, key_id: str = "n8n-default", keep_old_days: int = 1):
    """
    Rotate a user's service_api secret.
    - Generates a new secret for the given key_id (default: n8n-default)
    - Moves the old secret into service_api.keys[] with expires_at for grace period
    - Updates service_api.secret and last_rotated_at
    Returns {rotated: bool, key_id, new_secret}
    """
    try:
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        doc = users.find_one({"_id": user_id}, {"service_api": 1})
        if not doc or not doc.get("service_api") or not doc["service_api"].get("secret"):
            # nothing to rotate; create fresh instead
            res = ensure_service_api(user_id, key_id=key_id)
            return {"rotated": res.get("created", False), "key_id": key_id, "new_secret": res.get("secret")}
        sa = doc["service_api"]
        old = {"key_id": sa.get("key_id", key_id), "secret": sa.get("secret"), "created_at": sa.get("created_at", datetime.utcnow())}
        # set expiry for old key
        expires_at = datetime.utcnow() + timedelta(days=keep_old_days)
        old["expires_at"] = expires_at
        # generate new
        import secrets as _sec
        new_secret = _sec.token_urlsafe(32)
        # push old into keys[], set new as current
        users.update_one(
            {"_id": user_id},
            {
                "$push": {"service_api.keys": old},
                "$set": {"service_api.key_id": key_id, "service_api.secret": new_secret, "service_api.last_rotated_at": datetime.utcnow()}
            }
        )
        return {"rotated": True, "key_id": key_id, "new_secret": new_secret}
    except Exception as e:
        import logging
        logging.exception(f"rotate_service_api error: {e}")
        return {"rotated": False, "key_id": key_id, "new_secret": None}

def users_due_for_rotation(days: int = 90, limit: int = 1000):
    """Yield user ids whose service_api.last_rotated_at is older than <days>."""
    cutoff = datetime.utcnow() - timedelta(days=days)
    cur = users.find({"service_api.last_rotated_at": {"$lt": cutoff}}, {"_id": 1}).limit(int(limit))
    for d in cur:
        yield str(d["_id"])