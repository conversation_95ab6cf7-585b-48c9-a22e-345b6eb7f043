<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Subscription Plans</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f9f9f9;
      padding: 40px;
    }
    h1 {
      text-align: center;
      color: #333;
    }
    table {
      width: 100%;
      max-width: 800px;
      margin: 30px auto;
      border-collapse: collapse;
      background: #fff;
      box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }
    th, td {
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      text-align: left;
    }
    th {
      background-color: #f1f1f1;
      color: #555;
    }
    tr:hover {
      background-color: #f9f9f9;
    }
    .emoji {
      font-size: 1.2em;
    }
    .btn-subscribe {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 8px 16px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .btn-subscribed {
      background-color: #cccccc;
      color: #666666;
      border: none;
      padding: 8px 16px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      margin: 4px 2px;
      cursor: not-allowed;
      border-radius: 4px;
    }
    .message {
      text-align: center;
      padding: 10px;
      margin: 20px auto;
      max-width: 800px;
      border-radius: 4px;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
      border: 1px solid #d6e9c6;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
      border: 1px solid #ebccd1;
    }
    .info {
      background-color: #d9edf7;
      color: #31708f;
      border: 1px solid #bce8f1;
    }
  </style>
</head>
<body>

  <h1>💼 Subscription Plans (Monthly)</h1>
  
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="message {{ category }}">
          {{ message }}
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <table>
    <thead>
      <tr>
        <th>Plan Name</th>
        <th>Price (USD)</th>
        <th>Included Tokens</th>
        <th>Effective Token Price</th>
        <th>Target Audience</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Starter</td>
        <td>$9.99</td>
        <td>250</td>
        <td>$0.04</td>
        <td>Small teams, individual use</td>
        <td>
          {% if user_subscribed %}
            <button class="btn-subscribed" disabled>Subscribed</button>
          {% else %}
            <form method="POST" action="{{ url_for('subscription.subscribe_plan') }}">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
              <input type="hidden" name="plan_name" value="Starter">
              <input type="hidden" name="plan_price" value="9.99">
              <input type="hidden" name="tokens" value="250">
              <button type="submit" class="btn-subscribe">Subscribe</button>
            </form>
          {% endif %}
        </td>
      </tr>
      <tr>
        <td>Pro</td>
        <td>$24.99</td>
        <td>1,000</td>
        <td>$0.025</td>
        <td>Growing apps, moderate usage</td>
        <td>
          {% if user_subscribed %}
            <button class="btn-subscribed" disabled>Subscribed</button>
          {% else %}
            <form method="POST" action="{{ url_for('subscription.subscribe_plan') }}">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
              <input type="hidden" name="plan_name" value="Pro">
              <input type="hidden" name="plan_price" value="24.99">
              <input type="hidden" name="tokens" value="1000">
              <button type="submit" class="btn-subscribe">Subscribe</button>
            </form>
          {% endif %}
        </td>
      </tr>
      <tr>
        <td>Business</td>
        <td>$69.99</td>
        <td>3,000</td>
        <td>$0.023</td>
        <td>SMBs, frequent content review</td>
        <td>
          {% if user_subscribed %}
            <button class="btn-subscribed" disabled>Subscribed</button>
          {% else %}
            <form method="POST" action="{{ url_for('subscription.subscribe_plan') }}">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
              <input type="hidden" name="plan_name" value="Business">
              <input type="hidden" name="plan_price" value="69.99">
              <input type="hidden" name="tokens" value="3000">
              <button type="submit" class="btn-subscribe">Subscribe</button>
            </form>
          {% endif %}
        </td>
      </tr>
      <tr>
        <td>Enterprise</td>
        <td>$199.99</td>
        <td>10,000</td>
        <td>$0.02</td>
        <td>Large-scale platforms</td>
        <td>
          {% if user_subscribed %}
            <button class="btn-subscribed" disabled>Subscribed</button>
          {% else %}
            <form method="POST" action="{{ url_for('subscription.subscribe_plan') }}">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
              <input type="hidden" name="plan_name" value="Enterprise">
              <input type="hidden" name="plan_price" value="199.99">
              <input type="hidden" name="tokens" value="10000">
              <button type="submit" class="btn-subscribe">Subscribe</button>
            </form>
          {% endif %}
        </td>
      </tr>
    </tbody>
  </table>

  <div style="text-align: center; margin-top: 20px;">
    <a href="{{ url_for('connect') }}">Connect Social Media Accounts</a> | 
    <a href="{{ url_for('auth.logout') }}">Logout</a>
  </div>

</body>
</html>
