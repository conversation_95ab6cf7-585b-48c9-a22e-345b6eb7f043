2025-06-19 16:51:29,145 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 16:51:29,145 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 16:51:29,146 - werkzeug - INFO -  * Restarting with stat
2025-06-19 16:51:29,495 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 16:51:29,504 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 16:51:47,161 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:51:47] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-19 16:51:47,290 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:51:47] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-19 16:51:47,292 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:51:47] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-19 16:51:47,633 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:51:47] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=2BvJBrk7AgyXYPM2Cuex HTTP/1.1" 200 -
2025-06-19 16:51:48,468 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:51:48] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-19 16:53:23,275 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 16:53:23,617 - werkzeug - INFO -  * Restarting with stat
2025-06-19 16:53:24,003 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 16:53:24,006 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 16:53:37,831 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:53:37] "GET / HTTP/1.1" 200 -
2025-06-19 16:53:43,446 - flask_wtf.csrf - INFO - The CSRF token is missing.
2025-06-19 16:53:43,447 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:53:43] "[31m[1mPOST /auth/send-otp HTTP/1.1[0m" 400 -
2025-06-19 16:56:24,654 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 16:56:24,654 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 16:56:24,655 - werkzeug - INFO -  * Restarting with stat
2025-06-19 16:56:24,995 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 16:56:24,998 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 16:56:30,958 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:56:30] "GET / HTTP/1.1" 200 -
2025-06-19 16:56:34,563 - flask_wtf.csrf - INFO - The CSRF token is missing.
2025-06-19 16:56:34,564 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:56:34] "[31m[1mPOST /auth/send-otp HTTP/1.1[0m" 400 -
2025-06-19 16:56:50,938 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 16:56:50,938 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 16:56:50,939 - werkzeug - INFO -  * Restarting with stat
2025-06-19 16:56:51,273 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 16:56:51,276 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 16:56:53,974 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:56:53] "GET / HTTP/1.1" 200 -
2025-06-19 16:57:01,132 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 16:57:01,132 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 16:57:01,132 - twilio.http_client - INFO - Headers:
2025-06-19 16:57:01,132 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 16:57:01,133 - twilio.http_client - INFO - Accept : application/json
2025-06-19 16:57:01,133 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 16:57:01,133 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 16:57:01,133 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 16:57:01,133 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 16:57:02,659 - twilio.http_client - INFO - Response Status Code: 400
2025-06-19 16:57:02,659 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json', 'Content-Length': '132', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 11:27:02 GMT', 'X-Twilio-Error-Code': '21211', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQb11bcdc6a278b4336c6f63a29ddf72b8', 'Twilio-Request-Duration': '0.081', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Error from cloudfront', 'Via': '1.1 c13661f9455806125a56201b0ccd2b96.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'MlCin6ZF7b1tmi7PqaIiGrtIDL8mRqhwxBiuYUGCWOxbL5vmlsXkIw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 16:57:02,661 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:57:02] "[35m[1mPOST /auth/send-otp HTTP/1.1[0m" 500 -
2025-06-19 16:59:17,927 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:59:17] "GET / HTTP/1.1" 200 -
2025-06-19 16:59:25,720 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 16:59:25,720 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 16:59:25,720 - twilio.http_client - INFO - Headers:
2025-06-19 16:59:25,720 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 16:59:25,720 - twilio.http_client - INFO - Accept : application/json
2025-06-19 16:59:25,721 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 16:59:25,721 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 16:59:25,721 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 16:59:25,721 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 16:59:26,727 - twilio.http_client - INFO - Response Status Code: 400
2025-06-19 16:59:26,728 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json', 'Content-Length': '200', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 11:29:26 GMT', 'X-Twilio-Error-Code': '21408', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQf90ce9c6a2c8f0b057b201dc2a1c7feb', 'Twilio-Request-Duration': '0.074', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Error from cloudfront', 'Via': '1.1 0587d29bba1a416012edbf0b64a7e34c.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '0SoAoyrk6igDrx1MdpUjH_gLdzm8oIuOEeAJmXS1AP03qmZFqLWalQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 16:59:26,729 - werkzeug - INFO - 1******** - - [19/Jun/2025 16:59:26] "[35m[1mPOST /auth/send-otp HTTP/1.1[0m" 500 -
2025-06-19 17:04:05,222 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:04:05,222 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:04:05,223 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:04:05,548 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:04:05,557 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:04:07,536 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:07] "GET / HTTP/1.1" 200 -
2025-06-19 17:04:09,048 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:09] "GET / HTTP/1.1" 200 -
2025-06-19 17:04:13,750 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:04:13,750 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:04:13,750 - twilio.http_client - INFO - Headers:
2025-06-19 17:04:13,751 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:04:13,751 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:04:13,751 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:04:13,751 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:04:13,751 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:04:13,751 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:04:14,305 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:04:14,305 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 11:34:14 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ24ad9e3bcf1c7df7e004b639d0088513', 'Twilio-Request-Duration': '0.168', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 311b540b2896b95f3dd13864b05aa3fa.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'XXaF52R8pUSHs4Cq1r2yd9PASdxAC5weDWIZ9KjamBCNd85RNg97ig==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:04:14,310 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:14] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:04:35,864 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:35] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:04:35,878 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:35] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:04:41,621 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:41] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:04:47,740 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:04:47] "[31m[1mGET /twitter/callback?state=agenticoauth&code=bzhIYjlpTEpodmxGNFQ5VTFDaTB2ZjhzYVl6TXFrZ2x5dmgxcVJqMXgyZUV0OjE3NTAzMzI4ODQ3ODk6MToxOmFjOjE HTTP/1.1[0m" 403 -
2025-06-19 17:07:01,956 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:07:01] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:07:04,417 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:07:04] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:07:13,806 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:07:13] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-19 17:07:13,821 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:07:13] "GET / HTTP/1.1" 200 -
2025-06-19 17:14:14,460 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:14:14,955 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:14:15,499 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:14:15,506 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:15:05,865 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:15:06,393 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:15:06,796 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:15:06,799 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:15:27,851 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:15:27] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:15:36,103 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:15:36,103 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:15:36,103 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:15:36,459 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:15:36,462 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:15:38,090 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:15:38] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:15:40,429 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:15:40] "GET / HTTP/1.1" 200 -
2025-06-19 17:16:01,051 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:16:01,051 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:16:01,051 - twilio.http_client - INFO - Headers:
2025-06-19 17:16:01,051 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:16:01,051 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:16:01,051 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:16:01,051 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:16:01,051 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:16:01,051 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:16:02,547 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:16:02,547 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 11:46:02 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ6c0a56eddddd2534bf98d7e44b8b73b1', 'Twilio-Request-Duration': '0.141', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 3ed047b9445f29bd769c4848cba712a0.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '09bO3TpO_JNpK60z2OBeEuVPQZOCjsFNWDVR0yJF-qD2fMDip3LXHQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:16:02,553 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:16:02] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:16:15,926 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:16:15] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:16:15,937 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:16:15] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:16:19,456 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:16:19] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:16:25,600 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:16:25] "[31m[1mGET /twitter/callback?state=agenticoauth&code=aV9mU1FkR0lNWDRhcnp4ZG1DSnl2VWlXV1ItZ1pQZ050RE1VVHhjMS1YbEU2OjE3NTAzMzM1ODI1OTc6MToxOmFjOjE HTTP/1.1[0m" 403 -
2025-06-19 17:19:43,536 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:19:44,183 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:19:44,800 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:19:44,806 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:21:37,633 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-19 17:21:38,174 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:21:38,618 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:21:38,621 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:21:47,688 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-19 17:21:48,160 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:21:48,609 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:21:48,614 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:21:56,844 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:21:56,844 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:21:56,845 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:21:57,192 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:21:57,195 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:22:03,250 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:03] "GET / HTTP/1.1" 200 -
2025-06-19 17:22:10,968 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:22:10,968 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:22:10,969 - twilio.http_client - INFO - Headers:
2025-06-19 17:22:10,969 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:22:10,969 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:22:10,969 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:22:10,969 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:22:10,969 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:22:10,969 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:22:12,214 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:22:12,214 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 11:52:12 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQb52aa9e15b35090f7b0b95a8fa8c66f3', 'Twilio-Request-Duration': '0.136', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c2e71f7a744af65a5cea344ff07d9628.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'm3LN0rWb1x7_jM4trOt5hkVGNJ1lW15sN_66BQA6lSVwLs5CFYejbg==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:22:12,220 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:12] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:22:25,936 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:25] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:22:25,946 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:25] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:22:27,854 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:27] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:22:36,486 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:22:36] "[31m[1mGET /twitter/callback?state=agenticoauth&code=czlOLWJUdlF5YTFRYUE1UmRLWnRwSWw3eHdvSEZTcFJwbXhvSGNUV0toeDQtOjE3NTAzMzM5NTMxNTg6MToxOmFjOjE HTTP/1.1[0m" 403 -
2025-06-19 17:22:49,657 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:22:50,087 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:22:50,521 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:22:50,523 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:22:54,563 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:22:55,108 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:22:55,618 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:22:55,623 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:22:56,645 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-19 17:22:57,225 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:22:57,621 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:22:57,624 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:23:15,502 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:15] "[35m[1mGET /twitter/callback?state=agenticoauth&code=czlOLWJUdlF5YTFRYUE1UmRLWnRwSWw3eHdvSEZTcFJwbXhvSGNUV0toeDQtOjE3NTAzMzM5NTMxNTg6MToxOmFjOjE HTTP/1.1[0m" 500 -
2025-06-19 17:23:20,386 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:20] "[35m[1mGET /twitter/callback?state=agenticoauth&code=czlOLWJUdlF5YTFRYUE1UmRLWnRwSWw3eHdvSEZTcFJwbXhvSGNUV0toeDQtOjE3NTAzMzM5NTMxNTg6MToxOmFjOjE HTTP/1.1[0m" 500 -
2025-06-19 17:23:24,135 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:24] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:23:27,008 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:27] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:23:29,040 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:29] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:23:34,541 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:23:34] "[31m[1mGET /twitter/callback?state=agenticoauth&code=R1FuLTBiclBRT1lEUG5UWm1FMy1UTWFNXzBnY19zYkNkcl9QMUNqMVhtMi14OjE3NTAzMzQwMTE4Njc6MToxOmFjOjE HTTP/1.1[0m" 403 -
2025-06-19 17:24:59,465 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:24:59,971 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:00,367 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:00,370 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:25:08,435 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:25:08,929 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:09,301 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:09,305 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:25:11,333 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:25:11,899 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:12,350 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:12,353 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:25:24,446 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 17:25:24,936 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:25,388 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:25,391 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:25:53,601 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 17:25:54,109 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:54,581 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:54,584 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:25:55,613 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:25:56,114 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:25:56,554 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:25:56,557 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:26:18,732 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:26:19,169 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:26:19,602 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:26:19,605 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:26:31,697 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:26:32,148 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:26:32,684 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:26:32,688 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:26:33,703 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:26:34,250 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:26:34,695 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:26:34,698 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:28:55,660 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:28:56,088 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:28:56,556 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:28:56,559 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:07,647 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:29:08,103 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:29:08,550 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:29:08,553 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:23,674 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:29:24,085 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:29:24,455 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:29:24,457 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:26,482 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:29:27,043 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:29:27,524 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:29:27,526 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:45,665 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:29:46,047 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:29:46,438 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:29:46,441 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:56,528 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:29:57,055 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:29:57,548 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:29:57,551 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:29:59,577 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 17:30:00,128 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:30:00,503 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:30:00,508 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:30:17,363 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:30:17,363 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:30:17,364 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:30:17,738 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:30:17,748 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:30:23,708 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:30:23] "GET / HTTP/1.1" 200 -
2025-06-19 17:30:38,826 - root - INFO - User status for +************: Existing user
2025-06-19 17:30:39,128 - root - INFO - Upsert result for +************: None
2025-06-19 17:30:39,257 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:30:39,257 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:30:39,257 - twilio.http_client - INFO - Headers:
2025-06-19 17:30:39,257 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:30:39,258 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:30:39,258 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:30:39,258 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:30:39,258 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:30:39,258 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:30:40,204 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:30:40,204 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:00:40 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQae806cb6c08cbafcf1853e455500022a', 'Twilio-Request-Duration': '0.141', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 e5ecb7c664172229c759168f606959ba.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'PF042184fW1Vq0oVKaGDTfCNfSq_IovzSXQ1eRAlAIeE40brTJay3Q==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:30:40,211 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:30:40] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:31:07,642 - root - ERROR - Invalid OTP for mobile: +************
2025-06-19 17:31:07,643 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:31:07] "[31m[1mPOST /auth/verify-otp HTTP/1.1[0m" 401 -
2025-06-19 17:31:26,071 - root - INFO - User status for +************: Existing user
2025-06-19 17:31:26,417 - root - INFO - Upsert result for +************: None
2025-06-19 17:31:26,417 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:31:26,418 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:31:26,418 - twilio.http_client - INFO - Headers:
2025-06-19 17:31:26,418 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:31:26,418 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:31:26,418 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:31:26,418 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:31:26,419 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:31:26,419 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:31:27,129 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:31:27,129 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:01:26 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQe302b8c6d8bbf0c1de55001d33c2b964', 'Twilio-Request-Duration': '0.119', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 6504d64256258122cb818ccf9402cf1a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'w9RVYJO1o5ViC4psnn-IXpebSM_hArPvgl4ydVYzxysaqNNRAlRjCg==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:31:27,131 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:31:27] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:31:41,941 - root - INFO - User logged in - User ID: 6853f49572410714215b14e5
2025-06-19 17:31:41,941 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:31:41,943 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:31:41] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:31:41,955 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:31:41] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:32:25,929 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:32:25] "[33mGET /logout HTTP/1.1[0m" 404 -
2025-06-19 17:32:47,183 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:32:47] "[33mGET /logout HTTP/1.1[0m" 404 -
2025-06-19 17:32:51,306 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:32:51] "GET / HTTP/1.1" 200 -
2025-06-19 17:32:58,942 - root - INFO - User status for +************: Existing user
2025-06-19 17:32:59,244 - root - INFO - Upsert result for +************: None
2025-06-19 17:32:59,244 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:32:59,245 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:32:59,245 - twilio.http_client - INFO - Headers:
2025-06-19 17:32:59,245 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:32:59,245 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:32:59,245 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:32:59,245 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:32:59,245 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:32:59,245 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:33:00,376 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:33:00,377 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:03:00 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQe1f1c24c2c977f4835ddef987c27b14a', 'Twilio-Request-Duration': '0.120', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 bec9deaed9db0375ab5f43d39e7c94ca.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'eQpBDaNpBQPuMZs4Ujc5Yew4yuFo10samjo3VpfuOiQZKGi7Vrz7Ag==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:33:00,378 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:33:00] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:33:18,495 - root - INFO - User logged in - User ID: 6853f49572410714215b14e5
2025-06-19 17:33:18,495 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:33:18,496 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:33:18] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:33:18,506 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:33:18] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:33:47,079 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:33:47,079 - root - INFO - login_required - User is logged in with ID: 6853f49572410714215b14e5
2025-06-19 17:33:47,080 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:33:47,081 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:33:47] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:34:01,866 - root - ERROR - Twitter callback - No user_id in session
2025-06-19 17:34:01,868 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:34:01] "[32mGET /twitter/callback?state=agenticoauth&code=UWJHWmRRUEVENDJhc05qTmJkVzNfaW1FbTJrMmlxdVNvX0c3Uzc0TWxiNVpfOjE3NTAzMzQ2MzIzOTM6MToxOmFjOjE HTTP/1.1[0m" 302 -
2025-06-19 17:34:01,875 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:34:01] "GET /auth/send-otp?next=http://localhost:5000/twitter/callback?state%3Dagenticoauth%26code%3DUWJHWmRRUEVENDJhc05qTmJkVzNfaW1FbTJrMmlxdVNvX0c3Uzc0TWxiNVpfOjE3NTAzMzQ2MzIzOTM6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 17:37:27,398 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:37:28,008 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:37:28,806 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:37:28,811 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:37:43,948 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:37:44,325 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:37:45,189 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:37:45,193 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:37:57,300 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:37:57,884 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:37:58,714 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:37:58,718 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:38:07,681 - root - INFO - User status for +************: Existing user
2025-06-19 17:38:08,208 - root - INFO - User ID after upsert for +************: 6853f49572410714215b14e5
2025-06-19 17:38:08,321 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:38:08,321 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:38:08,322 - twilio.http_client - INFO - Headers:
2025-06-19 17:38:08,322 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:38:08,322 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:38:08,322 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:38:08,322 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:38:08,323 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:38:08,323 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:38:09,573 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:38:09,573 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:08:09 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ3a0c210b7aa344ff02846a02643f73de', 'Twilio-Request-Duration': '0.139', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 6504d64256258122cb818ccf9402cf1a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'IuNJXhJH_toKU2gVujJVxMioaquv5vfjbOxhksnuZBinrpkhnp_YVA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:38:09,573 - root - INFO - OTP sent to +************, Twilio SID: SM3a0c210b7aa344ff02846a02643f73de
2025-06-19 17:38:09,582 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:38:09] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:38:24,950 - root - INFO - User logged in - User ID: 6853f49572410714215b14e5
2025-06-19 17:38:24,950 - root - INFO - Session after login: {'csrf_token': '20ab119b11324946ce2e028c2a7d96788fc6c356', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5', '_permanent': True}
2025-06-19 17:38:24,952 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:38:24] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:38:24,963 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:38:24] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:38:27,938 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '20ab119b11324946ce2e028c2a7d96788fc6c356', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:38:27,939 - root - INFO - login_required - User is logged in with ID: 6853f49572410714215b14e5
2025-06-19 17:38:27,939 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '20ab119b11324946ce2e028c2a7d96788fc6c356', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:38:27,940 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:38:27] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:38:36,726 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:38:36] "GET /twitter/callback?state=agenticoauth&code=RHU0aGdFMkdnTGNGYS00ZXlZcWNTUms3c0pXUEdMR2RBYlh3T280d1JyY09wOjE3NTAzMzQ5MTMxMTE6MTowOmFjOjE HTTP/1.1" 200 -
2025-06-19 17:50:45,066 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 17:50:45,550 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:50:46,327 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:50:46,332 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:53:11,391 - root - ERROR - Error in Twitter callback: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py", line 61, in callback
    response.raise_for_status()  # Raise exception for HTTP errors
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/agentic_oauth_codebase/venv/lib/python3.12/site-packages/requests/models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
2025-06-19 17:53:11,394 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:53:11] "[35m[1mGET /twitter/callback?state=agenticoauth&code=RHU0aGdFMkdnTGNGYS00ZXlZcWNTUms3c0pXUEdMR2RBYlh3T280d1JyY09wOjE3NTAzMzQ5MTMxMTE6MTowOmFjOjE HTTP/1.1[0m" 500 -
2025-06-19 17:53:20,821 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:53:20,821 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:53:20,822 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:53:21,520 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:53:21,524 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:53:25,136 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:53:25] "GET / HTTP/1.1" 200 -
2025-06-19 17:53:32,721 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:53:32,722 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:53:32,722 - twilio.http_client - INFO - Headers:
2025-06-19 17:53:32,722 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:53:32,722 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:53:32,722 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:53:32,723 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:53:32,723 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:53:32,723 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:53:34,122 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:53:34,122 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:23:34 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQe57144c219aad1b49077ca55596468aa', 'Twilio-Request-Duration': '0.142', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 0587d29bba1a416012edbf0b64a7e34c.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'zIIP46IWWaCMl463QMjEF1bQ_KaTzhVO4yLeGl8BzRFuseb1h40Ctw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:53:34,127 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:53:34] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:53:46,639 - root - INFO - User logged in - User ID: 6853f49572410714215b14e5
2025-06-19 17:53:46,639 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6853f49572410714215b14e5'}
2025-06-19 17:53:46,641 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:53:46] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:53:46,651 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:53:46] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:57:24,697 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 17:57:25,259 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:57:26,087 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:57:26,092 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:57:35,729 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 17:57:35,730 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 17:57:35,731 - werkzeug - INFO -  * Restarting with stat
2025-06-19 17:57:36,480 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 17:57:36,485 - werkzeug - INFO -  * Debugger PIN: 124-************-06-19 17:57:41,886 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:57:41] "GET / HTTP/1.1" 200 -
2025-06-19 17:59:15,358 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 17:59:15,359 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 17:59:15,359 - twilio.http_client - INFO - Headers:
2025-06-19 17:59:15,359 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 17:59:15,359 - twilio.http_client - INFO - Accept : application/json
2025-06-19 17:59:15,359 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 17:59:15,360 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 17:59:15,360 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 17:59:15,360 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 17:59:16,237 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 17:59:16,238 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:29:16 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQf3c0e0eec36982f25ec4a8c96b448163', 'Twilio-Request-Duration': '0.136', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 e9c3f34c843eb4099f41b5b2a942220e.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'ywuhEc_WeA4olyXahxOTaoNgOXgHJh7H7_jz5Z4I8DeiLC-EwpHu2A==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 17:59:16,247 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:59:16] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 17:59:41,407 - root - INFO - User logged in - User ID: 6854029a03fe25a0161e3bda
2025-06-19 17:59:41,408 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 17:59:41,409 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:59:41] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 17:59:41,420 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:59:41] "GET /connect HTTP/1.1" 200 -
2025-06-19 17:59:47,200 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 17:59:47,200 - root - INFO - login_required - User is logged in with ID: 6854029a03fe25a0161e3bda
2025-06-19 17:59:47,200 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 17:59:47,202 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:59:47] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 17:59:55,861 - werkzeug - INFO - 1******** - - [19/Jun/2025 17:59:55] "GET /twitter/callback?state=agenticoauth&code=c3VYX2R6UlRXSTB6TWV2elczQmJKQ1ZZeWlhdjFaQjBoSFZfdy1oQWY3MGZMOjE3NTAzMzYxOTI0Nzc6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 18:20:37,482 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 18:20:37,483 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 18:20:37,484 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:20:37,793 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:20:37,801 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:20:40,842 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:20:40] "GET / HTTP/1.1" 200 -
2025-06-19 18:20:41,889 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:20:41] "GET / HTTP/1.1" 200 -
2025-06-19 18:20:42,150 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:20:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-19 18:21:14,038 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 18:21:14,038 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 18:21:14,038 - twilio.http_client - INFO - Headers:
2025-06-19 18:21:14,038 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 18:21:14,038 - twilio.http_client - INFO - Accept : application/json
2025-06-19 18:21:14,038 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 18:21:14,039 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 18:21:14,039 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 18:21:14,039 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 18:21:14,615 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 18:21:14,616 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:51:14 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQa58bca335da4ac506b611ac4c5b19382', 'Twilio-Request-Duration': '0.147', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 8b519479acb11940c85c873e3175ae14.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '0co0H0f7STp9xPIwSF_313kDU3ATE8lrXm6Z-mV1NS0YzCuSJvnSBA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 18:21:14,621 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:21:14] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 18:21:31,273 - root - INFO - User logged in - User ID: 6854029a03fe25a0161e3bda
2025-06-19 18:21:31,273 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 18:21:31,275 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:21:31] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 18:21:31,285 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:21:31] "GET /connect HTTP/1.1" 200 -
2025-06-19 18:21:56,032 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 18:21:56,032 - root - INFO - login_required - User is logged in with ID: 6854029a03fe25a0161e3bda
2025-06-19 18:21:56,032 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '6854029a03fe25a0161e3bda'}
2025-06-19 18:21:56,032 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:21:56] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 18:22:02,010 - root - INFO - Twitter callback - Using user_id from session: 6853f49572410714215b14e5 of type <class 'str'>
2025-06-19 18:22:02,010 - root - INFO - Saving profile with user_id: 6853f49572410714215b14e5 of type <class 'bson.objectid.ObjectId'>
2025-06-19 18:22:02,376 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685407f272410714215b246c
2025-06-19 18:22:02,378 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:22:02] "GET /twitter/callback?state=agenticoauth&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-19 18:22:03,363 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:22:03] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-19 18:22:50,660 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:22:50] "GET / HTTP/1.1" 200 -
2025-06-19 18:22:58,699 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 18:22:58,700 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 18:22:58,700 - twilio.http_client - INFO - Headers:
2025-06-19 18:22:58,700 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 18:22:58,700 - twilio.http_client - INFO - Accept : application/json
2025-06-19 18:22:58,700 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 18:22:58,701 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 18:22:58,701 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 18:22:58,701 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 18:22:59,861 - twilio.http_client - INFO - Response Status Code: 400
2025-06-19 18:22:59,862 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json', 'Content-Length': '123', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:52:59 GMT', 'X-Twilio-Error-Code': '21211', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQcd302a6a9a21759a2a0499f78a3f39b9', 'Twilio-Request-Duration': '0.058', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Error from cloudfront', 'Via': '1.1 e84e3d2180ad6d18a31ea765a099b5b4.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'DxLz0F1vd1ra5S0BdSgby9zUKkTQs6eBuV9u7S_eXcjfNZpkuXSqAg==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 18:22:59,863 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:22:59] "[35m[1mPOST /auth/send-otp HTTP/1.1[0m" 500 -
2025-06-19 18:23:03,729 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:23:03] "GET / HTTP/1.1" 200 -
2025-06-19 18:23:07,576 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 18:23:07,576 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 18:23:07,576 - twilio.http_client - INFO - Headers:
2025-06-19 18:23:07,576 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 18:23:07,577 - twilio.http_client - INFO - Accept : application/json
2025-06-19 18:23:07,577 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 18:23:07,577 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 18:23:07,577 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 18:23:07,577 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 18:23:08,004 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 18:23:08,004 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 12:53:07 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ994ecdc5421cf25979408f329a20b9bf', 'Twilio-Request-Duration': '0.122', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 0b0f611d8f6c752b165371a160dc5c62.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'agBF0cKUnNQbcHG64l3Aye0-8dzFfkfmR8vncZn907KeYPDopu4E-w==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 18:23:08,006 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:23:08] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 18:23:34,665 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-19 18:23:34,665 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:23:34,667 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:23:34] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 18:23:34,675 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:23:34] "GET /connect HTTP/1.1" 200 -
2025-06-19 18:23:59,097 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:23:59,097 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 18:23:59,097 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:23:59,099 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:23:59] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 18:24:04,588 - root - INFO - Twitter callback - Using user_id from session: 6853f49572410714215b14e5 of type <class 'str'>
2025-06-19 18:24:04,589 - root - INFO - Saving profile with user_id: 6853f49572410714215b14e5 of type <class 'bson.objectid.ObjectId'>
2025-06-19 18:24:05,075 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6854086c72410714215b24f9
2025-06-19 18:24:05,076 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:24:05] "GET /twitter/callback?state=agenticoauth&code=ZThXbHF3Q2E3dzB2TEV4bGpTRTBpTDVhYTZIU3N6S1FaRGZZOG45X3lXcnY4OjE3NTAzMzc2NDE2NTg6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 18:29:08,444 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 18:29:09,024 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:29:09,501 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:29:09,507 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:33:22,076 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-06-19 18:33:22,590 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:33:23,115 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:33:23,121 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:33:33,185 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-19 18:33:33,621 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:33:33,968 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:33:33,971 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:33:39,013 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-19 18:33:39,488 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:33:39,899 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:33:39,902 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:33:53,995 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-19 18:33:54,416 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:33:54,823 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:33:54,825 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:33:55,843 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-19 18:33:56,338 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:33:56,663 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:33:56,665 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:34:34,986 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:34:34] "GET / HTTP/1.1" 200 -
2025-06-19 18:34:37,542 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:34:37] "GET /connect HTTP/1.1" 200 -
2025-06-19 18:34:40,233 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:34:40,233 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 18:34:40,233 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:34:40,234 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:34:40] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 18:34:45,582 - root - INFO - Twitter callback - Using user_id from session: 6853f49572410714215b14e5 of type <class 'str'>
2025-06-19 18:34:45,582 - root - INFO - Saving profile with user_id: 6853f49572410714215b14e5 of type <class 'bson.objectid.ObjectId'>
2025-06-19 18:34:46,995 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 68540aee72410714215b2722
2025-06-19 18:34:46,997 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:34:46] "GET /twitter/callback?state=agenticoauth&code=cElSbHNCV1ltbWZTVDZKV3FrckU1WmJPclg5cWpiTjk4aF9jUVRQMldnWjBJOjE3NTAzMzgyODI3NTc6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 18:35:05,734 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:35:05] "GET / HTTP/1.1" 200 -
2025-06-19 18:35:12,140 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:35:12] "[33mGET /logout HTTP/1.1[0m" 404 -
2025-06-19 18:37:13,799 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-19 18:37:14,280 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:37:14,665 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:37:14,668 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:37:16,691 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 18:37:17,178 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:37:17,530 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:37:17,532 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:37:29,836 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '9730baa3d84c2554c02e27303117a099d662a2d6', 'is_new_user': False, 'mobile': '+************', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:37:29,836 - root - INFO - Session after logout: {}
2025-06-19 18:37:29,837 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:37:29] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-19 18:37:29,854 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:37:29] "GET / HTTP/1.1" 200 -
2025-06-19 18:37:36,703 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:37:36] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-19 18:37:36,725 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:37:36] "GET / HTTP/1.1" 200 -
2025-06-19 18:37:41,104 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:37:41] "[33mPOST /auth/send-otp HTTP/1.1[0m" 404 -
2025-06-19 18:38:12,869 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 18:38:13,205 - werkzeug - INFO -  * Restarting with stat
2025-06-19 18:38:13,559 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 18:38:13,562 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 18:38:19,223 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:19] "GET / HTTP/1.1" 200 -
2025-06-19 18:38:24,969 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 18:38:24,969 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 18:38:24,969 - twilio.http_client - INFO - Headers:
2025-06-19 18:38:24,969 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 18:38:24,969 - twilio.http_client - INFO - Accept : application/json
2025-06-19 18:38:24,970 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 18:38:24,970 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 18:38:24,970 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 18:38:24,970 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 18:38:26,382 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 18:38:26,382 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 13:08:26 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ981cf29ac69e16dd6f815bb4748ea96c', 'Twilio-Request-Duration': '0.132', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 e84e3d2180ad6d18a31ea765a099b5b4.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'RbtB0_EFd6xAMGhlF4eQZMisJekj-zMNmSh0FXBqx6wSfg83KKLRMQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 18:38:26,388 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:26] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 18:38:39,693 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-19 18:38:39,694 - root - INFO - Session after login: {'csrf_token': '934292291e39b26fa152620fda651a965ed08cff', 'user_id': '68540833f34e1092d12781bc', '_permanent': True}
2025-06-19 18:38:39,695 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:39] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 18:38:39,706 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:39] "GET /connect HTTP/1.1" 200 -
2025-06-19 18:38:53,858 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '934292291e39b26fa152620fda651a965ed08cff', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:38:53,858 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 18:38:53,859 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '934292291e39b26fa152620fda651a965ed08cff', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 18:38:53,859 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:53] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 18:38:59,079 - root - INFO - Twitter callback - Using user_id from session: 6853f49572410714215b14e5 of type <class 'str'>
2025-06-19 18:38:59,079 - root - INFO - Saving profile with user_id: 6853f49572410714215b14e5 of type <class 'bson.objectid.ObjectId'>
2025-06-19 18:38:59,498 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 68540beb72410714215b280b
2025-06-19 18:38:59,500 - werkzeug - INFO - 1******** - - [19/Jun/2025 18:38:59] "GET /twitter/callback?state=agenticoauth&code=N0dneG9CZEtBWDd5QkNrU0hFWFdtakdyY0hYUkdUUXBTOUZxUkJReFRncDJkOjE3NTAzMzg1MzY0NjM6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 19:00:02,975 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 19:00:03,554 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:01:43,556 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 19:01:43,557 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 19:01:43,557 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:01:43,870 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:01:43,878 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:01:48,245 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:01:48] "GET / HTTP/1.1" 200 -
2025-06-19 19:01:55,572 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:01:55] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-19 19:01:55,581 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:01:55] "GET / HTTP/1.1" 200 -
2025-06-19 19:02:00,140 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:00] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-19 19:02:00,150 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:00] "GET / HTTP/1.1" 200 -
2025-06-19 19:02:18,733 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 19:02:18,733 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 19:02:18,733 - twilio.http_client - INFO - Headers:
2025-06-19 19:02:18,733 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 19:02:18,733 - twilio.http_client - INFO - Accept : application/json
2025-06-19 19:02:18,733 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 19:02:18,733 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 19:02:18,733 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 19:02:18,733 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 19:02:19,533 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 19:02:19,533 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 13:32:19 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ2a352edc118ad843825014e388e7eba4', 'Twilio-Request-Duration': '0.144', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 56ac9ee632b7bbf3c8d55761ceb503da.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'xbXhK7AOjQrQvSQERCE1rO0yvKyNE-TVTlD0N8N4fk5lhaqAdTQr9w==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 19:02:19,534 - root - INFO - OTP sent to +************, Twilio SID: SM2a352edc118ad843825014e388e7eba4
2025-06-19 19:02:19,539 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:19] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 19:02:32,960 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-19 19:02:32,960 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '07768fe760f80226b8acfd62185ae7d0b10715d7', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:02:32,962 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:32] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 19:02:32,975 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:32] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:02:35,453 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '07768fe760f80226b8acfd62185ae7d0b10715d7', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:02:35,454 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:02:35,454 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '07768fe760f80226b8acfd62185ae7d0b10715d7', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:02:35,455 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:35] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 19:02:40,855 - root - ERROR - Twitter callback - No user_id in session
2025-06-19 19:02:40,857 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:40] "[32mGET /twitter/callback?state=agenticoauth&code=cFZ6dW90SmdaVXM3OW5OZ0cwTTRlaUlFY2RpZGpTYUh0alp6N0wzNXZWQVZlOjE3NTAzMzk5NTgwNjI6MToxOmFjOjE HTTP/1.1[0m" 302 -
2025-06-19 19:02:40,867 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:02:40] "GET /auth/send-otp?next=http://localhost:5000/twitter/callback?state%3Dagenticoauth%26code%3DcFZ6dW90SmdaVXM3OW5OZ0cwTTRlaUlFY2RpZGpTYUh0alp6N0wzNXZWQVZlOjE3NTAzMzk5NTgwNjI6MToxOmFjOjE HTTP/1.1" 200 -
2025-06-19 19:05:31,472 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 19:05:31,978 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:05:32,378 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:05:32,382 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:05:51,514 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 19:05:51,874 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:08:05,884 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 19:08:05,884 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 19:08:05,885 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:08:06,192 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:08:06,195 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:08:14,460 - root - INFO - Session before request: {'_permanent': True, 'csrf_token': '07768fe760f80226b8acfd62185ae7d0b10715d7', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:14,467 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:14] "GET / HTTP/1.1" 200 -
2025-06-19 19:08:19,257 - root - INFO - Session before request: {'_permanent': True, 'csrf_token': '07768fe760f80226b8acfd62185ae7d0b10715d7', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:19,259 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:19] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-19 19:08:19,266 - root - INFO - Session before request: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')]}
2025-06-19 19:08:19,268 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:19] "GET / HTTP/1.1" 200 -
2025-06-19 19:08:23,889 - root - INFO - Session before request: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92'}
2025-06-19 19:08:26,399 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 19:08:26,399 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 19:08:26,399 - twilio.http_client - INFO - Headers:
2025-06-19 19:08:26,399 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 19:08:26,399 - twilio.http_client - INFO - Accept : application/json
2025-06-19 19:08:26,399 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 19:08:26,399 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 19:08:26,399 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 19:08:26,399 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 19:08:26,988 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 19:08:26,988 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 13:38:26 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ07306c336ce7ed2055a406c3752b027b', 'Twilio-Request-Duration': '0.135', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c2e71f7a744af65a5cea344ff07d9628.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'Rgo-hGBcgzCkCVy0afr1Hn1hYLtnm12qJpLuuSyi1jJdY3iF5El7yw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 19:08:26,989 - root - INFO - OTP sent to +************, Twilio SID: SM07306c336ce7ed2055a406c3752b027b
2025-06-19 19:08:26,996 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:26] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 19:08:49,277 - root - INFO - Session before request: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92'}
2025-06-19 19:08:50,296 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-19 19:08:50,296 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:50,298 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:50] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 19:08:50,306 - root - INFO - Session before request: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:50,311 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:50] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:08:53,812 - root - INFO - Session before request: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:53,812 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:54,357 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:08:54,357 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:08:54,359 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:08:54] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 19:08:59,438 - root - INFO - Session before request: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5'}
2025-06-19 19:08:59,439 - root - INFO - Twitter callback - Session at start: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5'}
2025-06-19 19:09:00,438 - root - ERROR - Twitter callback - No user_id in session
2025-06-19 19:09:00,443 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:09:00] "[32mGET /twitter/callback?state=agenticoauth&code=******************************************************************************************* HTTP/1.1[0m" 302 -
2025-06-19 19:09:00,452 - root - INFO - Session before request: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.')]}
2025-06-19 19:09:00,455 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:09:00] "GET /auth/send-otp?next=http://localhost:5000/twitter/callback?state%3Dagenticoauth%26code%3D******************************************************************************************* HTTP/1.1" 200 -
2025-06-19 19:11:55,825 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 19:11:56,309 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:11:56,758 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:11:56,761 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:11:57,777 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 19:11:58,263 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:11:58,664 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:11:58,668 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:12:12,763 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-19 19:12:13,166 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:12:13,607 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:12:13,613 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:12:26,731 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 19:12:27,081 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:12:27,515 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:12:27,519 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:12:43,620 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-19 19:12:44,190 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:12:44,529 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:12:44,532 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:12:52,595 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 19:12:52,595 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 19:12:52,596 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:12:52,905 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:12:52,908 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:12:56,466 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:56] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-19 19:12:57,062 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-19 19:12:57,101 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:57] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-19 19:12:57,104 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:57] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-19 19:12:57,385 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:57] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=yqdGyL2RaeoiPEFCQOwS HTTP/1.1" 200 -
2025-06-19 19:12:58,081 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:12:58] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-19 19:14:03,321 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-19 19:14:03,791 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:14:04,178 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:14:04,181 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:14:08,192 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:08,197 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:08] "GET / HTTP/1.1" 200 -
2025-06-19 19:14:13,704 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:16,120 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-19 19:14:16,120 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-19 19:14:16,120 - twilio.http_client - INFO - Headers:
2025-06-19 19:14:16,120 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-19 19:14:16,120 - twilio.http_client - INFO - Accept : application/json
2025-06-19 19:14:16,120 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-19 19:14:16,121 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-19 19:14:16,121 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-19 19:14:16,121 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-19 19:14:17,340 - twilio.http_client - INFO - Response Status Code: 201
2025-06-19 19:14:17,341 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 19 Jun 2025 13:44:17 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQfb9e97c187c8a775bc210b339e94aea7', 'Twilio-Request-Duration': '0.143', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 b097aefce5c96f6d140009a88452ea4a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '6V1d-6u_HEJ-XDnJTv98YjJK-zDCNoP9TbU0IklV0YJN_yvC0LiDng==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-19 19:14:17,341 - root - INFO - OTP sent to +************, Twilio SID: SMfb9e97c187c8a775bc210b339e94aea7
2025-06-19 19:14:17,347 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:17] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-19 19:14:28,350 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:29,343 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-19 19:14:29,343 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:29,345 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:29] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-19 19:14:29,353 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:29,358 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:29] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:14:32,305 - root - INFO - Request path: /twitter/login, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:32,305 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:32,745 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:14:32,746 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:14:32,747 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:32] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-19 19:14:37,206 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.')]}
2025-06-19 19:14:37,207 - root - INFO - Twitter callback - Session at start: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.')]}
2025-06-19 19:14:37,207 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-19 19:14:38,116 - root - INFO - Using user_id from state parameter: 68540833f34e1092d12781bc
2025-06-19 19:14:38,116 - root - INFO - Twitter callback - Using user_id: 68540833f34e1092d12781bc of type <class 'str'>
2025-06-19 19:14:38,116 - root - INFO - Saving profile with user_id: 68540833f34e1092d12781bc of type <class 'bson.objectid.ObjectId'>
2025-06-19 19:14:38,551 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6854144672410714215b2ed5
2025-06-19 19:14:38,554 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:14:38] "GET /twitter/callback?state=agenticoauth_68540833f34e1092d12781bc&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-19 19:16:32,162 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-19 19:16:32,450 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:16:32,835 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:16:32,838 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:16:48,936 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-19 19:16:49,373 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:16:49,731 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:16:49,734 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:17:15,898 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-06-19 19:17:16,412 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:17:16,833 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:17:16,836 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:17:21,752 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-19 19:17:21,752 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-19 19:17:21,753 - werkzeug - INFO -  * Restarting with stat
2025-06-19 19:17:22,059 - werkzeug - WARNING -  * Debugger is active!
2025-06-19 19:17:22,062 - werkzeug - INFO -  * Debugger PIN: 108-************-06-19 19:17:36,073 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:17:36,079 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:17:36] "GET / HTTP/1.1" 200 -
2025-06-19 19:17:38,371 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:17:38,376 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:17:38] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:17:42,320 - root - INFO - Request path: /youtube/login, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:17:42,320 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:17:44,211 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:17:44,211 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:17:44,214 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:17:44] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-19 19:18:03,067 - root - INFO - Request path: /youtube/callback, Session: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.'), ('message', 'Twitter account connected successfully!')]}
2025-06-19 19:18:03,068 - root - INFO - YouTube callback - Session at start: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.'), ('message', 'Twitter account connected successfully!')]}
2025-06-19 19:18:03,068 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-19 19:18:03,571 - root - INFO - Using user_id from state parameter: 68540833f34e1092d12781bc
2025-06-19 19:18:03,571 - root - INFO - YouTube callback - Using user_id: 68540833f34e1092d12781bc of type <class 'str'>
2025-06-19 19:18:03,572 - root - INFO - Saving profile with user_id: 68540833f34e1092d12781bc of type <class 'bson.objectid.ObjectId'>
2025-06-19 19:18:03,984 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6854151372410714215b2f8f
2025-06-19 19:18:03,987 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:18:03] "GET /youtube/callback?state=agenticoauth_68540833f34e1092d12781bc&code=4/0AUJR-x6KCQqvQ1p9tPShTyES7OtGa68uQtx_NYDoe1SBpwFLplEm7hOqTR_BEXEJR2EeaA&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1" 200 -
2025-06-19 19:18:17,123 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:18:17,126 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:18:17] "GET / HTTP/1.1" 200 -
2025-06-19 19:18:19,086 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:18:19,088 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:18:19] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:18:24,346 - root - INFO - Request path: /facebook/login, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:18:24,346 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:18:24,966 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:18:24,967 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:18:24,969 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:18:24] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-19 19:18:31,214 - root - INFO - Request path: /facebook/callback, Session: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')]}
2025-06-19 19:18:31,215 - root - INFO - Facebook callback - Session at start: {'_permanent': True, 'csrf_token': '26db16a003c4e1fc7bc9dfffb529da8af75f0ba5', '_flashes': [('message', 'Your session expired. Please log in again to connect your Twitter account.'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')]}
2025-06-19 19:18:31,215 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-19 19:18:32,333 - root - INFO - Using user_id from state parameter: 68540833f34e1092d12781bc
2025-06-19 19:18:32,333 - root - INFO - Facebook callback - Using user_id: 68540833f34e1092d12781bc of type <class 'str'>
2025-06-19 19:18:32,333 - root - INFO - Saving profile with user_id: 68540833f34e1092d12781bc of type <class 'bson.objectid.ObjectId'>
2025-06-19 19:18:32,742 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6854153072410714215b2fa9
2025-06-19 19:18:32,744 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:18:32] "GET /facebook/callback?code=AQBuwJZR3_C_dejiwm2MFeD000Rn7P9zt-T0gPf4l892uaqDbr_mG1ggVuprp1NiF7MfAvR74v2CBqf6awVxkD8mf2w1prOElYWo2s8W1rIEgrQnJomh3hwbQg0KPlmB8tG5KJAoJL5z1n1ODdRVZNvJbCEADze-LmEfQFHMqn6nBeQByVQv_6I_5LrzHdPA0v2RtsQj3G_xPz-9K5SOlvb46-TlmmNQMO9klFrBnE4LaZvAonJFAVUvGqIFXNZ1bhgk8Xy3Lr7oMgpQUBFiPhX6k8iuk1FynbAZxouA136kLloHI6wsmuzqU5cS2hasKypcc_Kop3LVy-5gdnRPwwxLCfaDc-XFagdukz9xc31Gsdc89v9EdpGzYgfeVE97WnQ&state=agenticoauth_68540833f34e1092d12781bc HTTP/1.1" 200 -
2025-06-19 19:19:13,963 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:13,965 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:13] "GET / HTTP/1.1" 200 -
2025-06-19 19:19:16,068 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:16,070 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:16] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:19:24,484 - root - INFO - Request path: /linkedin/login, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:24,484 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:25,081 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-19 19:19:25,081 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:25,082 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:25] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-06-19 19:19:41,986 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:41,988 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:41] "GET / HTTP/1.1" 200 -
2025-06-19 19:19:45,898 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:45,900 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:45] "GET /connect HTTP/1.1" 200 -
2025-06-19 19:19:56,523 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:56,523 - root - INFO - Logout called - Session before logout: {'_permanent': True, '_flashes': [('message', 'You have been successfully logged out.')], 'csrf_token': 'd202ff1fd07ee1a87ac45de3c2ed1686747e3f92', 'user_id': '68540833f34e1092d12781bc'}
2025-06-19 19:19:56,523 - root - INFO - Session after logout: {'_permanent': True}
2025-06-19 19:19:56,525 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:56] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-19 19:19:56,537 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-19 19:19:56,541 - werkzeug - INFO - 1******** - - [19/Jun/2025 19:19:56] "GET / HTTP/1.1" 200 -
2025-06-20 10:34:11,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-20 10:34:11,371 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 10:34:11,372 - werkzeug - INFO -  * Restarting with stat
2025-06-20 10:34:11,719 - werkzeug - WARNING -  * Debugger is active!
2025-06-20 10:34:11,726 - werkzeug - INFO -  * Debugger PIN: 105-795-547
2025-06-20 10:34:22,451 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 10:34:22,457 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:34:22] "GET / HTTP/1.1" 200 -
2025-06-20 10:34:23,766 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538'}
2025-06-20 10:34:23,768 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:34:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-20 10:34:28,826 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538'}
2025-06-20 10:34:28,827 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:34:28] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-20 10:34:28,837 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538'}
2025-06-20 10:34:28,840 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:34:28] "GET / HTTP/1.1" 200 -
2025-06-20 10:34:37,069 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538'}
2025-06-20 10:34:38,651 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-20 10:34:38,651 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-20 10:34:38,651 - twilio.http_client - INFO - Headers:
2025-06-20 10:34:38,651 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-20 10:34:38,651 - twilio.http_client - INFO - Accept : application/json
2025-06-20 10:34:38,651 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-20 10:34:38,651 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-20 10:34:38,651 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-20 10:34:38,651 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-20 10:34:39,334 - twilio.http_client - INFO - Response Status Code: 201
2025-06-20 10:34:39,334 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 20 Jun 2025 05:04:39 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ9b8e527a73e64c75f24b32d1c74d7285', 'Twilio-Request-Duration': '0.160', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c13661f9455806125a56201b0ccd2b96.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '9cVH7yLwUt8r0XKohZssZZh6QnVdHLYYIh5Lewl0QWRbbGcF0YOCuQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-20 10:34:39,334 - root - INFO - OTP sent to +************, Twilio SID: SM9b8e527a73e64c75f24b32d1c74d7285
2025-06-20 10:34:39,340 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:34:39] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-20 10:35:02,439 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538'}
2025-06-20 10:35:03,034 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-20 10:35:03,034 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:35:03,036 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:35:03] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-20 10:35:03,043 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:35:03,048 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:35:03] "GET /connect HTTP/1.1" 200 -
2025-06-20 10:37:00,257 - root - INFO - Request path: /twitter/login, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:37:00,257 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:37:00,516 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-20 10:37:00,516 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:37:00,519 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:37:00] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-20 10:37:05,494 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True}
2025-06-20 10:37:05,495 - root - INFO - Twitter callback - Session at start: {'_permanent': True}
2025-06-20 10:37:05,495 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-20 10:37:06,391 - root - INFO - Using user_id from state parameter: 68540833f34e1092d12781bc
2025-06-20 10:37:06,391 - root - INFO - Twitter callback - Using user_id: 68540833f34e1092d12781bc of type <class 'str'>
2025-06-20 10:37:06,392 - root - INFO - Saving profile with user_id: 68540833f34e1092d12781bc of type <class 'bson.objectid.ObjectId'>
2025-06-20 10:37:06,657 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-20 10:37:06,660 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:37:06] "GET /twitter/callback?state=agenticoauth_68540833f34e1092d12781bc&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-20 10:37:07,854 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, '_flashes': [('message', 'Twitter account connected successfully!')]}
2025-06-20 10:37:07,857 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:37:07] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-20 10:39:06,922 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True, '_flashes': [('message', 'Twitter account connected successfully!')]}
2025-06-20 10:39:06,923 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Twitter account connected successfully!')]}
2025-06-20 10:39:06,923 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-20 10:39:07,348 - root - ERROR - Error in Twitter callback: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py", line 80, in callback
    response.raise_for_status()
  File "/home/<USER>/Desktop/agentic_oauth_codebase/venv/lib/python3.12/site-packages/requests/models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
2025-06-20 10:39:07,352 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:07] "[35m[1mGET /twitter/callback?state=agenticoauth_68540833f34e1092d12781bc&code=******************************************************************************************* HTTP/1.1[0m" 500 -
2025-06-20 10:39:13,749 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:39:13,752 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:13] "GET /connect HTTP/1.1" 200 -
2025-06-20 10:39:15,871 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:39:15,872 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '2c59433a48c407ba8ae512e921bdcf4bf8ab4538', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 10:39:15,872 - root - INFO - Session after logout: {'_permanent': True}
2025-06-20 10:39:15,874 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:15] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-20 10:39:15,885 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 10:39:15,888 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:15] "GET / HTTP/1.1" 200 -
2025-06-20 10:39:19,183 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '7d6f59d6b178d5182a6fe2358f4f164402252287'}
2025-06-20 10:39:19,185 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:19] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-20 10:39:19,197 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '7d6f59d6b178d5182a6fe2358f4f164402252287'}
2025-06-20 10:39:19,199 - werkzeug - INFO - 1******** - - [20/Jun/2025 10:39:19] "GET / HTTP/1.1" 200 -
2025-06-20 12:54:20,152 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-20 12:54:20,152 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 12:54:20,153 - werkzeug - INFO -  * Restarting with stat
2025-06-20 12:54:20,464 - werkzeug - WARNING -  * Debugger is active!
2025-06-20 12:54:20,470 - werkzeug - INFO -  * Debugger PIN: 105-795-547
2025-06-20 12:54:25,999 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 12:54:26,007 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:54:26] "GET / HTTP/1.1" 200 -
2025-06-20 12:54:32,565 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33'}
2025-06-20 12:54:34,480 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-20 12:54:34,480 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-20 12:54:34,480 - twilio.http_client - INFO - Headers:
2025-06-20 12:54:34,480 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-20 12:54:34,481 - twilio.http_client - INFO - Accept : application/json
2025-06-20 12:54:34,481 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-20 12:54:34,481 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-20 12:54:34,481 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-20 12:54:34,481 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-20 12:54:35,043 - twilio.http_client - INFO - Response Status Code: 201
2025-06-20 12:54:35,043 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 20 Jun 2025 07:24:35 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQcc157e0e6d3f58efd752aec3f4dc9d82', 'Twilio-Request-Duration': '0.152', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 311b540b2896b95f3dd13864b05aa3fa.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'oB6SqrzIU1Rt97D6acJ8MS7e1NkZSMMvPxWZc1iw06uS2AUuDB01vw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-20 12:54:35,044 - root - INFO - OTP sent to +************, Twilio SID: SMcc157e0e6d3f58efd752aec3f4dc9d82
2025-06-20 12:54:35,050 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:54:35] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-20 12:54:50,472 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33'}
2025-06-20 12:54:51,292 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-20 12:54:51,293 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:54:51,295 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:54:51] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-20 12:54:51,304 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:54:51,309 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:54:51] "GET /connect HTTP/1.1" 200 -
2025-06-20 12:54:54,373 - root - INFO - Request path: /linkedin/login, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:54:54,373 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:54:54,639 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-20 12:54:54,639 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:54:54,641 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:54:54] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-06-20 12:55:04,573 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:55:04,575 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:55:04] "GET /connect HTTP/1.1" 200 -
2025-06-20 12:55:08,506 - root - INFO - Request path: /linkedin/login, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:55:08,507 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:55:08,906 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-20 12:55:08,907 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 12:55:08,908 - werkzeug - INFO - 1******** - - [20/Jun/2025 12:55:08] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-06-20 13:01:22,875 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:01:22,876 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': 'b1740d40f3e70b04373fcf64458c8ece73442b33', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:01:22,876 - root - INFO - Session after logout: {'_permanent': True}
2025-06-20 13:01:22,881 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:01:22] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-20 13:01:22,894 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 13:01:22,897 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:01:22] "GET / HTTP/1.1" 200 -
2025-06-20 13:03:07,725 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0'}
2025-06-20 13:03:08,265 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-20 13:03:08,265 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-20 13:03:08,266 - twilio.http_client - INFO - Headers:
2025-06-20 13:03:08,266 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-20 13:03:08,266 - twilio.http_client - INFO - Accept : application/json
2025-06-20 13:03:08,266 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-20 13:03:08,266 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-20 13:03:08,266 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-20 13:03:08,266 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-20 13:03:08,973 - twilio.http_client - INFO - Response Status Code: 201
2025-06-20 13:03:08,973 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 20 Jun 2025 07:33:08 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ694f7f682cdd4a7e3a74db5f33b99e45', 'Twilio-Request-Duration': '0.149', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 fc93b40927ecc3b1d5e4fffeca69b042.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '-KTHXuXLZ-pvOyi8XULfcmQKTJcYeijRCdDTLAGKOXdw8HYHQDBUYg==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-20 13:03:08,973 - root - INFO - OTP sent to +************, Twilio SID: SM694f7f682cdd4a7e3a74db5f33b99e45
2025-06-20 13:03:08,975 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:03:08] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-20 13:03:24,214 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0'}
2025-06-20 13:03:24,751 - root - INFO - User logged in - User ID: 68540833f34e1092d12781bc
2025-06-20 13:03:24,751 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:03:24,753 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:03:24] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-20 13:03:24,759 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:03:24,761 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:03:24] "GET /connect HTTP/1.1" 200 -
2025-06-20 13:03:33,602 - root - INFO - Request path: /twitter/login, Session: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:03:33,603 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:03:33,870 - root - INFO - login_required - User is logged in with ID: 68540833f34e1092d12781bc
2025-06-20 13:03:33,870 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': 'a25afa6b68bf579b6ec55b9a5792a4c1cd393ff0', 'user_id': '68540833f34e1092d12781bc'}
2025-06-20 13:03:33,873 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:03:33] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-20 13:03:42,499 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True}
2025-06-20 13:03:42,500 - root - INFO - Twitter callback - Session at start: {'_permanent': True}
2025-06-20 13:03:42,500 - root - INFO - Extracted user_id from state: 68540833f34e1092d12781bc
2025-06-20 13:03:43,413 - root - INFO - Using user_id from state parameter: 68540833f34e1092d12781bc
2025-06-20 13:03:43,413 - root - INFO - Twitter callback - Using user_id: 68540833f34e1092d12781bc of type <class 'str'>
2025-06-20 13:03:43,413 - root - INFO - Saving profile with user_id: 68540833f34e1092d12781bc of type <class 'bson.objectid.ObjectId'>
2025-06-20 13:03:43,682 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-20 13:03:43,684 - werkzeug - INFO - 1******** - - [20/Jun/2025 13:03:43] "GET /twitter/callback?state=agenticoauth_68540833f34e1092d12781bc&code=WWs3T2dDY3V0MkhObWhFQVhhZHU0OC1JREdfb3F6LUFkc0pGeFJrT21EQWZmOjE3NTA0MDQ4MTc0MTk6MTowOmFjOjE HTTP/1.1" 200 -
2025-06-20 16:22:45,006 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 16:22:45,068 - werkzeug - INFO - 1******** - - [20/Jun/2025 16:22:45] "GET / HTTP/1.1" 200 -
2025-06-20 16:53:11,313 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-20 16:53:11,314 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 16:53:11,315 - werkzeug - INFO -  * Restarting with stat
2025-06-20 16:53:11,683 - werkzeug - WARNING -  * Debugger is active!
2025-06-20 16:53:11,690 - werkzeug - INFO -  * Debugger PIN: 105-795-547
2025-06-20 16:53:17,596 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 16:53:17,604 - werkzeug - INFO - 1******** - - [20/Jun/2025 16:53:17] "GET / HTTP/1.1" 200 -
2025-06-20 16:53:25,725 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': 'fa33ed819dea98431035367d32f403a555392eea'}
2025-06-20 16:53:25,731 - werkzeug - INFO - 1******** - - [20/Jun/2025 16:53:25] "GET /subscription HTTP/1.1" 200 -
2025-06-20 16:55:21,590 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': 'fa33ed819dea98431035367d32f403a555392eea'}
2025-06-20 16:55:21,596 - werkzeug - INFO - 1******** - - [20/Jun/2025 16:55:21] "GET /subscription HTTP/1.1" 200 -
2025-06-20 17:34:20,587 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 17:34:20,595 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:34:20] "GET / HTTP/1.1" 200 -
2025-06-20 17:45:03,781 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b'}
2025-06-20 17:45:03,788 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:45:03] "GET / HTTP/1.1" 200 -
2025-06-20 17:47:42,892 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b'}
2025-06-20 17:47:42,899 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:47:42] "GET / HTTP/1.1" 200 -
2025-06-20 17:47:56,363 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b'}
2025-06-20 17:47:58,802 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-20 17:47:58,802 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-20 17:47:58,802 - twilio.http_client - INFO - Headers:
2025-06-20 17:47:58,802 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-20 17:47:58,802 - twilio.http_client - INFO - Accept : application/json
2025-06-20 17:47:58,802 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-20 17:47:58,802 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-20 17:47:58,802 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-20 17:47:58,802 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-20 17:47:59,288 - twilio.http_client - INFO - Response Status Code: 201
2025-06-20 17:47:59,288 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 20 Jun 2025 12:17:59 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ3506639fc937785130a7524fe0f973fc', 'Twilio-Request-Duration': '0.146', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 9c90794f48a52061ae9b790232a927e0.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'UKi5maksyD9yItn5eZT3EXMXjsVl5-Wy7EtUivt-WKTpOli0_-8Nog==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-20 17:47:59,288 - root - INFO - OTP sent to +************, Twilio SID: SM3506639fc937785130a7524fe0f973fc
2025-06-20 17:47:59,295 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:47:59] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-20 17:48:21,469 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b'}
2025-06-20 17:48:22,333 - root - INFO - User logged in - User ID: 68555135d9149ab3af7adaba
2025-06-20 17:48:22,333 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:48:22,334 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:48:22] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-20 17:48:22,342 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:48:22,349 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:48:22] "GET /connect HTTP/1.1" 200 -
2025-06-20 17:49:11,714 - root - INFO - Request path: /facebook/login, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:11,714 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:12,062 - root - INFO - login_required - User is logged in with ID: 68555135d9149ab3af7adaba
2025-06-20 17:49:12,063 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:12,065 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:49:12] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-20 17:49:22,997 - root - INFO - Request path: /facebook/callback, Session: {'_permanent': True}
2025-06-20 17:49:22,997 - root - INFO - Facebook callback - Session at start: {'_permanent': True}
2025-06-20 17:49:22,997 - root - INFO - Extracted user_id from state: 68555135d9149ab3af7adaba
2025-06-20 17:49:23,826 - root - INFO - Using user_id from state parameter: 68555135d9149ab3af7adaba
2025-06-20 17:49:23,827 - root - INFO - Facebook callback - Using user_id: 68555135d9149ab3af7adaba of type <class 'str'>
2025-06-20 17:49:23,827 - root - INFO - Saving profile with user_id: 68555135d9149ab3af7adaba of type <class 'bson.objectid.ObjectId'>
2025-06-20 17:49:24,200 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685551cb72410714215be0db
2025-06-20 17:49:24,202 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:49:24] "GET /facebook/callback?code=AQCTE8cU6-2so3dQ9wwO-ylr2-t37FpLiEIoBweTUn5DQHLni7ZIbrryTeRdizCcklc95g6LJdDbwaUcW4UuOJcQgEkhG-Kr7EBrCkdbDeCafPV4QR5N1QaJKejF0yHn9R6w0xrBEVKsDMFLLfpz1cr8XW0WKmAf9uhahPskNFVGd7DQz3UjsRDmg82hg14Ft7DuOz7Z1q2ZkRByWTIdqYsOJG-t3HmrFxZkXNr5a4Oo8TA4_1NwwvSMXyFKH1-hNYBPPTshqFPX-063tnXRjOsV7dFoLkDeU3A47nP_HDgjL--flWkynGVGL0B3JdqVRo2aG36C-m_xufEBBCUYfa14IjuMzEn0QfghTuV0StIWqa09KYkDlVNKtMKTprlHBko&state=agenticoauth_68555135d9149ab3af7adaba HTTP/1.1" 200 -
2025-06-20 17:49:52,782 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:52,784 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:49:52] "GET / HTTP/1.1" 200 -
2025-06-20 17:49:55,115 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:55,117 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:49:55] "GET /connect HTTP/1.1" 200 -
2025-06-20 17:49:57,232 - root - INFO - Request path: /twitter/login, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:57,233 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:57,592 - root - INFO - login_required - User is logged in with ID: 68555135d9149ab3af7adaba
2025-06-20 17:49:57,592 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:49:57,594 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:49:57] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-20 17:51:27,122 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-20 17:51:27,122 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-20 17:51:27,122 - root - INFO - Extracted user_id from state: 68555135d9149ab3af7adaba
2025-06-20 17:51:28,123 - root - INFO - Using user_id from state parameter: 68555135d9149ab3af7adaba
2025-06-20 17:51:28,123 - root - INFO - Twitter callback - Using user_id: 68555135d9149ab3af7adaba of type <class 'str'>
2025-06-20 17:51:28,124 - root - INFO - Saving profile with user_id: 68555135d9149ab3af7adaba of type <class 'bson.objectid.ObjectId'>
2025-06-20 17:51:28,480 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6855524872410714215be158
2025-06-20 17:51:28,482 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:51:28] "GET /twitter/callback?state=agenticoauth_68555135d9149ab3af7adaba&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-20 17:51:52,287 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:51:52,291 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:51:52] "GET / HTTP/1.1" 200 -
2025-06-20 17:51:55,107 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:51:55,109 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:51:55] "GET /connect HTTP/1.1" 200 -
2025-06-20 17:51:58,402 - root - INFO - Request path: /youtube/login, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:51:58,405 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:51:58,792 - root - INFO - login_required - User is logged in with ID: 68555135d9149ab3af7adaba
2025-06-20 17:51:58,792 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:51:58,794 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:51:58] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-20 17:52:38,600 - root - INFO - Request path: /youtube/callback, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')]}
2025-06-20 17:52:38,601 - root - INFO - YouTube callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')]}
2025-06-20 17:52:38,601 - root - INFO - Extracted user_id from state: 68555135d9149ab3af7adaba
2025-06-20 17:52:39,089 - root - INFO - Using user_id from state parameter: 68555135d9149ab3af7adaba
2025-06-20 17:52:39,090 - root - INFO - YouTube callback - Using user_id: 68555135d9149ab3af7adaba of type <class 'str'>
2025-06-20 17:52:39,090 - root - INFO - Saving profile with user_id: 68555135d9149ab3af7adaba of type <class 'bson.objectid.ObjectId'>
2025-06-20 17:52:39,485 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 6855528f72410714215be19d
2025-06-20 17:52:39,494 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:52:39] "GET /youtube/callback?state=agenticoauth_68555135d9149ab3af7adaba&code=4/0AUJR-x6X-oGui_ccRxIW5i-sxaEGHqE1cKl1F3K4CbnUVflK-qgORG7GwrsEowkG_KXXFw&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1" 200 -
2025-06-20 17:53:10,241 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:53:10,242 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:53:10] "GET / HTTP/1.1" 200 -
2025-06-20 17:53:13,592 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:53:13,594 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:53:13] "GET /connect HTTP/1.1" 200 -
2025-06-20 17:53:18,588 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:53:18,588 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '0c8b40d8616c374c43fc6bf1715fd628f81f641b', 'user_id': '68555135d9149ab3af7adaba'}
2025-06-20 17:53:18,588 - root - INFO - Session after logout: {'_permanent': True}
2025-06-20 17:53:18,590 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:53:18] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-20 17:53:18,614 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 17:53:18,617 - werkzeug - INFO - 1******** - - [20/Jun/2025 17:53:18] "GET / HTTP/1.1" 200 -
2025-06-20 18:51:34,580 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-20 18:51:34,589 - werkzeug - INFO - 1******** - - [20/Jun/2025 18:51:34] "GET / HTTP/1.1" 200 -
2025-06-25 12:35:42,808 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-25 12:35:42,808 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 12:35:42,809 - werkzeug - INFO -  * Restarting with stat
2025-06-25 12:35:43,171 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 12:35:43,178 - werkzeug - INFO -  * Debugger PIN: 182-527-016
2025-06-25 12:35:50,550 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-25 12:35:50,563 - werkzeug - INFO - 1******** - - [25/Jun/2025 12:35:50] "GET / HTTP/1.1" 200 -
2025-06-25 12:35:51,539 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': '51cd4fb13d7efdb75bec701db620795ba6538069'}
2025-06-25 12:35:51,541 - werkzeug - INFO - 1******** - - [25/Jun/2025 12:35:51] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-25 12:35:57,043 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '51cd4fb13d7efdb75bec701db620795ba6538069'}
2025-06-25 12:35:57,047 - werkzeug - INFO - 1******** - - [25/Jun/2025 12:35:57] "GET /subscription HTTP/1.1" 200 -
2025-06-26 09:40:33,154 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-26 09:40:33,154 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 09:40:33,155 - werkzeug - INFO -  * Restarting with stat
2025-06-26 09:40:33,510 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 09:40:33,517 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 09:40:43,771 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 09:40:43,782 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:40:43] "GET / HTTP/1.1" 200 -
2025-06-26 09:40:44,925 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83'}
2025-06-26 09:40:44,927 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:40:44] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-26 09:41:01,010 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83'}
2025-06-26 09:41:01,013 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:41:01] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 09:41:01,024 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83'}
2025-06-26 09:41:01,025 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83'}
2025-06-26 09:41:01,025 - root - ERROR - login_required - No user_id in session
2025-06-26 09:41:01,027 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:41:01] "[32mGET /subscription/ HTTP/1.1[0m" 302 -
2025-06-26 09:41:01,034 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 09:41:01,038 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:41:01] "GET /auth/send-otp?next=http://1********:5000/subscription/ HTTP/1.1" 200 -
2025-06-26 09:50:11,245 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 09:50:11,869 - werkzeug - INFO -  * Restarting with stat
2025-06-26 09:51:14,300 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-26 09:51:14,300 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 09:51:14,300 - werkzeug - INFO -  * Restarting with stat
2025-06-26 09:51:14,678 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 09:51:14,681 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 09:51:19,833 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 09:51:19,846 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:51:19] "GET /auth/send-otp?next=http://1********:5000/subscription/ HTTP/1.1" 200 -
2025-06-26 09:51:21,820 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 09:51:21,824 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:51:21] "GET /auth/send-otp?next=http://1********:5000/subscription/ HTTP/1.1" 200 -
2025-06-26 09:51:25,661 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 09:51:27,020 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 09:51:27,020 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 09:51:27,020 - twilio.http_client - INFO - Headers:
2025-06-26 09:51:27,020 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 09:51:27,020 - twilio.http_client - INFO - Accept : application/json
2025-06-26 09:51:27,020 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 09:51:27,020 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 09:51:27,021 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 09:51:27,021 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 09:51:27,551 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 09:51:27,551 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 04:21:27 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQec5dfe311f156a163cf05f72e594ac70', 'Twilio-Request-Duration': '0.139', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 b0f8455cc4557d0eb768236b7fae9c06.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'HIU8_h4rR9H9BZR36zG6H8FXccizzjSeNfPh3-QcYX_hF7a6xtBhlQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 09:51:27,551 - root - INFO - OTP sent to +************, Twilio SID: SMec5dfe311f156a163cf05f72e594ac70
2025-06-26 09:51:27,557 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:51:27] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 09:52:04,548 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 09:52:06,055 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 09:52:06,055 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:06,058 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:06] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-26 09:52:06,068 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:06,074 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:06] "GET /connect HTTP/1.1" 200 -
2025-06-26 09:52:12,902 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:12,905 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:12] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 09:52:12,921 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:12,922 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:13,275 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 09:52:13,700 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:13] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 09:52:52,526 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:52,528 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:52:52,803 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 09:52:53,359 - root - WARNING - Telegram notification not sent: User 685cc0e0d9149ab3af7adaf1 has no telegram_chat_id
2025-06-26 09:52:53,362 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:53] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 09:52:53,370 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 09:52:53,370 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 09:52:53,712 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 09:52:54,034 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:52:54] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 09:53:44,073 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 09:53:44,076 - werkzeug - INFO - 1******** - - [26/Jun/2025 09:53:44] "GET /connect HTTP/1.1" 200 -
2025-06-26 10:00:44,896 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 10:00:45,472 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:00:46,426 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:00:46,438 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:00:48,472 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:00:48,961 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:00:49,761 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:00:49,766 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:01:15,970 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:01:16,531 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:01:17,382 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:01:17,388 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:02:10,906 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:02:11,433 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:02:12,182 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:02:12,187 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:02:38,303 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:02:38,316 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:02:38] "GET /connect HTTP/1.1" 200 -
2025-06-26 10:02:42,300 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:02:42,303 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:02:42] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 10:02:42,313 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:02:42,313 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:02:43,563 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:02:43,885 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:02:43] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:07:12,465 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:07:12,814 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:07:13,688 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:07:13,693 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:07:14,720 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 10:07:15,243 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:07:16,174 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:07:16,179 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:07:30,346 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 10:07:30,930 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:07:31,768 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:07:31,773 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:07:43,879 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-26 10:07:43,879 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 10:07:43,880 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:07:44,585 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:07:44,590 - werkzeug - INFO -  * Debugger PIN: 106-308-926
2025-06-26 10:07:49,914 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:07:49,914 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:07:51,012 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:07:51,310 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:07:51] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:07:56,517 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:07:56,520 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:07:56,808 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:07:57,422 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:07:57,422 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login
2025-06-26 10:07:57,422 - root - WARNING - Telegram notification not sent: User 685cc0e0d9149ab3af7adaf1 has no ChatId or telegram_chat_id
2025-06-26 10:07:57,423 - root - INFO - User mobile: +************
2025-06-26 10:07:57,423 - root - WARNING - Cannot update chat state: User 685cc0e0d9149ab3af7adaf1 has no ChatId
2025-06-26 10:07:57,425 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:07:57] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 10:07:57,433 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:07:57,433 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '72efb6b78891b77f05b6c884b9d63cc4885dec83', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:07:57,730 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:07:58,002 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:07:58] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:42:01,750 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-26 10:42:01,750 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 10:42:01,751 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:42:02,044 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:42:02,050 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:42:08,813 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 10:42:08,819 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:08] "GET / HTTP/1.1" 200 -
2025-06-26 10:42:09,719 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299'}
2025-06-26 10:42:09,721 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:09] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-26 10:42:13,712 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299'}
2025-06-26 10:42:15,684 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 10:42:15,684 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 10:42:15,684 - twilio.http_client - INFO - Headers:
2025-06-26 10:42:15,684 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 10:42:15,684 - twilio.http_client - INFO - Accept : application/json
2025-06-26 10:42:15,684 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 10:42:15,684 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 10:42:15,684 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 10:42:15,684 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 10:42:16,269 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 10:42:16,269 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 05:12:16 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ304e7e84ceda57e49268aa1d57c7cd14', 'Twilio-Request-Duration': '0.174', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 30d6f8dde4b5a27a899bbabad07e3ed2.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'q8i-czLjZP6WTo110HzulkiR4VcpbRGzzWQWWYs86vTGsvCjiEUDcA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 10:42:16,269 - root - INFO - OTP sent to +************, Twilio SID: SM304e7e84ceda57e49268aa1d57c7cd14
2025-06-26 10:42:16,275 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:16] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 10:42:31,613 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299'}
2025-06-26 10:42:32,753 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:42:32,753 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:42:32,755 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:32] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-26 10:42:32,762 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:42:32,767 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:32] "GET /connect HTTP/1.1" 200 -
2025-06-26 10:42:35,890 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:42:35,891 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:35] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 10:42:35,902 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:42:35,902 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:42:36,160 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:42:36,555 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:42:36] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:43:59,343 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:43:59,344 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:43:59,607 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:43:59,896 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:43:59] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:44:11,622 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:44:11,626 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:44:11,874 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:44:12,361 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:44:12,361 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login
2025-06-26 10:44:12,361 - root - INFO - Sending Telegram notification to chat_id: 7644343290
2025-06-26 10:44:13,096 - root - INFO - Telegram notification sent successfully: {'ok': True, 'result': {'message_id': 391, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750914852, 'text': '🎉 New Subscription!\n\nUser: Unknown (+************)\nPlan: Starter\nPrice: $9.99\nTokens: 250\nDate: 2025-06-26 05:14:12 UTC', 'entities': [{'offset': 37, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 10:44:13,097 - root - INFO - Attempting to update chat state to 'awaiting_post' for chat_id: 7644343290
2025-06-26 10:44:13,341 - root - INFO - Created new chat state with state 'awaiting_post' for chat_id: 7644343290
2025-06-26 10:44:13,344 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:44:13] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 10:44:13,352 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:44:13,352 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:44:13,600 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:44:13,846 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:44:13] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:50:35,527 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:50:35,898 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:50:36,370 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:50:36,374 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:50:51,484 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-26 10:50:51,863 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:50:52,292 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:50:52,295 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:53:41,326 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-26 10:53:41,870 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:53:42,343 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:53:42,351 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:55:22,565 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:55:22,565 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:55:23,852 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:55:24,169 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:24] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:55:26,582 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:55:26,583 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': 'dcb71e169ed382c9a40849a14fb494d57aadb299', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:55:26,583 - root - INFO - Session after logout: {'_permanent': True}
2025-06-26 10:55:26,586 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:26] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-26 10:55:26,598 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 10:55:26,602 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:26] "GET / HTTP/1.1" 200 -
2025-06-26 10:55:32,581 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:55:33,189 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 10:55:33,189 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 10:55:33,189 - twilio.http_client - INFO - Headers:
2025-06-26 10:55:33,189 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 10:55:33,189 - twilio.http_client - INFO - Accept : application/json
2025-06-26 10:55:33,190 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 10:55:33,190 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 10:55:33,190 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 10:55:33,190 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 10:55:33,681 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 10:55:33,681 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 05:25:33 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ165fda6359a682145020105fc589b269', 'Twilio-Request-Duration': '0.149', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 432645b1da9920794b254755cc203550.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'jjZF_BvrN7ZUDG9-c7ubcayWv-eflim4HAuRpUHo3_bFf6Cq0uzZDw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 10:55:33,682 - root - INFO - OTP sent to +************, Twilio SID: SM165fda6359a682145020105fc589b269
2025-06-26 10:55:33,688 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:33] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 10:55:48,451 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:55:48,984 - root - INFO - Deleted OTP record for mobile: +************
2025-06-26 10:55:49,654 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:55:49,654 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:55:49,666 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:49] "[35m[1mPOST /auth/verify-otp HTTP/1.1[0m" 500 -
2025-06-26 10:55:49,706 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:49] "GET /auth/verify-otp?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-26 10:55:49,707 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:49] "GET /auth/verify-otp?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-26 10:55:49,783 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:49] "GET /auth/verify-otp?__debugger__=yes&cmd=resource&f=console.png&s=2V9v36r9Xn7C0inddssj HTTP/1.1" 200 -
2025-06-26 10:55:49,970 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:55:49] "GET /auth/verify-otp?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-26 10:57:03,695 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/auth.py', reloading
2025-06-26 10:57:04,121 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:57:04,499 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:57:04,502 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:57:43,741 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:57:44,155 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:57:44,568 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:57:44,572 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:58:04,689 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 10:58:05,092 - werkzeug - INFO -  * Restarting with stat
2025-06-26 10:58:05,608 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 10:58:05,612 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 10:58:42,009 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:58:43,080 - root - ERROR - Invalid OTP for mobile: +************
2025-06-26 10:58:43,083 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:58:43] "[31m[1mPOST /auth/verify-otp HTTP/1.1[0m" 401 -
2025-06-26 10:58:54,499 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:58:54,504 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:58:54] "GET / HTTP/1.1" 200 -
2025-06-26 10:58:56,125 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:58:56,127 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:58:56] "GET / HTTP/1.1" 200 -
2025-06-26 10:59:01,002 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:59:01,629 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 10:59:01,629 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 10:59:01,629 - twilio.http_client - INFO - Headers:
2025-06-26 10:59:01,629 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 10:59:01,630 - twilio.http_client - INFO - Accept : application/json
2025-06-26 10:59:01,630 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 10:59:01,630 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 10:59:01,630 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 10:59:01,630 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 10:59:02,240 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 10:59:02,240 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 05:29:02 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQc24c8603d6fbadf7d8f60f3663924b3b', 'Twilio-Request-Duration': '0.158', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 32425d05dba07c0bc08aaf2049c77326.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'o9i6kIqPmIUyMW3tcoB1oXzr92awxNqJeBF5Yjy3kMhikF55HwLxvw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 10:59:02,241 - root - INFO - OTP sent to +************, Twilio SID: SMc24c8603d6fbadf7d8f60f3663924b3b
2025-06-26 10:59:02,246 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:02] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 10:59:13,880 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442'}
2025-06-26 10:59:14,470 - root - INFO - Deleted OTP record for mobile: +************
2025-06-26 10:59:14,995 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:14,996 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:15,394 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:15] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-26 10:59:15,403 - root - INFO - Request path: /auth/complete-profile, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:15,403 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:15,765 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:16,037 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:16] "GET /auth/complete-profile HTTP/1.1" 200 -
2025-06-26 10:59:16,067 - root - INFO - Request path: /static/css/style.css, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:16,069 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:16] "[33mGET /static/css/style.css HTTP/1.1[0m" 404 -
2025-06-26 10:59:23,933 - root - INFO - Request path: /auth/complete-profile, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:23,934 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:24,199 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:24,608 - root - INFO - Updated user info for user 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:24,610 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:24] "[32mPOST /auth/complete-profile HTTP/1.1[0m" 302 -
2025-06-26 10:59:24,619 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 10:59:24,623 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:24] "GET /connect HTTP/1.1" 200 -
2025-06-26 10:59:42,386 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 10:59:42,388 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:42] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 10:59:42,397 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 10:59:42,398 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 10:59:42,735 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:43,011 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:43] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 10:59:56,702 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:56,703 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 10:59:56,975 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:57,686 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:57,687 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-26 10:59:57,687 - root - INFO - Sending Telegram notification to chat_id: 7644343290
2025-06-26 10:59:58,414 - root - INFO - Telegram notification sent successfully: {'ok': True, 'result': {'message_id': 392, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750915798, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Starter\nPrice: $9.99\nTokens: 250\nDate: 2025-06-26 05:29:57 UTC', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 10:59:58,415 - root - INFO - Attempting to update chat state to 'awaiting_post' for chat_id: 7644343290
2025-06-26 10:59:58,950 - root - INFO - Created new chat state with state 'awaiting_post' for chat_id: 7644343290
2025-06-26 10:59:58,952 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:58] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 10:59:58,959 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:59:58,959 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 10:59:59,222 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 10:59:59,491 - werkzeug - INFO - 1******** - - [26/Jun/2025 10:59:59] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 11:16:37,326 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/db.py', reloading
2025-06-26 11:16:37,736 - werkzeug - INFO -  * Restarting with stat
2025-06-26 11:16:38,383 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 11:16:38,391 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 11:17:08,586 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 11:17:09,064 - werkzeug - INFO -  * Restarting with stat
2025-06-26 11:17:09,387 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 11:17:09,390 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 11:18:28,219 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:18:28,219 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:18:29,663 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:18:29,947 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:18:29] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 11:18:34,872 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:18:34,873 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:18:35,135 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:18:35,658 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:18:35,658 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-26 11:18:35,659 - root - INFO - Sending Telegram notification to chat_id: 7644343290
2025-06-26 11:18:36,538 - root - INFO - Telegram notification sent successfully: {'ok': True, 'result': {'message_id': 393, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750916916, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Starter\nPrice: $9.99\nTokens: 250\nDate: 2025-06-26 05:48:35 UTC', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 11:18:36,539 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 11:18:37,161 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 11:18:37,163 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:18:37] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 11:18:37,171 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 11:18:37,172 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 11:18:37,445 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:18:37,754 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:18:37] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 11:22:38,423 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 11:22:38,990 - werkzeug - INFO -  * Restarting with stat
2025-06-26 11:22:39,359 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 11:22:39,362 - werkzeug - INFO -  * Debugger PIN: 142-************-06-26 11:26:22,622 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:26:22,623 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:26:23,982 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:26:24,306 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:26:24] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 11:26:30,023 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:26:30,025 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 11:26:30,331 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:26:31,150 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:26:31,150 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-26 11:26:31,150 - root - INFO - Sending Telegram notification to chat_id: 7644343290
2025-06-26 11:26:31,971 - root - INFO - Telegram notification sent successfully: {'ok': True, 'result': {'message_id': 394, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750917391, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Starter\nPrice: $9.99\nTokens: 250', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 11:26:31,971 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 11:26:32,687 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 11:26:32,689 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:26:32] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 11:26:32,698 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 11:26:32,698 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '52de55f7eb1f1363ae2008244028cfde30655442', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Starter plan!')]}
2025-06-26 11:26:32,953 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 11:26:33,302 - werkzeug - INFO - 1******** - - [26/Jun/2025 11:26:33] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 12:14:13,716 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-26 12:14:13,716 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 12:14:13,717 - werkzeug - INFO -  * Restarting with stat
2025-06-26 12:14:14,080 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 12:14:14,086 - werkzeug - INFO -  * Debugger PIN: 827-118-145
2025-06-26 12:14:17,343 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 12:14:17,350 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:17] "GET / HTTP/1.1" 200 -
2025-06-26 12:14:18,456 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9'}
2025-06-26 12:14:18,458 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:18] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-26 12:14:32,746 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9'}
2025-06-26 12:14:34,152 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 12:14:34,153 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 12:14:34,153 - twilio.http_client - INFO - Headers:
2025-06-26 12:14:34,153 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 12:14:34,153 - twilio.http_client - INFO - Accept : application/json
2025-06-26 12:14:34,153 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 12:14:34,153 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 12:14:34,153 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 12:14:34,153 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 12:14:35,180 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 12:14:35,180 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 06:44:35 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ32f5f8c7f860bd098dbede43e37df2a5', 'Twilio-Request-Duration': '0.156', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 6504d64256258122cb818ccf9402cf1a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '1CLfGqnbr1-51QZX7AZjg1qk7u7paGyxr6PUFu_OgQtWWCa6KfVb6g==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 12:14:35,181 - root - INFO - OTP sent to +918923542758, Twilio SID: SM32f5f8c7f860bd098dbede43e37df2a5
2025-06-26 12:14:35,186 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:35] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 12:14:46,620 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9'}
2025-06-26 12:14:47,135 - root - INFO - Deleted OTP record for mobile: +918923542758
2025-06-26 12:14:47,650 - root - INFO - User logged in - User ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:14:47,650 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:14:47,908 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:47] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-26 12:14:47,919 - root - INFO - Request path: /auth/complete-profile, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:14:47,920 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:14:48,177 - root - INFO - login_required - User is logged in with ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:14:48,446 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:48] "GET /auth/complete-profile HTTP/1.1" 200 -
2025-06-26 12:14:48,495 - root - INFO - Request path: /static/css/style.css, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:14:48,498 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:14:48] "[33mGET /static/css/style.css HTTP/1.1[0m" 404 -
2025-06-26 12:15:13,952 - root - INFO - Request path: /auth/complete-profile, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:15:13,953 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:15:14,213 - root - INFO - login_required - User is logged in with ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:14,475 - root - INFO - Updated user info for user 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:14,478 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:14] "[32mPOST /auth/complete-profile HTTP/1.1[0m" 302 -
2025-06-26 12:15:14,487 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 12:15:14,490 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:14] "GET /connect HTTP/1.1" 200 -
2025-06-26 12:15:27,870 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 12:15:27,871 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:27] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 12:15:27,880 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 12:15:27,880 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-26 12:15:28,147 - root - INFO - login_required - User is logged in with ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:28,425 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:28] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 12:15:35,189 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:15:35,189 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:15:35,444 - root - INFO - login_required - User is logged in with ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:35,955 - root - INFO - Attempting to send Telegram notification for user: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:35,955 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-26 12:15:35,955 - root - INFO - Sending Telegram notification to chat_id: 5864858511
2025-06-26 12:15:36,628 - root - INFO - Telegram notification sent successfully: {'ok': True, 'result': {'message_id': 412, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 5864858511, 'first_name': 'Hani', 'last_name': 'Tomar', 'type': 'private'}, 'date': 1750920336, 'text': '🎉 New Subscription!\n\nUser: Hani Tomar (+918923542758)\nPlan: Pro\nPrice: $24.99\nTokens: 1000', 'entities': [{'offset': 40, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 12:15:36,629 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 5864858511
2025-06-26 12:15:37,143 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 5864858511
2025-06-26 12:15:37,146 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:37] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 12:15:37,153 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-26 12:15:37,153 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-26 12:15:37,409 - root - INFO - login_required - User is logged in with ID: 685ce9d9d9149ab3af7adaf3
2025-06-26 12:15:37,666 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:15:37] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 12:16:09,257 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:16:09,257 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '4b1fcd2032fa7fad9034522ac9b715febd0447a9', 'user_id': '685ce9d9d9149ab3af7adaf3'}
2025-06-26 12:16:09,257 - root - INFO - Session after logout: {'_permanent': True}
2025-06-26 12:16:09,258 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:16:09] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-26 12:16:09,272 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 12:16:09,276 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:16:09] "GET / HTTP/1.1" 200 -
2025-06-26 12:19:47,682 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 12:19:48,232 - werkzeug - INFO -  * Restarting with stat
2025-06-26 12:19:48,638 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 12:19:48,645 - werkzeug - INFO -  * Debugger PIN: 827-118-145
2025-06-26 12:20:23,333 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca'}
2025-06-26 12:20:23,340 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:20:23] "GET / HTTP/1.1" 200 -
2025-06-26 12:25:40,680 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-06-26 12:25:41,246 - werkzeug - INFO -  * Restarting with stat
2025-06-26 12:25:41,569 - werkzeug - WARNING -  * Debugger is active!
2025-06-26 12:25:41,571 - werkzeug - INFO -  * Debugger PIN: 827-118-145
2025-06-26 12:26:47,462 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca'}
2025-06-26 12:26:47,468 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:26:47] "GET / HTTP/1.1" 200 -
2025-06-26 12:26:53,183 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca'}
2025-06-26 12:26:54,700 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-26 12:26:54,700 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-26 12:26:54,700 - twilio.http_client - INFO - Headers:
2025-06-26 12:26:54,700 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-26 12:26:54,700 - twilio.http_client - INFO - Accept : application/json
2025-06-26 12:26:54,700 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-26 12:26:54,700 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-26 12:26:54,700 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-26 12:26:54,700 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-26 12:26:55,658 - twilio.http_client - INFO - Response Status Code: 201
2025-06-26 12:26:55,658 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 26 Jun 2025 06:56:55 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQcb5aa9c62f0df3d54a716942a888e2cc', 'Twilio-Request-Duration': '0.147', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 1bf9bb4ffb8bb50e9e8dab77ca8a313e.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '2K2n_eINgaGghloN4hyOgr8dNNrMUzFay2cFCTDUcGlM2kliEqaz8A==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-26 12:26:55,659 - root - INFO - OTP sent to +************, Twilio SID: SMcb5aa9c62f0df3d54a716942a888e2cc
2025-06-26 12:26:55,666 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:26:55] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-26 12:27:11,134 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca'}
2025-06-26 12:27:11,732 - root - INFO - Deleted OTP record for mobile: +************
2025-06-26 12:27:12,347 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 12:27:12,348 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:12,656 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:12] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-26 12:27:12,664 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:12,668 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:12] "GET /connect HTTP/1.1" 200 -
2025-06-26 12:27:15,685 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:15,687 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:15] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 12:27:15,697 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:15,697 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:15,943 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 12:27:16,201 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:16] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 12:27:20,519 - root - INFO - Request path: /subscription/subscribe, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:20,519 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:27:20,812 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 12:27:21,563 - root - INFO - Attempting to send Telegram notification for user: 685cc0e0d9149ab3af7adaf1
2025-06-26 12:27:21,564 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-26 12:27:21,564 - root - INFO - Sending subscription notification to chat_id: 7644343290
2025-06-26 12:27:22,331 - root - INFO - Subscription notification sent successfully: {'ok': True, 'result': {'message_id': 413, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750921042, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Pro\nPrice: $24.99\nTokens: 1000', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-26 12:27:22,331 - root - INFO - Sending moderation request to chat_id: 7644343290
2025-06-26 12:27:23,469 - root - INFO - Moderation request sent successfully: {'ok': True, 'result': {'message_id': 414, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1750921043, 'text': '📝 Please share your post for moderation.\n\nYour subscription is now active. You can now share your post for review and publishing.'}}
2025-06-26 12:27:23,470 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 12:27:23,972 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 7644343290
2025-06-26 12:27:23,974 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:23] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-26 12:27:23,983 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-26 12:27:23,984 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-26 12:27:24,229 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-26 12:27:24,476 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:27:24] "GET /subscription/ HTTP/1.1" 200 -
2025-06-26 12:28:10,791 - root - INFO - Request path: /auth/logout, Session: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:28:10,791 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '5738e04b85b6fa7d6b01c5038f712ab47bc68cca', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-26 12:28:10,791 - root - INFO - Session after logout: {'_permanent': True}
2025-06-26 12:28:10,793 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:28:10] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-26 12:28:10,803 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 12:28:10,805 - werkzeug - INFO - 1******** - - [26/Jun/2025 12:28:10] "GET / HTTP/1.1" 200 -
2025-06-26 13:35:16,386 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-26 13:35:16,450 - werkzeug - INFO - 1******** - - [26/Jun/2025 13:35:16] "GET / HTTP/1.1" 200 -
2025-06-26 13:35:19,141 - root - INFO - Request path: /subscription, Session: {'_permanent': True, 'csrf_token': '7cd1ab13c74a64fca2e0889fececd1df54e14919'}
2025-06-26 13:35:19,148 - werkzeug - INFO - 1******** - - [26/Jun/2025 13:35:19] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-26 13:35:19,175 - root - INFO - Request path: /subscription/, Session: {'_permanent': True, 'csrf_token': '7cd1ab13c74a64fca2e0889fececd1df54e14919'}
2025-06-26 13:35:19,176 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '7cd1ab13c74a64fca2e0889fececd1df54e14919'}
2025-06-26 13:35:19,177 - root - ERROR - login_required - No user_id in session
2025-06-26 13:35:19,194 - werkzeug - INFO - 1******** - - [26/Jun/2025 13:35:19] "[32mGET /subscription/ HTTP/1.1[0m" 302 -
2025-06-26 13:35:19,209 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '7cd1ab13c74a64fca2e0889fececd1df54e14919', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-26 13:35:19,231 - werkzeug - INFO - 1******** - - [26/Jun/2025 13:35:19] "GET /auth/send-otp?next=http://1********:5000/subscription/ HTTP/1.1" 200 -
2025-06-27 15:22:25,072 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-27 15:22:25,072 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 15:22:25,073 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:22:25,395 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:22:25,402 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:22:28,338 - root - INFO - Request path: /, Session: {'_permanent': True}
2025-06-27 15:22:28,344 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:22:28] "GET / HTTP/1.1" 200 -
2025-06-27 15:22:30,512 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d'}
2025-06-27 15:22:30,515 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:22:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-27 15:22:40,886 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d'}
2025-06-27 15:22:42,741 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 15:22:42,741 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 15:22:42,741 - twilio.http_client - INFO - Headers:
2025-06-27 15:22:42,741 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 15:22:42,742 - twilio.http_client - INFO - Accept : application/json
2025-06-27 15:22:42,742 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 15:22:42,742 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 15:22:42,742 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 15:22:42,742 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 15:22:43,297 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 15:22:43,297 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 09:52:43 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ3a6b17d4c682b78db7fe9da311c977eb', 'Twilio-Request-Duration': '0.166', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 bf084b319ce7781c125b74c71a495762.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'DLBRGPk8zIBv5dyEL7wcQ5Qo3EpqUgjariM7AHwDP1KW53ckgRCEiA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 15:22:43,298 - root - INFO - OTP sent to +************, Twilio SID: SM3a6b17d4c682b78db7fe9da311c977eb
2025-06-27 15:22:43,303 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:22:43] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 15:23:01,011 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d'}
2025-06-27 15:23:01,806 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 15:23:02,523 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:23:02,523 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:23:02,831 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:23:02] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 15:23:02,838 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:23:02,841 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:23:02] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:24:46,745 - root - INFO - Request path: /facebook/login, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:24:46,745 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:24:47,072 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:24:47,072 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:24:47,074 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:24:47] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 15:24:53,226 - root - INFO - Request path: /facebook/callback, Session: {'_permanent': True}
2025-06-27 15:24:53,227 - root - INFO - Facebook callback - Session at start: {'_permanent': True}
2025-06-27 15:24:53,227 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:24:54,160 - root - INFO - Using user_id from state parameter: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:24:54,160 - root - INFO - Facebook callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:24:54,160 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:24:54,421 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e6a6f7241071421690081
2025-06-27 15:24:54,423 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:24:54] "GET /facebook/callback?code=AQBLeKlhn-TYNjPPgYNOhZmgw3GcVGKlJ8Wyxv7p3roDwA07ZpWGz0J_OGxULbZY8qfutdOByPQT9Wa9Y7LnXC8VTzgVd23bepks1FjGtN6Atc-535CndwDGEjMrMuJ50IL7TaxSjDy8MwzLsZh-hF_SHhTIkXLCCs7iUijSVpkrTtxgZXd2hL3t16ax409aahRcp1MjGx9bg_PRQjyqvKtBeuRyhxb5Ncgcs9IHZ4F5htNwrrU8VG95AUwM0JKQjzlRi6SmsZnxxNeG1WtWq4b2WfTnuUJlb1s0QoQMSgHst9F1rMxyjHIK1nq5V1sCxmlrgpSI9cKYtj32tt0BMkJs0nWDjm2nbprBdCzp5DQzXQPO6z0T8jcXf7w3u_jfyhU&state=agenticoauth_685cc0e0d9149ab3af7adaf1 HTTP/1.1" 200 -
2025-06-27 15:24:55,441 - root - INFO - Request path: /favicon.ico, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 15:24:55,443 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:24:55] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-27 15:27:09,793 - root - INFO - Request path: /, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:27:09,800 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:27:09] "GET / HTTP/1.1" 200 -
2025-06-27 15:27:12,217 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:27:12,219 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:27:12] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:27:22,372 - root - INFO - Request path: /linkedin/login, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:27:22,373 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:27:22,701 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:27:22,702 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:27:22,703 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:27:22] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-06-27 15:28:10,802 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-27 15:28:11,478 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:28:11,956 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:28:11,963 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:28:20,009 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-06-27 15:28:20,523 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:28:20,895 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:28:20,897 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:28:27,942 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-27 15:28:28,526 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:28:29,052 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:28:29,055 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:28:35,117 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-27 15:28:35,601 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:28:36,040 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:28:36,043 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:28:37,058 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-27 15:28:37,645 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:28:38,085 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:28:38,088 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:29:18,360 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-06-27 15:29:18,726 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:29:19,175 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:29:19,178 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:29:22,865 - root - INFO - Request path: /connect, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:29:22,872 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:22] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:29:25,832 - root - INFO - Request path: /twitter/login, Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:29:25,832 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:29:26,954 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:29:26,954 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:29:26,956 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:26] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-27 15:29:32,148 - root - INFO - Request path: /twitter/callback, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 15:29:32,148 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 15:29:32,148 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:29:33,039 - root - INFO - Using user_id from state parameter: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:29:33,039 - root - INFO - Twitter callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:29:33,039 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:29:33,307 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e6b857241071421690498
2025-06-27 15:29:33,310 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:33] "GET /twitter/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-27 15:29:39,837 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')]}
2025-06-27 15:29:39,839 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:39] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-27 15:29:39,852 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')]}
2025-06-27 15:29:39,857 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:39] "GET / HTTP/1.1" 200 -
2025-06-27 15:29:50,388 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1'}
2025-06-27 15:29:50,390 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:50] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-27 15:29:50,401 - root - INFO - Request path: /, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1'}
2025-06-27 15:29:50,403 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:29:50] "GET / HTTP/1.1" 200 -
2025-06-27 15:33:34,763 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-27 15:33:35,265 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:33:35,777 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:33:35,783 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:33:37,805 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-06-27 15:33:38,342 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:33:38,665 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:33:38,668 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:33:44,456 - root - INFO - Request path: /auth/send-otp, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1'}
2025-06-27 15:33:46,083 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 15:33:46,083 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 15:33:46,083 - twilio.http_client - INFO - Headers:
2025-06-27 15:33:46,083 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 15:33:46,083 - twilio.http_client - INFO - Accept : application/json
2025-06-27 15:33:46,083 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 15:33:46,083 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 15:33:46,084 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 15:33:46,084 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 15:33:46,510 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 15:33:46,511 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 10:03:47 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ65bae267400ce1d423444a7dbf446f32', 'Twilio-Request-Duration': '0.156', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 31d74c8d65c5b9b59345d2b28c72aa24.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'Kj5oBLDgthzZjGgZFdUZ-pOOW7FUwaq5RLCMnEA9srS5BFg5VtKGyA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 15:33:46,511 - root - INFO - OTP sent to +************, Twilio SID: SM65bae267400ce1d423444a7dbf446f32
2025-06-27 15:33:46,521 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:33:46] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 15:33:58,341 - root - INFO - Request path: /auth/verify-otp, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1'}
2025-06-27 15:33:59,003 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 15:33:59,822 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:33:59,822 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:00,093 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:34:00] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 15:34:00,102 - root - INFO - Request path: /connect, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:00,107 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:34:00] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:34:02,272 - root - INFO - Request path: /facebook/login, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:02,272 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:02,587 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:34:02,587 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:02,590 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:34:02] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 15:34:07,371 - root - INFO - Request path: /facebook/callback, Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:07,371 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:34:07,373 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:34:08,123 - root - INFO - Facebook callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:34:08,123 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:34:08,382 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:34:08,382 - root - ERROR - Error in Facebook callback: cannot access local variable 'flash' where it is not associated with a value
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py", line 121, in callback
    flash("Facebook account connected successfully!")
    ^^^^^
UnboundLocalError: cannot access local variable 'flash' where it is not associated with a value
2025-06-27 15:34:08,384 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:34:08] "[35m[1mGET /facebook/callback?code=AQDU9D-chVOY90ROeChpgJZKzDKLVwAgHfWDozKhzSf1UASVTJ6zQMzuvwIa85Tq304y-sA6hpymasHTtyxtDwtSl3gBahNtJxptZC_YDKNYNnC-DAbJ2eyUqM4HahzSC0hU0nanV4zUQOVPebtrgjdaaY-ATGXwot-INblBxA8e_6TSV3WQ5rrFjdGl2GszABt4ClpNKc4L-X32msBZYV9hBeuaPFJCEsgzT0bBMLt3YK1vdP6BQmyUZfoi3mg79fr0kgDRsKPrSQ16uUtghaZMRvFodJT_ctinpeX_VWn0voJ0lqXSCs9RA8Sjhxd8IyweiVrgiFtkutTRr3RneU0hvAE_dv16QTmDIF71EJ1lxwXkuEcoI6fP3EpHiNM4oLo&state=agenticoauth_685cc0e0d9149ab3af7adaf1 HTTP/1.1[0m" 500 -
2025-06-27 15:35:01,273 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-27 15:35:01,741 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:35:02,270 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:35:02,273 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:35:14,754 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 15:35:14,754 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:14,755 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:35:14,755 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:14,755 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:35:15,510 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:35:15] "[31m[1mGET /facebook/callback?code=AQDU9D-chVOY90ROeChpgJZKzDKLVwAgHfWDozKhzSf1UASVTJ6zQMzuvwIa85Tq304y-sA6hpymasHTtyxtDwtSl3gBahNtJxptZC_YDKNYNnC-DAbJ2eyUqM4HahzSC0hU0nanV4zUQOVPebtrgjdaaY-ATGXwot-INblBxA8e_6TSV3WQ5rrFjdGl2GszABt4ClpNKc4L-X32msBZYV9hBeuaPFJCEsgzT0bBMLt3YK1vdP6BQmyUZfoi3mg79fr0kgDRsKPrSQ16uUtghaZMRvFodJT_ctinpeX_VWn0voJ0lqXSCs9RA8Sjhxd8IyweiVrgiFtkutTRr3RneU0hvAE_dv16QTmDIF71EJ1lxwXkuEcoI6fP3EpHiNM4oLo&state=agenticoauth_685cc0e0d9149ab3af7adaf1 HTTP/1.1[0m" 400 -
2025-06-27 15:35:28,016 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:35:28,016 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:28,016 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:35:28,016 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:28,023 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:35:28] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:35:29,712 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 15:35:29,712 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:29,712 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:35:29,712 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:30,803 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:35:30,803 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:30,805 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:35:30] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 15:35:35,629 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 15:35:35,630 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:35,630 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:35:35,630 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:35:35,630 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:35:36,422 - root - INFO - Facebook callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:35:36,423 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:35:36,691 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:35:36,691 - root - ERROR - Error in Facebook callback: cannot access local variable 'flash' where it is not associated with a value
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py", line 121, in callback
    flash("Facebook account connected successfully!")
    ^^^^^
UnboundLocalError: cannot access local variable 'flash' where it is not associated with a value
2025-06-27 15:35:36,694 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:35:36] "[35m[1mGET /facebook/callback?code=AQDP_nofswAI1OypILKXi7ZhN7jj7biQWsSNRuYzh9AI_HoobBXjwP4eecmgjgiTQPH5ozhVE3_7hgDUb2asT9swEwC8pdLQ7QxO1fkCjyfDZxsrzbLDZ3uLpUfsU4kQUh4ZW14_OjLEWdNrABvYy59n3ZpCpYCEwQx8zF2_X9WdtVNsMTytIPielGDYbDSPKeamUb-10kipyEltDoLa9yif1ZVB3hBfrPTL9z91r8g5r9rkCYVMZSB_Y6k5-s668_jo16cZ-RMvl29RQLwA-HqcHE1ZEhgL4MTTaVfOLtfie3m5GgIw7BM_XbbA5cOhfW68l3ktw1MUviJCR0oClOxFEGs76pc-173N_nLkaVjuT6ZeWh6FENfHPhMjPBC_jFA&state=agenticoauth_685cc0e0d9149ab3af7adaf1 HTTP/1.1[0m" 500 -
2025-06-27 15:36:13,698 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-27 15:36:14,140 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:36:14,528 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:36:14,531 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:36:17,552 - root - INFO - Request path: /, Method: GET
2025-06-27 15:36:17,552 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:17,552 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:36:17,560 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:36:17] "GET / HTTP/1.1" 200 -
2025-06-27 15:36:19,027 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:36:19,028 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:19,028 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:36:19,028 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:19,033 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:36:19] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:36:20,807 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 15:36:20,807 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:20,807 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:36:20,807 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:21,825 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:36:21,826 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:21,827 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:36:21] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 15:36:26,603 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 15:36:26,604 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:26,604 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:36:26,604 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:26,605 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:36:27,396 - root - INFO - Facebook callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:36:27,396 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:36:27,646 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:36:27,648 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:36:27] "GET /facebook/callback?code=AQDnNdm1ICiznqdxs3NNEDKOajJexytsdSuSKEfIIR6jTrNeIKz_fhnqi1Wu8ukCrzQv_KJttMbYkzmnZIBvyp42oRjaSQOUbmWPQiBZNZmwCxxGet7tpCkqwjSqgnEfqus9oQHS18XPC38Z5sZ2JeCj7-LgOZCXUm8a2_YN3L7rsWcFJ-JAmGHuuTdhwvp2FRcUHKAaxPgZ2fKuEFEa9uoi69Yw1We1eDSpiTsHQk41zOqUV3qByCHaJA2dQ6t1UEdbDjasNlasAORA9s-sev9NN0l3VGPPclXtcY4Mxkw5DYzdgA0fe5HVbZicBLEslIgH1hA7JjJ5ajNFebucWkMS8fFaX6Y-AYyEBY8_z81dx3v1nCLcUKf_Qh9rWjTKMLE&state=agenticoauth_685cc0e0d9149ab3af7adaf1 HTTP/1.1" 200 -
2025-06-27 15:36:30,512 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:36:30,512 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:30,512 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:36:30,512 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:36:30,513 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:36:30] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:38:05,166 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-27 15:38:05,610 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:38:05,982 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:38:05,986 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:38:35,168 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-27 15:38:35,496 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:39:35,415 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-27 15:39:35,415 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 15:39:35,416 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:39:35,709 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:39:35,712 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:39:37,308 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:39:37,308 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:37,308 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:39:37,308 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:38,458 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:39:38,458 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:38,458 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:39:38,464 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:39:38] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:39:40,256 - root - INFO - Request path: /twitter/login, Method: GET
2025-06-27 15:39:40,256 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:40,256 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:39:40,257 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:40,521 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:39:40,521 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:40,523 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:39:40] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-27 15:39:45,350 - root - INFO - Request path: /twitter/callback, Method: GET
2025-06-27 15:39:45,350 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:45,351 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:39:45,351 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:39:45,352 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:39:46,278 - root - INFO - Twitter callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:39:46,278 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:39:46,541 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:39:46,541 - root - ERROR - Error in Twitter callback: name 'flash' is not defined
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py", line 131, in callback
    flash("Twitter account connected successfully!")
    ^^^^^
NameError: name 'flash' is not defined. Did you mean: 'hash'?
2025-06-27 15:39:46,546 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:39:46] "[35m[1mGET /twitter/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=******************************************************************************************* HTTP/1.1[0m" 500 -
2025-06-27 15:40:35,061 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-06-27 15:40:35,282 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:40:35,676 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:40:35,680 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:40:41,700 - root - INFO - Request path: /twitter/callback, Method: GET
2025-06-27 15:40:41,700 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:41,700 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:40:41,700 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:41,701 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:42,152 - root - ERROR - Error in Twitter callback: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py", line 79, in callback
    response.raise_for_status()
  File "/home/<USER>/Desktop/agentic_oauth_codebase/venv/lib/python3.12/site-packages/requests/models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api.twitter.com/2/oauth2/token
2025-06-27 15:40:42,156 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:40:42] "[35m[1mGET /twitter/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=******************************************************************************************* HTTP/1.1[0m" 500 -
2025-06-27 15:40:48,040 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:40:48,040 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:48,040 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:40:48,041 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:49,072 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:49,073 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:49,073 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:49,080 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:40:49] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:40:50,352 - root - INFO - Request path: /twitter/login, Method: GET
2025-06-27 15:40:50,352 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:50,352 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:40:50,353 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:50,633 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:50,634 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:50,636 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:40:50] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-27 15:40:55,730 - root - INFO - Request path: /twitter/callback, Method: GET
2025-06-27 15:40:55,730 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:55,730 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:40:55,730 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:55,730 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:56,616 - root - INFO - Twitter callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:40:56,617 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:40:56,870 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:40:56,873 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:40:56] "GET /twitter/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-27 15:40:59,502 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:40:59,503 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:59,503 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:40:59,503 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:59,753 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:59,754 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:40:59,754 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:40:59,757 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:40:59] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:41:02,571 - root - INFO - Request path: /youtube/login, Method: GET
2025-06-27 15:41:02,571 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:41:02,571 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:41:02,571 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:41:02,822 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:41:02,822 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:41:02,824 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:41:02] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-27 15:41:13,550 - root - INFO - Request path: /youtube/callback, Method: GET
2025-06-27 15:41:13,551 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:41:13,552 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:41:13,552 - root - INFO - YouTube callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:41:13,553 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:41:14,021 - root - INFO - YouTube callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:41:14,021 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:41:14,272 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e6e427241071421690ed9
2025-06-27 15:41:14,273 - root - ERROR - Error in YouTube callback: name 'url_for' is not defined
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py", line 141, in callback
    <a href="{url_for('connect')}" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background-color: #FF0000; color: white; text-decoration: none; border-radius: 5px;">Back to Connect Page</a>
              ^^^^^^^
NameError: name 'url_for' is not defined
2025-06-27 15:41:14,277 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:41:14] "[35m[1mGET /youtube/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=4/0AVMBsJjFPv97NWTQj_KCnNYxI6Eai2wwifTZOfh8HoqODWnFCXQ0cqO9tCfFwrAQ5IESNQ&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1[0m" 500 -
2025-06-27 15:42:02,235 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-27 15:42:02,742 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:42:03,135 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:42:03,138 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:42:13,083 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:42:13,083 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:13,083 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:42:13,084 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:14,278 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:14,278 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:14,278 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:14,285 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:42:14] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:42:20,613 - root - INFO - Request path: /youtube/login, Method: GET
2025-06-27 15:42:20,613 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:20,613 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:42:20,613 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:20,967 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:20,968 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:20,970 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:42:20] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-27 15:42:32,709 - root - INFO - Request path: /youtube/callback, Method: GET
2025-06-27 15:42:32,709 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:32,709 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:42:32,709 - root - INFO - YouTube callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:32,710 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:33,727 - root - INFO - YouTube callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:42:33,727 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:42:33,996 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:42:33,998 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:42:33] "GET /youtube/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=4/0AVMBsJhdP2nppD3eOdHUlvVOMfmsxWOvroyU0KY764FDx4O486B69xrs5pzaLiCYrsUKeg&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1" 200 -
2025-06-27 15:42:36,693 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:42:36,693 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:36,694 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:42:36,694 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:37,071 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:37,072 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:42:37,072 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:42:37,073 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:42:37] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:43:34,945 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:43:34,945 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:34,945 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:43:34,945 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:35,309 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:43:35,310 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:35,310 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:43:35,312 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:43:35] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:43:38,919 - root - INFO - Request path: /subs, Method: GET
2025-06-27 15:43:38,919 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:38,920 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:43:38,921 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:43:38] "[33mGET /subs HTTP/1.1[0m" 404 -
2025-06-27 15:43:44,529 - root - INFO - Request path: /subscriptions, Method: GET
2025-06-27 15:43:44,529 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:44,529 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:43:44,530 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:43:44] "[33mGET /subscriptions HTTP/1.1[0m" 404 -
2025-06-27 15:43:48,063 - root - INFO - Request path: /, Method: GET
2025-06-27 15:43:48,063 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:43:48,064 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:43:48,066 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:43:48] "GET / HTTP/1.1" 200 -
2025-06-27 15:46:08,466 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-06-27 15:46:08,815 - werkzeug - INFO -  * Restarting with stat
2025-06-27 15:46:09,251 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 15:46:09,254 - werkzeug - INFO -  * Debugger PIN: 184-415-897
2025-06-27 15:46:17,374 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-06-27 15:46:17,374 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:17,374 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:18,926 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 15:46:18,926 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 15:46:18,926 - twilio.http_client - INFO - Headers:
2025-06-27 15:46:18,926 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 15:46:18,926 - twilio.http_client - INFO - Accept : application/json
2025-06-27 15:46:18,926 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 15:46:18,926 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 15:46:18,927 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 15:46:18,927 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 15:46:19,979 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 15:46:19,980 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 10:16:20 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ193ad6d51209e7382f497aecce984f73', 'Twilio-Request-Duration': '0.156', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 e5ecb7c664172229c759168f606959ba.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'p1eOyy_1ml4lsgMiOxBGBK1jb3eQtYi90Wa4EQvi1e5YSGRm51sZPw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 15:46:19,980 - root - INFO - OTP sent to +************, Twilio SID: SM193ad6d51209e7382f497aecce984f73
2025-06-27 15:46:19,989 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:19] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 15:46:31,653 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-06-27 15:46:31,653 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:31,654 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:32,256 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 15:46:32,829 - root - INFO - User logged in - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:32,829 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:33,088 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:33] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 15:46:33,097 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:46:33,098 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:33,098 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:33,098 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:33,383 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:33,384 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:33,384 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:33,391 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:33] "GET /connect HTTP/1.1" 200 -
2025-06-27 15:46:37,075 - root - INFO - Request path: /youtube/login, Method: GET
2025-06-27 15:46:37,075 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:37,075 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:37,075 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:37,333 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:37,333 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:37,335 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:37] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-27 15:46:52,955 - root - INFO - Request path: /youtube/callback, Method: GET
2025-06-27 15:46:52,955 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:52,956 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:52,956 - root - INFO - YouTube callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:52,956 - root - INFO - Extracted user_id from state: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:53,432 - root - INFO - YouTube callback - Using user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'str'>
2025-06-27 15:46:53,432 - root - INFO - Saving profile with user_id: 685cc0e0d9149ab3af7adaf1 of type <class 'bson.objectid.ObjectId'>
2025-06-27 15:46:53,686 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 15:46:53,689 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:53] "GET /youtube/callback?state=agenticoauth_685cc0e0d9149ab3af7adaf1&code=4/0AVMBsJimAu8FvcCh0nFTMrENbvcZRfYyZ4H71jSwht5AdSGBnyLdLJvCdq3VFMDPRppo0g&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1" 200 -
2025-06-27 15:46:55,763 - root - INFO - Request path: /connect, Method: GET
2025-06-27 15:46:55,764 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:55,764 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:46:55,764 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:56,017 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:56,017 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:46:56,017 - root - INFO - Connect route - User ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:46:56,018 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:46:56] "GET /connect?csrf_token=213e64137803eb5a6bcee17744116c40ce7a15e1 HTTP/1.1" 200 -
2025-06-27 15:47:09,685 - root - INFO - Request path: /subscriptions, Method: GET
2025-06-27 15:47:09,686 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': '213e64137803eb5a6bcee17744116c40ce7a15e1', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:47:09,686 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ojGlKLRb2qAAaxjF4N056ZTkehnHIydyGIv9Fslfcdo.wA1Io0yrYOYbO_LwNpaE8iJhGNE')])
2025-06-27 15:47:09,689 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:47:09] "[33mGET /subscriptions HTTP/1.1[0m" 404 -
2025-06-27 15:47:13,007 - root - INFO - Request path: /, Method: GET
2025-06-27 15:47:13,007 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:47:13,007 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:47:13,010 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:47:13] "GET / HTTP/1.1" 200 -
2025-06-27 15:47:16,046 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 15:47:16,046 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:47:16,046 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:47:16,048 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:47:16] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 15:47:16,061 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 15:47:16,061 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:47:16,061 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 15:47:16,061 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 15:47:16,342 - root - INFO - login_required - User is logged in with ID: 685cc0e0d9149ab3af7adaf1
2025-06-27 15:47:16,696 - werkzeug - INFO - 1******** - - [27/Jun/2025 15:47:16] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 16:16:06,111 - root - INFO - Request path: /auth/logout, Method: GET
2025-06-27 16:16:06,112 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 16:16:06,112 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UQQekqstoB8K9kJkZsUko1qeEJmWBf-1O0NBURMRWSw.-a3iaFM5Ze0qgAcoykXjmqF1BSk')])
2025-06-27 16:16:06,112 - root - INFO - Logout called - Session before logout: {'_permanent': True, 'csrf_token': '06b225bb31c5823ceea06811ac5e80227eb3ad7d', 'user_id': '685cc0e0d9149ab3af7adaf1'}
2025-06-27 16:16:06,113 - root - INFO - Session after logout: {'_permanent': True}
2025-06-27 16:16:06,117 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:16:06] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-27 16:16:06,144 - root - INFO - Request path: /, Method: GET
2025-06-27 16:16:06,145 - root - INFO - Session data: {'_permanent': True}
2025-06-27 16:16:06,145 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-06-27 16:16:06,149 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:16:06] "GET / HTTP/1.1" 200 -
2025-06-27 16:34:15,474 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 16:34:15,476 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016'}
2025-06-27 16:34:15,479 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:15,488 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:15] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 16:34:15,530 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 16:34:15,530 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016'}
2025-06-27 16:34:15,530 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:15,530 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016'}
2025-06-27 16:34:15,530 - root - ERROR - login_required - No user_id in session
2025-06-27 16:34:15,532 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:15] "[32mGET /subscription/ HTTP/1.1[0m" 302 -
2025-06-27 16:34:15,542 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-06-27 16:34:15,542 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-27 16:34:15,542 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:15,544 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:15] "GET /auth/send-otp?next=http://1********:5000/subscription/ HTTP/1.1" 200 -
2025-06-27 16:34:21,161 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-06-27 16:34:21,161 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-27 16:34:21,162 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:22,714 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 16:34:22,714 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 16:34:22,715 - twilio.http_client - INFO - Headers:
2025-06-27 16:34:22,715 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 16:34:22,715 - twilio.http_client - INFO - Accept : application/json
2025-06-27 16:34:22,715 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 16:34:22,715 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 16:34:22,715 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 16:34:22,715 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 16:34:25,538 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 16:34:25,538 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 11:04:25 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ8e6aa594c2e024bd6831f6fb9df31ffa', 'Twilio-Request-Duration': '0.136', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c13661f9455806125a56201b0ccd2b96.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'LL6VxuY4jpIWz4GLpEcQ3HtKDXf-Sg_3vaNHuGq4cgxuueT5yEZzSg==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 16:34:25,539 - root - INFO - OTP sent to +************, Twilio SID: SM8e6aa594c2e024bd6831f6fb9df31ffa
2025-06-27 16:34:25,542 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:25] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 16:34:36,894 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-06-27 16:34:36,894 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')]}
2025-06-27 16:34:36,895 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:37,514 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 16:34:38,129 - root - INFO - User logged in - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:34:38,129 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:34:38,431 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:38] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 16:34:38,443 - root - INFO - Request path: /auth/complete-profile, Method: GET
2025-06-27 16:34:38,444 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:34:38,444 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:38,444 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', '_flashes': [('message', 'Please log in to access this page')], 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:34:38,734 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:34:39,035 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:39] "GET /auth/complete-profile HTTP/1.1" 200 -
2025-06-27 16:34:39,087 - root - INFO - Request path: /static/css/style.css, Method: GET
2025-06-27 16:34:39,088 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:34:39,088 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:34:39,091 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:34:39] "[33mGET /static/css/style.css HTTP/1.1[0m" 404 -
2025-06-27 16:35:17,764 - root - INFO - Request path: /auth/complete-profile, Method: POST
2025-06-27 16:35:17,765 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:35:17,765 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:17,766 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:35:18,092 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:18,412 - root - INFO - Updated user info for user 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:18,413 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:18] "[32mPOST /auth/complete-profile HTTP/1.1[0m" 302 -
2025-06-27 16:35:18,421 - root - INFO - Request path: /connect, Method: GET
2025-06-27 16:35:18,421 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:18,422 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:18,422 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:18,835 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:18,836 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:18,836 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:18,839 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:18] "GET /connect HTTP/1.1" 200 -
2025-06-27 16:35:23,168 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 16:35:23,168 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:23,168 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:23,171 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:23] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 16:35:23,191 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 16:35:23,192 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:23,192 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:23,192 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 16:35:23,523 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:23,893 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:23] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 16:35:31,191 - root - INFO - Request path: /subscription/subscribe, Method: POST
2025-06-27 16:35:31,192 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:35:31,192 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:31,194 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:35:31,480 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:32,054 - root - INFO - Attempting to send Telegram notification for user: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:32,055 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-27 16:35:32,055 - root - INFO - Sending subscription notification to chat_id: 7644343290
2025-06-27 16:35:33,006 - root - INFO - Subscription notification sent successfully: {'ok': True, 'result': {'message_id': 517, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1751022332, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Pro\nPrice: $24.99\nTokens: 1000', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-27 16:35:33,007 - root - INFO - Sending moderation request to chat_id: 7644343290
2025-06-27 16:35:34,138 - root - INFO - Moderation request sent successfully: {'ok': True, 'result': {'message_id': 518, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1751022333, 'text': '📝 Please share your post for moderation.\n\nYour subscription is now active. You can now share your post for review and publishing.'}}
2025-06-27 16:35:34,138 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 7644343290
2025-06-27 16:35:34,898 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 7644343290
2025-06-27 16:35:34,901 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:34] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-27 16:35:34,913 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 16:35:34,913 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-27 16:35:34,914 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:35:34,914 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-27 16:35:35,200 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:35:35,497 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:35:35] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 16:36:04,560 - root - INFO - Request path: /connect, Method: GET
2025-06-27 16:36:04,560 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:36:04,560 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:36:04,561 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:36:04,848 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:36:04,848 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:36:04,848 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:36:04,850 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:36:04] "GET /connect HTTP/1.1" 200 -
2025-06-27 16:36:23,502 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 16:36:23,503 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:36:23,503 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:36:23,503 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:36:23,904 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:36:24,366 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:36:24] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 16:57:01,965 - root - INFO - Request path: /connect, Method: GET
2025-06-27 16:57:01,973 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:01,975 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:57:01,977 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:02,302 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:57:02,302 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:02,303 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:57:02,339 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:57:02] "GET /connect HTTP/1.1" 200 -
2025-06-27 16:57:05,194 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 16:57:05,195 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:05,195 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'utuuR_Istj5iApTAJ1joDHyHn-rwxbiYGUbdaIYXlUE.FzIXAJxwAaWpVDzunURQNkRs6oM')])
2025-06-27 16:57:05,195 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:05,474 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 16:57:05,475 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': 'e1465af588f9cd26223720468149fcbe39ccf016', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 16:57:05,480 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:57:05] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 16:57:16,071 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 16:57:16,071 - root - INFO - Session data: {'_permanent': True}
2025-06-27 16:57:16,072 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-06-27 16:57:16,072 - root - INFO - Facebook callback - Session at start: {'_permanent': True}
2025-06-27 16:57:16,073 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 16:57:17,254 - root - INFO - Using user_id from state parameter: 685e77f1d9149ab3af7adb5e
2025-06-27 16:57:17,254 - root - INFO - Facebook callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 16:57:17,255 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 16:57:17,552 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e8015724107142169542f
2025-06-27 16:57:17,563 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:57:17] "GET /facebook/callback?code=AQAUP5kWIsx3tyKGeWGhtzEVA6rdPLo1tbaXQOk6XuJxRSmy0OJ9Xpc-OJa0NjoCvGKStEarUjJU25p1sKqQfKSSUdadWiY6XMUZyiS7fvneVH9ds4yvEzdOqZJygnSwBilnE7_xZTAGkwzlEL2e5H3KmgM1SvvtDbeNdh7asYmLG0dR9wsy-REDWScGsVKRe0vwxfxj1RLxr50R8k_-nyTzJgz5hYKuDzgFiPtzOkIl7UsGzt3Le5zhbMjNsVQd9IX-jhSE7Q9xSI-0T5Q97-gNgDE_SAUabBk0hVyucJnIEnWYSLpavmz41c52sxnzAfyrzOCQiqpn8-zp6hIzQSrkFCKOEcNjG7olYqlqYxStRFBr__8Z6wDMX3njOYej-nnJSU4JO6HxlyIO01OduKeeLfOGmAfai3w4J34HwHiq1A&state=agenticoauth_685e77f1d9149ab3af7adb5e HTTP/1.1" 200 -
2025-06-27 16:57:24,689 - root - INFO - Request path: /connect, Method: GET
2025-06-27 16:57:24,689 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 16:57:24,690 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'gCAjadj_jNOxtNPbGrgPZbqNXG0zL1vp-eTFtVrZiuA.78cYbWIdbyaAg8WEHu2UC1fPQN8')])
2025-06-27 16:57:24,690 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 16:57:24,690 - root - ERROR - login_required - No user_id in session
2025-06-27 16:57:24,695 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:57:24] "[32mGET /connect?csrf_token= HTTP/1.1[0m" 302 -
2025-06-27 16:57:24,708 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-06-27 16:57:24,708 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')]}
2025-06-27 16:57:24,708 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'gCAjadj_jNOxtNPbGrgPZbqNXG0zL1vp-eTFtVrZiuA.78cYbWIdbyaAg8WEHu2UC1fPQN8')])
2025-06-27 16:57:24,723 - werkzeug - INFO - 1******** - - [27/Jun/2025 16:57:24] "GET /auth/send-otp?next=http://localhost:5000/connect?csrf_token%3D HTTP/1.1" 200 -
2025-06-27 18:33:51,743 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-27 18:33:51,744 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 18:33:51,745 - werkzeug - INFO -  * Restarting with stat
2025-06-27 18:33:52,094 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 18:33:52,105 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 18:33:56,416 - root - INFO - Request path: /, Method: GET
2025-06-27 18:33:56,416 - root - INFO - Session data: {'_permanent': True}
2025-06-27 18:33:56,416 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-06-27 18:33:56,424 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:33:56] "GET / HTTP/1.1" 200 -
2025-06-27 18:33:57,397 - root - INFO - Request path: /favicon.ico, Method: GET
2025-06-27 18:33:57,398 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3'}
2025-06-27 18:33:57,398 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:33:57,400 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:33:57] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-27 18:33:59,965 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-06-27 18:33:59,965 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3'}
2025-06-27 18:33:59,965 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:34:03,713 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 18:34:03,713 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 18:34:03,713 - twilio.http_client - INFO - Headers:
2025-06-27 18:34:03,714 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 18:34:03,714 - twilio.http_client - INFO - Accept : application/json
2025-06-27 18:34:03,714 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 18:34:03,714 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 18:34:03,714 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 18:34:03,714 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 18:34:04,734 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 18:34:04,734 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 13:04:04 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ992cf22670ce31fe5839972c78d50661', 'Twilio-Request-Duration': '0.156', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 6504d64256258122cb818ccf9402cf1a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '7tRkGPgs5KtUZsEHA9imzy10BkBpr5Rpe4sJY6PAIstCwsPeDbEGHA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 18:34:04,735 - root - INFO - OTP sent to +************, Twilio SID: SM992cf22670ce31fe5839972c78d50661
2025-06-27 18:34:04,741 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:04] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 18:34:21,755 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-06-27 18:34:21,755 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3'}
2025-06-27 18:34:21,755 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:34:22,787 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 18:34:25,500 - root - INFO - User logged in - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:25,500 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:26,314 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:26] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 18:34:26,323 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:34:26,323 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:26,323 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:34:26,323 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:26,752 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:26,752 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:26,753 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:26,757 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:26] "GET /connect HTTP/1.1" 200 -
2025-06-27 18:34:31,515 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 18:34:31,516 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:31,516 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:34:31,516 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:32,762 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:32,763 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:34:32,764 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:32] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 18:34:37,709 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 18:34:37,709 - root - INFO - Session data: {'_permanent': True}
2025-06-27 18:34:37,709 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-06-27 18:34:37,709 - root - INFO - Facebook callback - Session at start: {'_permanent': True}
2025-06-27 18:34:37,709 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:38,609 - root - INFO - Using user_id from state parameter: 685e77f1d9149ab3af7adb5e
2025-06-27 18:34:38,610 - root - INFO - Facebook callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 18:34:38,610 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 18:34:39,055 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 18:34:39,058 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:39] "GET /facebook/callback?code=AQAqdJEgMUsg5OC1y5WYGwszCX3U4aGfbRlyajCBUQIoYwaLe6I9uJAc7093elwRONWoWB1Ey_FmvBhMvvt2FiPAmIJgY9HCVUFn7ZQTur5jCB9QcMD-vjIjyU4tAgpev04lE8aSZt4sIJdgyxxisFIZ4N30tf5R5Y1w6yY8ef9Q4-MPOZ6im6XL-ecIhlT2JD3R9wf9j2fMuOwcszIqDWyoZ-tv7TnM8IpVXUKdfDFtERvReEiABdNhEPyb0RXXiVKRnAc90PcygqPBmfilP2iHayLW26lZJTb9Sa7lPcRWLHUV-GtOGxhrLBfo1LWJw26_Ni2kFmZFcqCNiEi0LBgh_NR-Hz68pyDv-3HH8m40mJlq8-b7pqylhiiRLSfZmpg&state=agenticoauth_685e77f1d9149ab3af7adb5e HTTP/1.1" 200 -
2025-06-27 18:34:40,013 - root - INFO - Request path: /favicon.ico, Method: GET
2025-06-27 18:34:40,014 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 18:34:40,014 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:34:40,016 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:40] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-27 18:34:40,409 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:34:40,409 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 18:34:40,409 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:34:40,409 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!')]}
2025-06-27 18:34:40,409 - root - ERROR - login_required - No user_id in session
2025-06-27 18:34:40,410 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:40] "[32mGET /connect?csrf_token= HTTP/1.1[0m" 302 -
2025-06-27 18:34:40,419 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-06-27 18:34:40,419 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')]}
2025-06-27 18:34:40,419 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:34:40,420 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:34:40] "GET /auth/send-otp?next=http://localhost:5000/connect?csrf_token%3D HTTP/1.1" 200 -
2025-06-27 18:36:44,302 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-27 18:36:44,723 - werkzeug - INFO -  * Restarting with stat
2025-06-27 18:36:45,128 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 18:36:45,136 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 18:36:46,149 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/app.py', reloading
2025-06-27 18:36:46,630 - werkzeug - INFO -  * Restarting with stat
2025-06-27 18:36:47,076 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 18:36:47,079 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 18:36:49,667 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-06-27 18:36:49,667 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d'}
2025-06-27 18:36:49,667 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:36:51,465 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 18:36:51,465 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 18:36:51,465 - twilio.http_client - INFO - Headers:
2025-06-27 18:36:51,465 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 18:36:51,465 - twilio.http_client - INFO - Accept : application/json
2025-06-27 18:36:51,465 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 18:36:51,465 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 18:36:51,465 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 18:36:51,465 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 18:36:52,119 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 18:36:52,119 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 13:06:52 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ29b347ef965a6fad2d965c7fd398e8de', 'Twilio-Request-Duration': '0.122', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c2e71f7a744af65a5cea344ff07d9628.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'mgC0jSAE_TN5zmz3XsJORdrnRkzViAJ20SrWEiMGviSa6tZgZFPwjw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 18:36:52,119 - root - INFO - OTP sent to +************, Twilio SID: SM29b347ef965a6fad2d965c7fd398e8de
2025-06-27 18:36:52,129 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:36:52] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 18:37:05,699 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-06-27 18:37:05,699 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d'}
2025-06-27 18:37:05,699 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:06,750 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 18:37:08,270 - root - INFO - User logged in - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:08,271 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:08,562 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:08] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 18:37:08,571 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:37:08,571 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:08,572 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:08,572 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:08,572 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:08,577 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:08] "GET /connect HTTP/1.1" 200 -
2025-06-27 18:37:15,064 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 18:37:15,064 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:15,064 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:15,065 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:15,965 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:15,965 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:15,967 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:15] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 18:37:21,149 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 18:37:21,149 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:21,150 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:21,150 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:21,150 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:21,854 - root - INFO - Facebook callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 18:37:21,854 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 18:37:22,187 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 18:37:22,187 - root - INFO - Facebook callback - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:22,189 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:22] "[32mGET /facebook/callback?code=AQD7OJSqMQlf6bNxP8Ivm9LgGeLMBqN7lkd4nMZ9Ni8Tl6jMj8sAwMlTk4CRaJIe3Pkm51tXGCQ42-MC2TPT7B_Ig_BJ5UgAYPLyS1t53S7nXemUvVWbmThfqF6E-OExrj43dBx4fR8_ma85F63zYIYAprWgglqYpxNN5YBBgClj0EtXmG95ZyCLJMSxYnRPUM_MzYXUCLGoGxukNxsQqypK9coP5bBxRL5IcYyiC1QJueHC1O2be3GOcWOkOBg2Gc6Gbg4OB7-m8vXO2Hgvopm8vMdwzLujL-NPO3IRdDWKoEo11Zl-qFlY1CcJWxE8Whn8td96q5cl_tABoati79S04i96IEsGXJan-Wq7cGiXbbT8TNKbEhRLv_eImWDx6F0&state=agenticoauth_685e77f1d9149ab3af7adb5e HTTP/1.1[0m" 302 -
2025-06-27 18:37:22,196 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:37:22,196 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:22,196 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:22,196 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:22,196 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:22,197 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:22] "GET /connect HTTP/1.1" 200 -
2025-06-27 18:37:29,454 - root - INFO - Request path: /twitter/login, Method: GET
2025-06-27 18:37:29,454 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:29,455 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:29,455 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:29,870 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:29,871 - root - INFO - Twitter login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:29,873 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:29] "[32mGET /twitter/login HTTP/1.1[0m" 302 -
2025-06-27 18:37:35,103 - root - INFO - Request path: /twitter/callback, Method: GET
2025-06-27 18:37:35,103 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:35,104 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:35,104 - root - INFO - Twitter callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:35,104 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:36,014 - root - INFO - Twitter callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 18:37:36,014 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 18:37:37,507 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e9798724107142169aa37
2025-06-27 18:37:37,510 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:37] "GET /twitter/callback?state=agenticoauth_685e77f1d9149ab3af7adb5e&code=******************************************************************************************* HTTP/1.1" 200 -
2025-06-27 18:37:38,960 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:37:38,960 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:38,960 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:38,961 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:38,961 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:38,962 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:38] "GET /connect?csrf_token=d51f2097feeafd258955507767c96308675a182d HTTP/1.1" 200 -
2025-06-27 18:37:40,650 - root - INFO - Request path: /youtube/login, Method: GET
2025-06-27 18:37:40,650 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:40,651 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:40,651 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:40,987 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:40,987 - root - INFO - YouTube login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:40,989 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:40] "[32mGET /youtube/login HTTP/1.1[0m" 302 -
2025-06-27 18:37:50,293 - root - INFO - Request path: /youtube/callback, Method: GET
2025-06-27 18:37:50,293 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:50,293 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:50,294 - root - INFO - YouTube callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:50,294 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:50,745 - root - INFO - YouTube callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 18:37:50,745 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 18:37:51,085 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 685e97a6724107142169aa6c
2025-06-27 18:37:51,087 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:51] "GET /youtube/callback?state=agenticoauth_685e77f1d9149ab3af7adb5e&code=4/0AVMBsJjZyRgPSBUOljtq7Ahm4Q3nkhEHgDUhnl8eABOYEnhktq2HlhadNtODXeDxOwsRUQ&scope=https://www.googleapis.com/auth/youtube.force-ssl HTTP/1.1" 200 -
2025-06-27 18:37:52,717 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:37:52,717 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:52,717 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:37:52,717 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:37:52,718 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:37:52,719 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:37:52] "GET /connect?csrf_token=d51f2097feeafd258955507767c96308675a182d HTTP/1.1" 200 -
2025-06-27 18:39:58,420 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/facebook.py', reloading
2025-06-27 18:39:58,739 - werkzeug - INFO -  * Restarting with stat
2025-06-27 18:39:59,174 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 18:39:59,181 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 18:40:01,862 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:40:01,862 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:01,862 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:01,863 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:01,863 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:01,870 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:01] "GET /connect?csrf_token=d51f2097feeafd258955507767c96308675a182d HTTP/1.1" 200 -
2025-06-27 18:40:02,967 - root - INFO - Request path: /facebook/login, Method: GET
2025-06-27 18:40:02,967 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:02,967 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:02,967 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:04,864 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:04,864 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:04,867 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:04] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-06-27 18:40:09,359 - root - INFO - Request path: /facebook/callback, Method: GET
2025-06-27 18:40:09,360 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:09,360 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:09,360 - root - INFO - Facebook callback - Session at start: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:09,360 - root - INFO - Extracted user_id from state: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:10,821 - root - INFO - Facebook callback - Using user_id: 685e77f1d9149ab3af7adb5e of type <class 'str'>
2025-06-27 18:40:10,822 - root - INFO - Saving profile with user_id: 685e77f1d9149ab3af7adb5e of type <class 'bson.objectid.ObjectId'>
2025-06-27 18:40:11,168 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-06-27 18:40:11,169 - root - INFO - Facebook callback - Session before returning HTML: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:11,171 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:11] "GET /facebook/callback?code=AQBiNeEMHamlt3jrBNnpVtuf2v9RuhD3nUENaA5PTI98iqHf9Hgw_-y9qs4-ZAI6EGpKbwT8wzYFpebIuNhi1fJ0ek9HJRJyZ5ZS8VkGrLvb9gsU0M5XYGD-_9rIW5riW6TzqcF_Rc_fE_KJR7r_ONY5XIKfQbAmyJExLBlOlcZQ7SFX9NCumXH-EQnVuB-jszSjOvq1nh2jqYYwJLZHDfIxkjK4AYLbOjladgsIogesX-4nvuXNmhAeLR6UO03Hcu-H6IYYfLGQNMCDUFDHdmzo4y7wxv1IengxWXmpJxzYyzZmaKWacy5b7eUYrVGdsroUJO3GlDT851Y5W9rrc7FridSeMV2xiwhZxKCW0La4WcbPkCzUwQ7dOyBQjygy37I&state=agenticoauth_685e77f1d9149ab3af7adb5e HTTP/1.1" 200 -
2025-06-27 18:40:12,861 - root - INFO - Request path: /connect, Method: GET
2025-06-27 18:40:12,861 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:12,861 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:12,862 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:12,862 - root - INFO - Connect route - User ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:12,863 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:12] "GET /connect?csrf_token=d51f2097feeafd258955507767c96308675a182d HTTP/1.1" 200 -
2025-06-27 18:40:21,444 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 18:40:21,444 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:21,444 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:21,446 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:21] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 18:40:21,458 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 18:40:21,458 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:21,458 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:21,459 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'Facebook account connected successfully!'), ('message', 'Please log in to access this page'), ('message', 'Facebook account connected successfully!'), ('message', 'Twitter account connected successfully!'), ('message', 'YouTube account connected successfully!'), ('message', 'Facebook account connected successfully!')], 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:21,952 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:22,517 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:22] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 18:40:26,582 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 18:40:26,582 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:26,582 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:40:26,582 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:40:26,917 - root - INFO - login_required - User is logged in with ID: 685e77f1d9149ab3af7adb5e
2025-06-27 18:40:27,264 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:40:27] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 18:43:11,352 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 18:43:11,352 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:43:11,353 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'fDYyJ-lCWutyOFOmqieQpiEBPTKqkv-N4PVGCTaZS_Y.Sh5BM2o8kPpVm4qO1iLEBrlrFJ8')])
2025-06-27 18:43:11,353 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd51f2097feeafd258955507767c96308675a182d', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:43:11,741 - root - ERROR - login_required - User with ID 685e77f1d9149ab3af7adb5e not found in database
2025-06-27 18:43:11,751 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:11] "[35m[1mGET /subscription/ HTTP/1.1[0m" 500 -
2025-06-27 18:43:11,806 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:11] "GET /subscription/?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-27 18:43:11,806 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:11] "GET /subscription/?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-27 18:43:11,870 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:11] "GET /subscription/?__debugger__=yes&cmd=resource&f=console.png&s=orxWGvOGVVlIkrp9Gure HTTP/1.1" 200 -
2025-06-27 18:43:12,057 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:12] "GET /subscription/?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-27 18:43:17,155 - root - INFO - Request path: /, Method: GET
2025-06-27 18:43:17,155 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:43:17,156 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:43:17,159 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:17] "GET / HTTP/1.1" 200 -
2025-06-27 18:43:19,603 - root - INFO - Request path: /, Method: GET
2025-06-27 18:43:19,603 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 18:43:19,603 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 18:43:19,605 - werkzeug - INFO - 1******** - - [27/Jun/2025 18:43:19] "GET / HTTP/1.1" 200 -
2025-06-27 18:59:43,379 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-27 18:59:43,379 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 18:59:43,380 - werkzeug - INFO -  * Restarting with stat
2025-06-27 18:59:43,697 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 18:59:43,701 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 19:07:46,325 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 19:07:46,326 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:07:46,327 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:07:46,333 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:46] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 19:07:46,372 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 19:07:46,372 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:07:46,373 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:07:46,373 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:07:48,066 - root - ERROR - login_required - User with ID 685e77f1d9149ab3af7adb5e not found in database
2025-06-27 19:07:48,076 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:48] "[35m[1mGET /subscription/ HTTP/1.1[0m" 500 -
2025-06-27 19:07:48,124 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:48] "GET /subscription/?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-27 19:07:48,126 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:48] "GET /subscription/?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-27 19:07:48,516 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:48] "GET /subscription/?__debugger__=yes&cmd=resource&f=console.png&s=o1HZ2bz0tFHxLGqtZyaj HTTP/1.1" 200 -
2025-06-27 19:07:49,613 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:07:49] "GET /subscription/?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-27 19:08:01,672 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-27 19:08:01,672 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-27 19:08:01,673 - werkzeug - INFO -  * Restarting with stat
2025-06-27 19:08:02,184 - werkzeug - WARNING -  * Debugger is active!
2025-06-27 19:08:02,192 - werkzeug - INFO -  * Debugger PIN: 140-113-725
2025-06-27 19:08:05,793 - root - INFO - Request path: /, Method: GET
2025-06-27 19:08:05,793 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:08:05,793 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:05,800 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:05] "GET / HTTP/1.1" 200 -
2025-06-27 19:08:12,985 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-06-27 19:08:12,986 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:08:12,986 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:14,726 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-06-27 19:08:14,726 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-06-27 19:08:14,726 - twilio.http_client - INFO - Headers:
2025-06-27 19:08:14,726 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-06-27 19:08:14,726 - twilio.http_client - INFO - Accept : application/json
2025-06-27 19:08:14,726 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-06-27 19:08:14,726 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-06-27 19:08:14,726 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-06-27 19:08:14,726 - twilio.http_client - INFO - -- END Twilio API Request --
2025-06-27 19:08:15,725 - twilio.http_client - INFO - Response Status Code: 201
2025-06-27 19:08:15,725 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 27 Jun 2025 13:38:15 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQbd32a8978c3bbdf9003c85cdabc05666', 'Twilio-Request-Duration': '0.138', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 c13661f9455806125a56201b0ccd2b96.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '9MDe--vP62IxPo3RnDCHgjVP6GXjCwCpPzU8KPSFEmAtGmbzVY_6Uw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-06-27 19:08:15,725 - root - INFO - OTP sent to +************, Twilio SID: SMbd32a8978c3bbdf9003c85cdabc05666
2025-06-27 19:08:15,729 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:15] "POST /auth/send-otp HTTP/1.1" 200 -
2025-06-27 19:08:28,112 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-06-27 19:08:28,112 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e77f1d9149ab3af7adb5e'}
2025-06-27 19:08:28,112 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:28,771 - root - INFO - Deleted OTP record for mobile: +************
2025-06-27 19:08:29,409 - root - INFO - User logged in - User ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:29,409 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:29,678 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:29] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-06-27 19:08:29,690 - root - INFO - Request path: /auth/complete-profile, Method: GET
2025-06-27 19:08:29,690 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:29,691 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:29,691 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:29,973 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:30,311 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:30] "GET /auth/complete-profile HTTP/1.1" 200 -
2025-06-27 19:08:30,349 - root - INFO - Request path: /static/css/style.css, Method: GET
2025-06-27 19:08:30,349 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:30,350 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:30,352 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:30] "[33mGET /static/css/style.css HTTP/1.1[0m" 404 -
2025-06-27 19:08:44,184 - root - INFO - Request path: /auth/complete-profile, Method: POST
2025-06-27 19:08:44,185 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:44,185 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:44,186 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:08:44,519 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:44,804 - root - INFO - Updated user info for user 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:44,806 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:44] "[32mPOST /auth/complete-profile HTTP/1.1[0m" 302 -
2025-06-27 19:08:44,816 - root - INFO - Request path: /connect, Method: GET
2025-06-27 19:08:44,817 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 19:08:44,818 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:44,818 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 19:08:44,818 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:44,822 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:44] "GET /connect HTTP/1.1" 200 -
2025-06-27 19:08:54,194 - root - INFO - Request path: /subscription, Method: GET
2025-06-27 19:08:54,194 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 19:08:54,194 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:54,195 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:54] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-06-27 19:08:54,210 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 19:08:54,211 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 19:08:54,211 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:08:54,211 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Profile updated successfully')]}
2025-06-27 19:08:54,537 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:08:54,877 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:08:54] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 19:09:07,555 - root - INFO - Request path: /subscription/subscribe, Method: POST
2025-06-27 19:09:07,555 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:09:07,555 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:09:07,556 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:09:07,886 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:09:08,499 - root - INFO - Attempting to send Telegram notification for user: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:09:08,499 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-06-27 19:09:08,500 - root - INFO - Sending subscription notification to chat_id: 7644343290
2025-06-27 19:09:09,199 - root - INFO - Subscription notification sent successfully: {'ok': True, 'result': {'message_id': 547, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1751031549, 'text': '🎉 New Subscription!\n\nUser: Akhilesh Thakur (+************)\nPlan: Pro\nPrice: $24.99\nTokens: 1000', 'entities': [{'offset': 45, 'length': 13, 'type': 'phone_number'}]}}
2025-06-27 19:09:09,199 - root - INFO - Sending moderation request to chat_id: 7644343290
2025-06-27 19:09:09,869 - root - INFO - Moderation request sent successfully: {'ok': True, 'result': {'message_id': 548, 'from': {'id': 7231975578, 'is_bot': True, 'first_name': 'AI Moderation', 'username': 'n8n_ai_moderation_bot'}, 'chat': {'id': 7644343290, 'first_name': 'Akhilesh', 'last_name': 'Thakur', 'type': 'private'}, 'date': 1751031549, 'text': '📝 Please share your post for moderation.\n\nYour subscription is now active. You can now share your post for review and publishing.'}}
2025-06-27 19:09:09,870 - root - INFO - Attempting to update chat state to 'awaiting_post' for chatId: 7644343290
2025-06-27 19:09:10,527 - root - INFO - Updated existing chat state to 'awaiting_post' for chatId: 7644343290
2025-06-27 19:09:10,529 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:09:10] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-06-27 19:09:10,540 - root - INFO - Request path: /subscription/, Method: GET
2025-06-27 19:09:10,540 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-27 19:09:10,540 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:09:10,540 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b', '_flashes': [('success', 'Successfully subscribed to Pro plan!')]}
2025-06-27 19:09:10,869 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:09:11,206 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:09:11] "GET /subscription/ HTTP/1.1" 200 -
2025-06-27 19:14:40,327 - root - INFO - Request path: /connect, Method: GET
2025-06-27 19:14:40,327 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:14:40,327 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:14:40,328 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:14:40,328 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:14:40,329 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:14:40] "GET /connect HTTP/1.1" 200 -
2025-06-27 19:14:42,470 - root - INFO - Request path: /linkedin/login, Method: GET
2025-06-27 19:14:42,470 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:14:42,471 - root - INFO - Request cookies: ImmutableMultiDict([('session', '-joOf3RrgDkL_JXLskHLIjHq53V2-5BnzYLk9BsHvrA.qVYcP1U-OccgY-808gsHCrcqI7c')])
2025-06-27 19:14:42,471 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:14:42,801 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-06-27 19:14:42,801 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'be714562df89008332eecea577c874498428d2f3', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-06-27 19:14:42,803 - werkzeug - INFO - 1******** - - [27/Jun/2025 19:14:42] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-08 15:54:45,421 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-08 15:54:45,421 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 15:54:45,422 - werkzeug - INFO -  * Restarting with stat
2025-07-08 15:54:45,730 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 15:54:45,735 - werkzeug - INFO -  * Debugger PIN: 824-504-881
2025-07-08 15:54:52,355 - root - INFO - Request path: /, Method: GET
2025-07-08 15:54:52,356 - root - INFO - Session data: {'_permanent': True}
2025-07-08 15:54:52,356 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-08 15:54:52,362 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:54:52] "GET / HTTP/1.1" 200 -
2025-07-08 15:54:52,985 - root - INFO - Request path: /favicon.ico, Method: GET
2025-07-08 15:54:52,986 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87'}
2025-07-08 15:54:52,986 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:54:52,987 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:54:52] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-08 15:55:21,600 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-07-08 15:55:21,601 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87'}
2025-07-08 15:55:21,601 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:55:22,989 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-08 15:55:22,989 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-07-08 15:55:22,989 - twilio.http_client - INFO - Headers:
2025-07-08 15:55:22,989 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-08 15:55:22,989 - twilio.http_client - INFO - Accept : application/json
2025-07-08 15:55:22,989 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-07-08 15:55:22,989 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-07-08 15:55:22,989 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-08 15:55:22,989 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-08 15:55:23,889 - twilio.http_client - INFO - Response Status Code: 201
2025-07-08 15:55:23,889 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '826', 'Connection': 'keep-alive', 'Date': 'Tue, 08 Jul 2025 10:25:24 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ56d049cf457d7c1a9d11798eb72f4eb3', 'Twilio-Request-Duration': '0.163', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 e9c3f34c843eb4099f41b5b2a942220e.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'HoNHKqc_w8F2S0Erh1uLLTeHoAhYUJlmS3qEtJkn73Kqrg0KmEE20Q==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-08 15:55:23,890 - root - INFO - OTP sent to +13152153533, Twilio SID: SM56d049cf457d7c1a9d11798eb72f4eb3
2025-07-08 15:55:23,896 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:55:23] "POST /auth/send-otp HTTP/1.1" 200 -
2025-07-08 15:56:19,158 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-07-08 15:56:19,159 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87'}
2025-07-08 15:56:19,159 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:56:19,720 - root - INFO - Deleted OTP record for mobile: +13152153533
2025-07-08 15:56:20,283 - root - INFO - User logged in - User ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:56:20,284 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:20,643 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:56:20] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-07-08 15:56:20,651 - root - INFO - Request path: /auth/complete-profile, Method: GET
2025-07-08 15:56:20,651 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:20,651 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:56:20,651 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:20,950 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:56:21,266 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:56:21] "GET /auth/complete-profile HTTP/1.1" 200 -
2025-07-08 15:56:21,314 - root - INFO - Request path: /static/css/style.css, Method: GET
2025-07-08 15:56:21,315 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:21,315 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:56:21,317 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:56:21] "[33mGET /static/css/style.css HTTP/1.1[0m" 404 -
2025-07-08 15:56:53,073 - root - INFO - Request path: /auth/complete-profile, Method: POST
2025-07-08 15:56:53,074 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:53,075 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:56:53,076 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:56:53,333 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:56:53,589 - root - INFO - Updated user info for user 686519e5d9149ab3af7adb6e
2025-07-08 15:56:53,591 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:56:53] "[32mPOST /auth/complete-profile HTTP/1.1[0m" 302 -
2025-07-08 15:56:53,599 - root - INFO - Request path: /connect, Method: GET
2025-07-08 15:56:53,600 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Profile updated successfully')]}
2025-07-08 15:56:53,600 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:56:53,600 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Profile updated successfully')]}
2025-07-08 15:56:53,600 - root - INFO - Connect route - User ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:56:53,604 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:56:53] "GET /connect HTTP/1.1" 200 -
2025-07-08 15:57:02,880 - root - INFO - Request path: /subscription, Method: GET
2025-07-08 15:57:02,881 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Profile updated successfully')]}
2025-07-08 15:57:02,881 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:02,883 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:02] "[32mGET /subscription HTTP/1.1[0m" 308 -
2025-07-08 15:57:02,892 - root - INFO - Request path: /subscription/, Method: GET
2025-07-08 15:57:02,892 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Profile updated successfully')]}
2025-07-08 15:57:02,892 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:02,892 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Profile updated successfully')]}
2025-07-08 15:57:03,150 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:03,458 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:03] "GET /subscription/ HTTP/1.1" 200 -
2025-07-08 15:57:06,390 - root - INFO - Request path: /subscription/subscribe, Method: POST
2025-07-08 15:57:06,390 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:06,390 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:06,391 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:06,647 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:07,188 - root - INFO - Attempting to send Telegram notification for user: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:07,189 - root - INFO - User data available: _id, mobile, otp, verified, subscribed, chatId, source, created_at, updated_at, last_login, email, name
2025-07-08 15:57:07,189 - root - WARNING - Telegram notification not sent: User 686519e5d9149ab3af7adb6e has no ChatId or telegram_chat_id
2025-07-08 15:57:07,189 - root - INFO - User mobile: +13152153533
2025-07-08 15:57:07,189 - root - WARNING - Cannot update chat state: User 686519e5d9149ab3af7adb6e has no chatId
2025-07-08 15:57:07,191 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:07] "[32mPOST /subscription/subscribe HTTP/1.1[0m" 302 -
2025-07-08 15:57:07,199 - root - INFO - Request path: /subscription/, Method: GET
2025-07-08 15:57:07,199 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Successfully subscribed to Business plan!')]}
2025-07-08 15:57:07,199 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:07,199 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e', '_flashes': [('success', 'Successfully subscribed to Business plan!')]}
2025-07-08 15:57:07,457 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:07,747 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:07] "GET /subscription/ HTTP/1.1" 200 -
2025-07-08 15:57:12,810 - root - INFO - Request path: /connect, Method: GET
2025-07-08 15:57:12,810 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:12,811 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:12,811 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:12,811 - root - INFO - Connect route - User ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:12,813 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:12] "GET /connect HTTP/1.1" 200 -
2025-07-08 15:57:17,586 - root - INFO - Request path: /facebook/login, Method: GET
2025-07-08 15:57:17,587 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:17,587 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:57:17,587 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:17,843 - root - INFO - login_required - User is logged in with ID: 686519e5d9149ab3af7adb6e
2025-07-08 15:57:17,844 - root - INFO - Facebook login - Session before redirect: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:57:17,845 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:17] "[32mGET /facebook/login HTTP/1.1[0m" 302 -
2025-07-08 15:57:43,836 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:43] code 400, message Bad request version ("õÀ\x90df\x9cV²\x0fA'@p´ô1ZR{¥ñF")
2025-07-08 15:57:43,836 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:43] "[31m[1m\x16\x03\x01\x06Ò\x01\x00\x06Î\x03\x03g\x03\x1dO,coÿ/ã\x1a\x8bäÇÒ\x86Zú\x146\x13^añÆY¬³L\x06«W y\x06h|2¤n¾:;vÌVu\x95¿ö\x0chÿ\x8dR\x84µ\x96ÍN·Ä± ~\x00 ::\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06eªª\x00\x00\x00\x0d\x00\x12\x00\x10\x04\x03\x08\x04\x04\x01\x05\x03\x08\x05\x05\x01\x08\x06\x06\x01DÍ\x00\x05\x00\x03\x02h2\x00-\x00\x02\x01\x01\x00+\x00\x07\x06\x1a\x1a\x03\x04\x03\x03\x00\x0b\x00\x02\x01\x00\x00\x1b\x00\x03\x02\x00\x02\x00\x10\x00\x0e\x00\x0c\x02h2\x08http/1.1\x00\x05\x00\x05\x01\x00\x00\x00\x00\x003\x04ï\x04íjj\x00\x01\x00\x11ì\x04ÀÍà¹\x10A\x04Þv\x060\x96\x12\x15¥_¥Q²\x1dÓ\x01jµÆ\x8bG>´ZÊöõ­÷\x8at\x85\x11_\x89;TØ8\x03Ü\x82rùaG\x0fIK.êºje¡§\x0c7\x9fip\x8ad­oº\x13dÀ^\x97ÚU@ÀÂrÈ\x0e6wB÷\x0b\x1cÃ§PIÀ\x0c\x0d\x8c\x05q\x97j²"\x99\x88¼\x00Gµ\x10b\x86\x99ÜS\x08%ÒÍß'iï"»kØ\x8c¦¦\x14\x08Ü=\x9adEÂ7,_\x07\x10Ó\x88\x19\x19âÁêX \x98\x15N'R½ûvhTÙ@øºË\x04³I õÀ\x90df\x9cV²\x0fA'@p´ô1ZR{¥ñF[0m" 400 -
2025-07-08 15:57:43,839 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:43] code 400, message Bad request version ('.\x993É\x8b(|')
2025-07-08 15:57:43,839 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:43] "[31m[1m\x16\x03\x01\x07\x12\x01\x00\x07\x0e\x03\x03G\x91Ì{\x8cc\x9e\x04\x06ÜS\x88¦ôX\x14ÕHïöHÎï\x99\x9c·>"õ\x1e\x0eî \x7fEÛð\x87\x873ÚM®È½î\x0dõ\x0e\x04éU\x18§Ó\x01^[EEzp¯òá\x00 ÚÚ\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06¥**\x00\x00\x00\x0d\x00\x12\x00\x10\x04\x03\x08\x04\x04\x01\x05\x03\x08\x05\x05\x01\x08\x06\x06\x01\x003\x04ï\x04íÚÚ\x00\x01\x00\x11ì\x04À\x87ö6µê\x00\x98eK°*\x94\x04bNLb½\x7f¬§øh\x8brÇZâüy\x01ø¨½\x8b*ÅW\x99CHO>\x81FôÛÂ~çl\x00!Q}àiìÒ\x19©\x08\x8b\x0fb&r\x85*gâNÀd\x02©"\x95\x06À\x0e6ÚJ\x96bgÎ \x1bàx¬ó"4cÓQ\x1cÐYî'UÒ\x91\x92\x82\x9b°KLq\x9d¨.l$GâéI´Ì\x8fGÚ\x07ÙÐ\x15=\x8bB\x99\x19{T\x11\x02Û \x00t£fËH[Ëì¤«Ì@äb\x0cýKÅ'9\x0e\x80'bä\x04wøå­~Hb\x02F:Y\x11|Hx\x93U°>É\x86^\x80K¦\x0eÈ<¯G1Æ\x13\x06}\x03Ãß÷\x05µózÄHÈ\x89¡\x97:\x09$¬¥\x1b\x09À¯"\x80\x9f¼Ç/*ö\x8d#v|lÒ W\x99±qW0>ÒÏÔèr YlÖ\x97v¶x\x81\x85l\x7f\x0dÆ<\x92ü¯£F©úÓ%ç:¦üU\x0c\x1a³§õµ¤%(´eñ\x1f\x9e)u÷È-¶\x0c\x17{¥HÖ)\x80i!e@\x8bF\x85ú\x8c\x0cPf¥ç-ð»xòWLP\x9bn\x9c\x88pÃÈ~ý`\x11ÏÁ¡#L\x8e\x8f²¸ê)4i\x002\x84c·Eü8\x0eÄ\x0b¡$1+ºMk\x90\x9an\x05JCà\x94\x17\x94@J\x89\x06\x0d\x96@ç¡\x17hü\x11\x95÷\x1a\x97D<ÔÄyKÜF\x99J,!\x1cpd×J\x99R(ë\x93Æ¢\x18\x0eH%a0\x05\x19\x8b\x09`´\x8b\x10h\x90_¦\x80Z>z¢u\x08^®Ðn\x0f5\x95\x9cà\x1c¥¸!?\x1c$¢2y\x9cL4È)O¼ÃI¶ÚÁµãI\x12\x86½/\x86\x04]\x9bO+\x98CJH\x09N\x84Y÷\x80È7¥@\x86q3Çø\x1d\x8eÔ¹Ê\x05FY\x8cWÊ\x17i\x8c#»Ç\x96 ¶H\x04L\x16N41ªÞ²\x1f\x0d\x90\x91Öq\x9a\x8f£TP«\x93¾ix>\x86oÜ\x98\x93m\x09n\x16ä£qd0QQ\x92¿%\x09[;3­õN\x07ç?F\x12´á\x19F8{pIE\x02=d\x9aãû^/º,$\x95kíú\x01PÅNl\x17\x05Ó\x91§!Ã¬í22\x85\x13\x86\x8c\x98G\x85\x19[\x85úq\x07\x0b\x02êÈgW\x09ÂTñ\x89\x19\x85.\x993É\x8b(|[0m" 400 -
2025-07-08 15:57:43,840 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:43] code 400, message Bad request version ('ªª\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06Eúú\x00\x00ÿ\x01\x00\x01\x00DÍ\x00\x05\x00\x03\x02h2\x00\x1b\x00\x03\x02\x00\x02\x00')
2025-07-08 15:57:43,841 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:43] "[31m[1m\x16\x03\x01\x06²\x01\x00\x06®\x03\x03½%=ÒÅÌ¶1A\x15\x15\x0bøÃu¯÷À\x82\x81v(·XG{©ÙøÿpY Ì\x88X\x07T7Ð[\x95¸òÌZ\x1cü\x1aýãWýsØ1Ô³øR}ú\x17á§\x00 ªª\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06Eúú\x00\x00ÿ\x01\x00\x01\x00DÍ\x00\x05\x00\x03\x02h2\x00\x1b\x00\x03\x02\x00\x02\x00[0m" 400 -
2025-07-08 15:57:43,842 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:43] code 400, message Bad request version ('`îÝ¬\x19GÏèBÿ\x01\x00\x01\x00\x00\x17\x00\x00\x00\x05\x00\x05\x01\x00\x00\x00\x00\x003\x04ï\x04í\x9a\x9a\x00\x01\x00\x11ì\x04ÀiEAÒË.Æ!\x18¼QÎô')
2025-07-08 15:57:43,842 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:43] "[31m[1m\x16\x03\x01\x06Ò\x01\x00\x06Î\x03\x03\x19é­\x08\x1d¸¡º("u\x9bh\x13Á\x87ÄC²b\x94\x1bê\x0b\x1aþ\x93æ«üV\\ s\x91\x05nI&Í\x19!9\x1f\x8aú¹\x9aîll\x07dIÕ)øTÖZ\x0eìÈ|»\x00 ºº\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06ejj\x00\x00\x00-\x00\x02\x01\x01\x00\x10\x00\x0e\x00\x0c\x02h2\x08http/1.1\x00\x00\x00\x0e\x00\x0c\x00\x00\x09localhost\x00\x1b\x00\x03\x02\x00\x02\x00\x0b\x00\x02\x01\x00\x00\x12\x00\x00þ\x0d\x00Ú\x00\x00\x01\x00\x01g\x00 ç\x93\x9fSÅª\x1cÇÏì÷AªgÂ!ÙPq¨m\x09Þã:¹¿\x8b\x0fI\x96 \x00°h\x90ù2®®åWX¹¥/*I7Ð~æ)b¸\x02&·0ü\x14-\x11¹Ï\x029ÁÎ:xGØî'\x02ÆöC8\x8fSu\x02)'¨ù÷«§´FWiä\x02èT\x0eKóc\x85\x1e\x0b\x09avå".x5±Q;T}\x07(q\x8f\x81k\x92Ðü[Ü!^<\x94\x85ØÍ\x99¼¯ºw9 y4>Z \x1cBö3¨\x96E\x9aÏ·`ì\x7f¼ú\x8cùI'éÔ[ ^%äÖg\x98®À\x8ef\x1eMóJ\x84v¡´\x86¤k¢C^AÇ\x13¹\x0d`îÝ¬\x19GÏèBÿ\x01\x00\x01\x00\x00\x17\x00\x00\x00\x05\x00\x05\x01\x00\x00\x00\x00\x003\x04ï\x04í\x9a\x9a\x00\x01\x00\x11ì\x04ÀiEAÒË.Æ!\x18¼QÎô[0m" 400 -
2025-07-08 15:57:46,149 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:46] code 400, message Bad request syntax ('\x16\x03\x01\x06²\x01\x00\x06®\x03\x03/½6V\x9a#\xadÔYr\x05%:µh\x01\x82?åÝF;')
2025-07-08 15:57:46,149 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:46] "[31m[1m\x16\x03\x01\x06²\x01\x00\x06®\x03\x03/½6V\x9a#­ÔYr\x05%:µh\x01\x82?åÝF;[0m" 400 -
2025-07-08 15:57:46,150 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:46] code 400, message Bad request version ('³çî\xad\x0fQT©ý')
2025-07-08 15:57:46,152 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:46] "[31m[1m\x16\x03\x01\x06Ò\x01\x00\x06Î\x03\x03\x02Ô\x04ò¶#u°ò¬J«5½Q%xõý·¤÷\x9a£\x0b}(ö3A\x1f\x0b £ÆÐro3 ³çî­\x0fQT©ý[0m" 400 -
2025-07-08 15:57:46,152 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:46] code 400, message Bad request version ('jj\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06Eêê\x00\x00\x00+\x00\x07\x06\x1a\x1a\x03\x04\x03\x03\x00')
2025-07-08 15:57:46,153 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:46] "[31m[1m\x16\x03\x01\x06²\x01\x00\x06®\x03\x03\x83Ý\x923À)\x82¢y\x98i\x99¿\\VIA/¨à\x16\x0cÔÍÌ\x11\x04«Bõ~g l\x94æï\x82\x98\x99OÞ0o\x93Ó\x17Zã\x9d\x8d°Û\x1e¹°\x00!\x94¼\x03\x82\x879þ\x00 jj\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06Eêê\x00\x00\x00+\x00\x07\x06\x1a\x1a\x03\x04\x03\x03\x00[0m" 400 -
2025-07-08 15:57:46,154 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:46] code 400, message Bad request version ('\x00\x02\x01\x00\x00\x1b\x00\x03\x02\x00\x02\x00')
2025-07-08 15:57:46,154 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:46] "[31m[1m\x16\x03\x01\x06Ò\x01\x00\x06Î\x03\x03ÅÖ¦\x08jyzÜ\x0d"Êé\x04Ç\x0d²\x90\x13\x9b·jèSt\x12Ò©ÊËYJ\x0e Z¬þ³!Ð\x86<\x91\x9dY\x18wõ.\x89ZJØ\x8cÔ÷ä ©8põp\x13/k\x00 ZZ\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06ejj\x00\x00\x00\x0b\x00\x02\x01\x00\x00\x1b\x00\x03\x02\x00\x02\x00[0m" 400 -
2025-07-08 15:57:47,645 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:47] code 400, message Bad request syntax ("\x16\x03\x01\x06ò\x01\x00\x06î\x03\x03ÏVÈÕ'KÔÈ¢")
2025-07-08 15:57:47,645 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:47] "[31m[1m\x16\x03\x01\x06ò\x01\x00\x06î\x03\x03ÏVÈÕ'KÔÈ¢[0m" 400 -
2025-07-08 15:57:47,646 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:47] code 400, message Bad request version ('KíÚ\x99»\x18\x95/\x92\x16ýf¨')
2025-07-08 15:57:47,646 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:47] "[31m[1m\x16\x03\x01\x06²\x01\x00\x06®\x03\x03Ãæg ç\x01ï\x0f\x8a²É\x96}n»S\x83\x16¼ë1§Ê\x0eXÄ\x14°\x13\x80H­ Ü\x92Â±zá\x15"âlÅÛ\x15Æ"bÿ(Qß\x93>H\x13\x02\x95\x00`+;\x09\x1d\x00 êê\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06E::\x00\x00\x00\x0b\x00\x02\x01\x00\x003\x04ï\x04íZZ\x00\x01\x00\x11ì\x04À^×*c\x9b\x88!\x9cr\x81×'N\x99\x90\x92x¯\x07IY<\x93¾Y,¨\x9bcuÞ\x95\x15Þ\x91orL\x90\x14e(¦\x06\x05#\x1c®5Á\x960\x90\x8aèLkq÷µÈWPâ¶@Ä±ÊF6\x92vC(Î\x9a\x16Ü¨\x15ÈÉÎ¢6$\x84@Âk»\x91m8\x9anëÈí|^I\x10%\x8fË¾L\x87{êå¸±x.ªF5äfc=³\x09Àd?O*-Õ3Pë¸x\x96C\x11e8Æ@\x11=ÙÒ²\x1b,ÌÞ"³ì6¹¡Ù;\x9d<>\x8e¥Ug nS²\x097\x92\x06¾\x85\x0dý±Î\x01Ë,Å\x9bÌ\x97VL\x1e\x14\x17u\x14F\x98C­\x022\x81Z\x9bHñ\x1cKíÚ\x99»\x18\x95/\x92\x16ýf¨[0m" 400 -
2025-07-08 15:57:47,647 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:47] code 400, message Bad request version ('¬©H\x1b/.ä¿G\x8fýB³\x81L#·G0\x7fOQdýji#\x8c¦¾A\\«\x159¿\x0eÜHloç¹OFÛõÕ£\x02"\\=bÍ\x17\x17qä§Ënô\'V\x8fFwZ\'aE©üþ$\x9e\\:Úåí\x0e\x94\x904»\x00\x17\x00\x00\x00')
2025-07-08 15:57:47,648 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:47] "[31m[1m\x16\x03\x01\x06²\x01\x00\x06®\x03\x03WZ\x07\x08ìñ\x88C;üäê¯Xå\x83fé\x81ËÌ@ÅhP¾\x0ee¡\x99Ü\x9e °Öl¬\x14Ûo\x06§^\x09\x98Î3j\x8a¶+a\x8dàí¿Ãà\x8e¯X%\x1c½[\x00 \x1a\x1a\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06E\x1a\x1a\x00\x00DÍ\x00\x05\x00\x03\x02h2\x00\x12\x00\x00\x00\x1b\x00\x03\x02\x00\x02þ\x0d\x00º\x00\x00\x01\x00\x01y\x00 \x1eN²c­\x9bÕ©Â^'`\x1a\x03é\x7fchY ÷C\x8fÑ\x9dûí\x18\x9d\x8b±L\x00\x90Ãjªè\x03dò\x8b\x06Ôº\x14â¢\x8eÁ5!\x8fbpN©;bãpS7~øÏ\x059\x91mÉ\x94qE¿Ýò¿ÔV7¶\x92\x99õî\x05\x8eÀU\x0d¬©H\x1b/.ä¿G\x8fýB³\x81L#·G0\x7fOQdýji#\x8c¦¾A\\«\x159¿\x0eÜHloç¹OFÛõÕ£\x02"\\=bÍ\x17\x17qä§Ënô'V\x8fFwZ'aE©üþ$\x9e\\:Úåí\x0e\x94\x904»\x00\x17\x00\x00\x00[0m" 400 -
2025-07-08 15:57:47,649 - werkzeug - ERROR - 1******** - - [08/Jul/2025 15:57:47] code 400, message Bad request version ('\x02h2\x08http/1.1\x00#\x00\x00\x00')
2025-07-08 15:57:47,649 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:57:47] "[31m[1m\x16\x03\x01\x06Ò\x01\x00\x06Î\x03\x03}Ò\x9ao£O¥êõ£}\x0c\x82\x95\x8b1¹D@êë\x03WÁ5ð²åÎvÆ° FüIn19ðM.Jw\x91AuEÔÃ¢á\x8c\x13¨Y\x11Z_Rá\x03\x01\x0bP\x00 ºº\x13\x01\x13\x02\x13\x03À+À/À,À0Ì©Ì¨À\x13À\x14\x00\x9c\x00\x9d\x00/\x005\x01\x00\x06e**\x00\x00\x00\x10\x00\x0e\x00\x0c\x02h2\x08http/1.1\x00#\x00\x00\x00[0m" 400 -
2025-07-08 15:58:40,443 - root - INFO - Request path: /, Method: GET
2025-07-08 15:58:40,444 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 15:58:40,444 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 15:58:40,445 - werkzeug - INFO - 1******** - - [08/Jul/2025 15:58:40] "GET / HTTP/1.1" 200 -
2025-07-08 16:10:42,382 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-07-08 16:10:43,022 - werkzeug - INFO -  * Restarting with stat
2025-07-08 16:10:43,508 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 16:10:43,515 - werkzeug - INFO -  * Debugger PIN: 824-504-881
2025-07-08 16:19:05,517 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-07-08 16:19:06,056 - werkzeug - INFO -  * Restarting with stat
2025-07-08 16:19:06,748 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 16:19:06,759 - werkzeug - INFO -  * Debugger PIN: 824-504-881
2025-07-08 16:20:10,435 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-07-08 16:20:11,031 - werkzeug - INFO -  * Restarting with stat
2025-07-08 16:20:11,564 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 16:20:11,575 - werkzeug - INFO -  * Debugger PIN: 824-504-881
2025-07-08 16:20:23,701 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/subscription.py', reloading
2025-07-08 16:20:24,303 - werkzeug - INFO -  * Restarting with stat
2025-07-08 16:20:24,781 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 16:20:24,788 - werkzeug - INFO -  * Debugger PIN: 824-504-881
2025-07-08 16:21:33,557 - root - INFO - Request path: /favicon.ico, Method: GET
2025-07-08 16:21:33,559 - root - INFO - Session data: {'_permanent': True, 'csrf_token': '5862a8ed50a44ff1839f0f87f73a5c5c26bb4e87', 'user_id': '686519e5d9149ab3af7adb6e'}
2025-07-08 16:21:33,559 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'bJV3e7QukiU2x7KLiwAw1w2CrYz5JOhFKF4y1pVMtQM.88BLYb5jDpNmi8qGdNPQ58_3ylQ')])
2025-07-08 16:21:33,566 - werkzeug - INFO - 1******** - - [08/Jul/2025 16:21:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 19:52:20,257 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-17 19:52:20,257 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-17 19:52:20,258 - werkzeug - INFO -  * Restarting with stat
2025-07-17 19:52:20,593 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 19:52:20,598 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 19:52:27,787 - root - INFO - Request path: /, Method: GET
2025-07-17 19:52:27,788 - root - INFO - Session data: {'_permanent': True}
2025-07-17 19:52:27,788 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 19:52:27,799 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:27] "GET / HTTP/1.1" 200 -
2025-07-17 19:52:28,385 - root - INFO - Request path: /favicon.ico, Method: GET
2025-07-17 19:52:28,385 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d'}
2025-07-17 19:52:28,385 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:52:28,386 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 19:52:32,505 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-07-17 19:52:32,506 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d'}
2025-07-17 19:52:32,506 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:52:34,089 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-17 19:52:34,089 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-07-17 19:52:34,089 - twilio.http_client - INFO - Headers:
2025-07-17 19:52:34,089 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-17 19:52:34,089 - twilio.http_client - INFO - Accept : application/json
2025-07-17 19:52:34,089 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-07-17 19:52:34,089 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-07-17 19:52:34,089 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-17 19:52:34,089 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-17 19:52:35,363 - twilio.http_client - INFO - Response Status Code: 201
2025-07-17 19:52:35,364 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 17 Jul 2025 14:22:35 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQf6fe62db99e70843d94567c9a1f8f9cd', 'Twilio-Request-Duration': '0.194', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 b097aefce5c96f6d140009a88452ea4a.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': 'kfH9L51VvivJI9TFmCDflKLC_Tx8hi5vX7i2GAgdIO36YzPxAm8z_A==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-17 19:52:35,364 - root - INFO - OTP sent to +************, Twilio SID: SMf6fe62db99e70843d94567c9a1f8f9cd
2025-07-17 19:52:35,370 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:35] "POST /auth/send-otp HTTP/1.1" 200 -
2025-07-17 19:52:50,270 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-07-17 19:52:50,270 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d'}
2025-07-17 19:52:50,270 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:52:51,129 - root - INFO - Deleted OTP record for mobile: +************
2025-07-17 19:52:51,950 - root - INFO - User logged in - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 19:52:51,950 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:52,362 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:52] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-07-17 19:52:52,370 - root - INFO - Request path: /connect, Method: GET
2025-07-17 19:52:52,370 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:52,370 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:52:52,370 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:52,370 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 19:52:52,375 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:52] "GET /connect HTTP/1.1" 200 -
2025-07-17 19:52:56,952 - root - INFO - Request path: /linkedin/login, Method: GET
2025-07-17 19:52:56,953 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:56,953 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:52:56,953 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:57,216 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 19:52:57,217 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:52:57,218 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:52:57] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-17 19:56:41,001 - root - INFO - Request path: /, Method: GET
2025-07-17 19:56:41,001 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:41,001 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:56:41,003 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:56:41] "GET / HTTP/1.1" 200 -
2025-07-17 19:56:44,204 - root - INFO - Request path: /connect, Method: GET
2025-07-17 19:56:44,204 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:44,205 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:56:44,205 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:44,205 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 19:56:44,207 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:56:44] "GET /connect HTTP/1.1" 200 -
2025-07-17 19:56:50,726 - root - INFO - Request path: /connect, Method: GET
2025-07-17 19:56:50,726 - root - INFO - Session data: {'_permanent': True}
2025-07-17 19:56:50,727 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 19:56:50,727 - root - INFO - Connect route - Session: {'_permanent': True}
2025-07-17 19:56:50,727 - root - ERROR - Connect route - No user_id in session
2025-07-17 19:56:50,731 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:56:50] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-07-17 19:56:50,739 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-07-17 19:56:50,739 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'Please log in to access your connected accounts')]}
2025-07-17 19:56:50,740 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'UxUN-J3ljwIj9lyfiHFgFrIWC4mrRESLs5AxWP45REY.-OnPF9kcsZRsbIDZZniZ2QOFy9U')])
2025-07-17 19:56:50,742 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:56:50] "GET /auth/send-otp?next=/connect HTTP/1.1" 200 -
2025-07-17 19:56:57,852 - root - INFO - Request path: /linkedin/login, Method: GET
2025-07-17 19:56:57,852 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:57,853 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 19:56:57,853 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:58,191 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 19:56:58,192 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 19:56:58,194 - werkzeug - INFO - 1******** - - [17/Jul/2025 19:56:58] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-17 20:04:00,978 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:04:01,439 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:04:01,847 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:04:01,850 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:04:05,936 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:04:05,936 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:04:05,936 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 20:04:05,936 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:04:05,936 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:04:05,942 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:04:05] "GET /connect HTTP/1.1" 200 -
2025-07-17 20:04:10,098 - root - INFO - Request path: /linkedin/login, Method: GET
2025-07-17 20:04:10,098 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:04:10,098 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 20:04:10,098 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:04:16,297 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:04:16,297 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:04:16,299 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:04:16] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-17 20:05:16,665 - root - INFO - Request path: /linkedin/callback, Method: GET
2025-07-17 20:05:16,665 - root - INFO - Session data: {'_permanent': True}
2025-07-17 20:05:16,666 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 20:05:16,666 - root - INFO - LinkedIn callback - Session at start: {'_permanent': True}
2025-07-17 20:05:16,666 - root - INFO - Extracted user_id from state: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:05:18,450 - root - INFO - Using user_id from state parameter: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:05:18,451 - root - INFO - LinkedIn callback - Using user_id: 685e9e1bd9149ab3af7adb6b of type <class 'str'>
2025-07-17 20:05:18,451 - root - INFO - Saving profile with user_id: 685e9e1bd9149ab3af7adb6b of type <class 'bson.objectid.ObjectId'>
2025-07-17 20:05:18,850 - root - INFO - Profile save result - matched: 0, modified: 0, upserted: 68790a267241071421c4e04d
2025-07-17 20:05:18,850 - root - ERROR - Error in LinkedIn callback: cannot access local variable 'flash' where it is not associated with a value
Traceback (most recent call last):
  File "/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py", line 120, in callback
    flash("LinkedIn account connected successfully!")
    ^^^^^
UnboundLocalError: cannot access local variable 'flash' where it is not associated with a value
2025-07-17 20:05:18,852 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:05:18] "[35m[1mGET /linkedin/callback?code=AQQDaar5b8K4oAlxUC9HHENJlfHuA2RQu44HRaZz49nF0EWRlMPzS1ejlGafwbeTnH2CpWnfwqAEirYhyeCMR_Ka1Y_rpkXQr1sO9Ix__ifN0beS0JE3VWRG8u_q_VMjvBZo6Rwmd1dLjG1zsgkTq71Beh9TWyDfHLbvHb-5tYxlqU3MbODWypi0c0YjtCBbvpES89UHFV-w9Mv7wks&state=agenticoauth_685e9e1bd9149ab3af7adb6b HTTP/1.1[0m" 500 -
2025-07-17 20:05:19,456 - root - INFO - Request path: /favicon.ico, Method: GET
2025-07-17 20:05:19,456 - root - INFO - Session data: {'_permanent': True}
2025-07-17 20:05:19,457 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 20:05:19,457 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:05:19] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-17 20:06:17,764 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:06:18,373 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:06:18,766 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:06:18,770 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:06:34,896 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:06:35,449 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:06:35,815 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:06:35,817 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:06:39,852 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:06:40,353 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:06:40,723 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:06:40,725 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:06:45,122 - root - INFO - Request path: /linkedin/callback, Method: GET
2025-07-17 20:06:45,123 - root - INFO - Session data: {'_permanent': True}
2025-07-17 20:06:45,124 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 20:06:45,124 - root - INFO - LinkedIn callback - Session at start: {'_permanent': True}
2025-07-17 20:06:45,124 - root - INFO - Extracted user_id from state: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:06:45,914 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:06:45] "[31m[1mGET /linkedin/callback?code=AQQDaar5b8K4oAlxUC9HHENJlfHuA2RQu44HRaZz49nF0EWRlMPzS1ejlGafwbeTnH2CpWnfwqAEirYhyeCMR_Ka1Y_rpkXQr1sO9Ix__ifN0beS0JE3VWRG8u_q_VMjvBZo6Rwmd1dLjG1zsgkTq71Beh9TWyDfHLbvHb-5tYxlqU3MbODWypi0c0YjtCBbvpES89UHFV-w9Mv7wks&state=agenticoauth_685e9e1bd9149ab3af7adb6b HTTP/1.1[0m" 400 -
2025-07-17 20:06:55,070 - root - INFO - Request path: /linkedin/callback, Method: GET
2025-07-17 20:06:55,071 - root - INFO - Session data: {'_permanent': True}
2025-07-17 20:06:55,071 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 20:06:55,071 - root - INFO - LinkedIn callback - Session at start: {'_permanent': True}
2025-07-17 20:06:55,071 - root - INFO - Extracted user_id from state: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:06:55,848 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:06:55] "[31m[1mGET /linkedin/callback?code=AQQDaar5b8K4oAlxUC9HHENJlfHuA2RQu44HRaZz49nF0EWRlMPzS1ejlGafwbeTnH2CpWnfwqAEirYhyeCMR_Ka1Y_rpkXQr1sO9Ix__ifN0beS0JE3VWRG8u_q_VMjvBZo6Rwmd1dLjG1zsgkTq71Beh9TWyDfHLbvHb-5tYxlqU3MbODWypi0c0YjtCBbvpES89UHFV-w9Mv7wks&state=agenticoauth_685e9e1bd9149ab3af7adb6b HTTP/1.1[0m" 400 -
2025-07-17 20:06:58,324 - root - INFO - Request path: /, Method: GET
2025-07-17 20:06:58,324 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:06:58,324 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 20:06:58,330 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:06:58] "GET / HTTP/1.1" 200 -
2025-07-17 20:07:00,677 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:07:00,677 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:07:00,677 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 20:07:00,677 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:07:00,678 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:07:00,681 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:00] "GET /connect HTTP/1.1" 200 -
2025-07-17 20:07:03,162 - root - INFO - Request path: /linkedin/login, Method: GET
2025-07-17 20:07:03,162 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:07:03,163 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ZF9gB74mlL19t4Hael1hI_IUKcXwBErCQZWr4eMR_Fg.Nq2pU9SAZjfJga8rTsjRXS1VdsQ')])
2025-07-17 20:07:03,163 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:07:04,550 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:07:04,551 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, 'csrf_token': 'd2c7c9f517e0a7d4c07ed7894bbd2e09bb3b551d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:07:04,553 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:04] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-17 20:07:07,886 - root - INFO - Request path: /linkedin/callback, Method: GET
2025-07-17 20:07:07,886 - root - INFO - Session data: {'_permanent': True}
2025-07-17 20:07:07,886 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-17 20:07:07,886 - root - INFO - LinkedIn callback - Session at start: {'_permanent': True}
2025-07-17 20:07:07,887 - root - INFO - Extracted user_id from state: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:07:09,924 - root - INFO - Using user_id from state parameter: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:07:09,924 - root - INFO - LinkedIn callback - Using user_id: 685e9e1bd9149ab3af7adb6b of type <class 'str'>
2025-07-17 20:07:09,924 - root - INFO - Saving profile with user_id: 685e9e1bd9149ab3af7adb6b of type <class 'bson.objectid.ObjectId'>
2025-07-17 20:07:10,333 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-07-17 20:07:10,335 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:10] "GET /linkedin/callback?code=AQTMyJ83q2DzIaX2Uvyldq_xiVEz83q7H9qfsiK0qmQKFmDunQJ1F6-Od2ZRVwfIDVUYhbBL6BRdl-r44BZZwLGVlu8xGZ4snhCD5QIAuUTIeFWoaYzJzo5r9e4XENHJJQyK11FF8OsPpUog1uvTtNJWGnC36jAHqfF9zdbYWLYck3K8G8l0icNsCwY2l8_q27WntVsHn3C1u-yoaSY&state=agenticoauth_685e9e1bd9149ab3af7adb6b HTTP/1.1" 200 -
2025-07-17 20:07:13,289 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:07:13,289 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!')]}
2025-07-17 20:07:13,289 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:13,290 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!')]}
2025-07-17 20:07:13,290 - root - ERROR - Connect route - No user_id in session
2025-07-17 20:07:13,291 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:13] "[32mGET /connect?csrf_token= HTTP/1.1[0m" 302 -
2025-07-17 20:07:13,308 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-07-17 20:07:13,309 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts')]}
2025-07-17 20:07:13,309 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:13,311 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:13] "GET /auth/send-otp?next=/connect HTTP/1.1" 200 -
2025-07-17 20:07:33,506 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:07:33,507 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:33,507 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:33,508 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:33,508 - root - ERROR - Connect route - No user_id in session
2025-07-17 20:07:33,510 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:33] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-07-17 20:07:33,526 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-07-17 20:07:33,526 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:33,527 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:33,529 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:33] "GET /auth/send-otp?next=/connect HTTP/1.1" 200 -
2025-07-17 20:07:39,417 - root - INFO - Request path: /, Method: GET
2025-07-17 20:07:39,417 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:39,417 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:39,419 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:39] "GET / HTTP/1.1" 200 -
2025-07-17 20:07:43,582 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:07:43,583 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:43,583 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:43,583 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:43,583 - root - ERROR - Connect route - No user_id in session
2025-07-17 20:07:43,585 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:43] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-07-17 20:07:43,596 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-07-17 20:07:43,596 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:07:43,596 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:07:43,599 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:07:43] "GET /auth/send-otp?next=/connect HTTP/1.1" 200 -
2025-07-17 20:14:39,035 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:14:39,499 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:14:40,010 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:14:40,013 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:17:01,982 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:17:02,535 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:17:02,846 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:17:02,848 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:27:37,438 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:27:37,700 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:27:38,500 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:27:38,506 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:28:33,920 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-17 20:28:34,499 - werkzeug - INFO -  * Restarting with stat
2025-07-17 20:28:35,124 - werkzeug - WARNING -  * Debugger is active!
2025-07-17 20:28:35,127 - werkzeug - INFO -  * Debugger PIN: 914-557-472
2025-07-17 20:28:39,662 - root - INFO - Request path: /auth/send-otp, Method: GET
2025-07-17 20:28:39,663 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:28:39,663 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:28:39,673 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:28:39] "GET /auth/send-otp?next=/connect HTTP/1.1" 200 -
2025-07-17 20:28:55,522 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-07-17 20:28:55,522 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:28:55,522 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:28:57,170 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-17 20:28:57,170 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-07-17 20:28:57,170 - twilio.http_client - INFO - Headers:
2025-07-17 20:28:57,170 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-17 20:28:57,170 - twilio.http_client - INFO - Accept : application/json
2025-07-17 20:28:57,170 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-07-17 20:28:57,170 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-07-17 20:28:57,170 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-17 20:28:57,170 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-17 20:28:58,474 - twilio.http_client - INFO - Response Status Code: 201
2025-07-17 20:28:58,474 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Thu, 17 Jul 2025 14:58:58 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ55ab095205e1287098d2d7038d39de87', 'Twilio-Request-Duration': '0.152', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 0df028dce5ff77b038214b2a6b5e7eaa.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '2hnISYnlskRKhvz7uIxbK54L464lQursq2N0wQ3CSLV6fGak8hBIQA==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-17 20:28:58,475 - root - INFO - OTP sent to +************, Twilio SID: SM55ab095205e1287098d2d7038d39de87
2025-07-17 20:28:58,481 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:28:58] "POST /auth/send-otp HTTP/1.1" 200 -
2025-07-17 20:29:10,657 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-07-17 20:29:10,658 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d'}
2025-07-17 20:29:10,658 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:29:11,272 - root - INFO - Deleted OTP record for mobile: +************
2025-07-17 20:29:11,876 - root - INFO - User logged in - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:29:11,877 - root - INFO - Session after login: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:12,195 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:29:12] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-07-17 20:29:12,205 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:29:12,205 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:12,205 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:29:12,206 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:12,206 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:29:12,210 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:29:12] "GET /connect HTTP/1.1" 200 -
2025-07-17 20:29:14,194 - root - INFO - Request path: /linkedin/login, Method: GET
2025-07-17 20:29:14,195 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:14,195 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:29:14,195 - root - INFO - login_required check - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:14,461 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:29:14,461 - root - INFO - LinkedIn login - Session before redirect: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:14,463 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:29:14] "[32mGET /linkedin/login HTTP/1.1[0m" 302 -
2025-07-17 20:29:24,308 - root - INFO - Request path: /linkedin/callback, Method: GET
2025-07-17 20:29:24,308 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:24,308 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:29:24,309 - root - INFO - LinkedIn callback - Session at start: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:24,309 - root - INFO - Extracted user_id from state: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:29:25,983 - root - INFO - LinkedIn callback - Using user_id: 685e9e1bd9149ab3af7adb6b of type <class 'str'>
2025-07-17 20:29:25,983 - root - INFO - Saving profile with user_id: 685e9e1bd9149ab3af7adb6b of type <class 'bson.objectid.ObjectId'>
2025-07-17 20:29:26,325 - root - INFO - Profile save result - matched: 1, modified: 1, upserted: None
2025-07-17 20:29:26,327 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:29:26] "GET /linkedin/callback?code=AQTP7r_D0_rgsBIulOWXzJCCvbHpa45_v3T59fuSrsMcMNcToVNOTigSGYFneeaP9nSxmlEVCNXSm9NWegnla3HFkboiQm2eo36J_bKS67hLw051iwf0qLaYPiJh0vwpYgld3B4-lPOqnV310_jZlXdAkdN7dTWfXk5UVhR7t1nrLPGrXhLgi-6jsVRnonSpfdy6EtFKCEhNn0MW6Rc&state=agenticoauth_685e9e1bd9149ab3af7adb6b HTTP/1.1" 200 -
2025-07-17 20:29:28,831 - root - INFO - Request path: /connect, Method: GET
2025-07-17 20:29:28,832 - root - INFO - Session data: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'LinkedIn account connected successfully!')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:28,832 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'ABx-Zu3R-IG47uLnyT1Qsd91WIOtB7_0tzQknAYWOEg.UPJ9-CIhBn1USdDCGEc9K24rLZY')])
2025-07-17 20:29:28,832 - root - INFO - Connect route - Session: {'_permanent': True, '_flashes': [('message', 'LinkedIn account connected successfully!'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'Please log in to access your connected accounts'), ('message', 'LinkedIn account connected successfully!')], 'csrf_token': '8e22ceceab28a6f6a4d8e23e998837ffd5090c1d', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-17 20:29:28,833 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-17 20:29:28,835 - werkzeug - INFO - 1******** - - [17/Jul/2025 20:29:28] "GET /connect?csrf_token=8e22ceceab28a6f6a4d8e23e998837ffd5090c1d HTTP/1.1" 200 -
2025-07-18 13:20:48,097 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-18 13:20:48,098 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 13:20:48,099 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:20:48,444 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:20:48,450 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:20:51,326 - root - INFO - Request path: /, Method: GET
2025-07-18 13:20:51,327 - root - INFO - Session data: {'_permanent': True}
2025-07-18 13:20:51,327 - root - INFO - Request cookies: ImmutableMultiDict([])
2025-07-18 13:20:51,335 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:20:51] "GET / HTTP/1.1" 200 -
2025-07-18 13:20:51,969 - root - INFO - Request path: /favicon.ico, Method: GET
2025-07-18 13:20:51,969 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4'}
2025-07-18 13:20:51,969 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:20:51,971 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:20:51] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-18 13:21:19,261 - root - INFO - Request path: /auth/send-otp, Method: POST
2025-07-18 13:21:19,261 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4'}
2025-07-18 13:21:19,261 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:21:21,384 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-18 13:21:21,384 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
2025-07-18 13:21:21,385 - twilio.http_client - INFO - Headers:
2025-07-18 13:21:21,385 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-18 13:21:21,385 - twilio.http_client - INFO - Accept : application/json
2025-07-18 13:21:21,385 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.3 (Linux x86_64) Python/3.12.3
2025-07-18 13:21:21,385 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.3
2025-07-18 13:21:21,385 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-18 13:21:21,385 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-18 13:21:22,912 - twilio.http_client - INFO - Response Status Code: 201
2025-07-18 13:21:22,912 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '827', 'Connection': 'keep-alive', 'Date': 'Fri, 18 Jul 2025 07:51:22 GMT', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ7f619aaeff7be4be18586e0e040f1d8d', 'Twilio-Request-Duration': '0.162', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=31536000', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 b58fb22cae083768666eb24f7e53ca1c.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'DEL54-P3', 'X-Amz-Cf-Id': '7pgOVUk-0A79OiRl7eXDBNWq2rWlc8ZOwqfgDzUlxPV7ZyaqOAIeKw==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-18 13:21:22,912 - root - INFO - OTP sent to +************, Twilio SID: SM7f619aaeff7be4be18586e0e040f1d8d
2025-07-18 13:21:22,919 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:21:22] "POST /auth/send-otp HTTP/1.1" 200 -
2025-07-18 13:21:36,691 - root - INFO - Request path: /auth/verify-otp, Method: POST
2025-07-18 13:21:36,691 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4'}
2025-07-18 13:21:36,691 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:21:37,245 - root - INFO - Deleted OTP record for mobile: +************
2025-07-18 13:21:37,820 - root - INFO - User logged in - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:21:37,820 - root - INFO - Session after login: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:21:38,167 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:21:38] "[32mPOST /auth/verify-otp HTTP/1.1[0m" 302 -
2025-07-18 13:21:38,172 - root - INFO - Request path: /connect, Method: GET
2025-07-18 13:21:38,172 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:21:38,172 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:21:38,172 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:21:38,172 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:21:38,175 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:21:38] "GET /connect HTTP/1.1" 200 -
2025-07-18 13:22:48,276 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-07-18 13:22:48,817 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:22:49,080 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:22:49,082 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:23:08,162 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-07-18 13:23:08,613 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:23:08,939 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:23:08,940 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:23:21,990 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-18 13:23:22,475 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:23:22,752 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:23:22,754 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:26:48,040 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/linkedin.py', reloading
2025-07-18 13:26:48,369 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:26:48,647 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:26:48,650 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:26:52,679 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/youtube.py', reloading
2025-07-18 13:26:53,202 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:26:53,464 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:26:53,466 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:26:54,481 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/twitter.py', reloading
2025-07-18 13:26:55,023 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:26:55,273 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:26:55,275 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:29:08,741 - root - INFO - Request path: /connect, Method: GET
2025-07-18 13:29:08,742 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:29:08,742 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:29:08,742 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:29:08,742 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:29:08,746 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:29:08] "GET /connect HTTP/1.1" 200 -
2025-07-18 13:29:10,693 - root - INFO - Request path: /instagram/login, Method: GET
2025-07-18 13:29:10,693 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:29:10,693 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:29:10,693 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:29:11,893 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:29:11,893 - root - INFO - Instagram login - Session before redirect: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:29:11,895 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:29:11] "[32mGET /instagram/login HTTP/1.1[0m" 302 -
2025-07-18 13:31:04,677 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:31:05,030 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:31:05,294 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:31:05,296 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:31:11,038 - root - INFO - Request path: /, Method: GET
2025-07-18 13:31:11,038 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:11,038 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:31:11,041 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:31:11] "GET / HTTP/1.1" 200 -
2025-07-18 13:31:14,633 - root - INFO - Request path: /connect, Method: GET
2025-07-18 13:31:14,633 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:14,633 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:31:14,633 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:14,633 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:31:14,635 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:31:14] "GET /connect HTTP/1.1" 200 -
2025-07-18 13:31:17,058 - root - INFO - Request path: /instagram/login, Method: GET
2025-07-18 13:31:17,058 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:17,058 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:31:17,058 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:18,246 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:31:18,246 - root - INFO - Instagram login - Session before redirect: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:31:18,248 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:31:18] "[32mGET /instagram/login HTTP/1.1[0m" 302 -
2025-07-18 13:52:24,850 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:52:25,561 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:52:25,933 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:52:25,938 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:52:41,006 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:52:41,433 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:52:41,697 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:52:41,699 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:55:03,755 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-18 13:55:03,755 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 13:55:03,756 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:55:04,030 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:55:04,032 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:55:43,694 - root - INFO - Request path: /, Method: GET
2025-07-18 13:55:43,694 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:43,694 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:55:43,698 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:55:43] "GET / HTTP/1.1" 200 -
2025-07-18 13:55:53,206 - root - INFO - Request path: /connect, Method: GET
2025-07-18 13:55:53,206 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:53,206 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:55:53,206 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:53,207 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:55:53,210 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:55:53] "GET /connect HTTP/1.1" 200 -
2025-07-18 13:55:55,162 - root - INFO - Request path: /instagram/login, Method: GET
2025-07-18 13:55:55,162 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:55,162 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 13:55:55,162 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:56,195 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 13:55:56,195 - root - INFO - Instagram login - Session before redirect: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 13:55:56,197 - werkzeug - INFO - 1******** - - [18/Jul/2025 13:55:56] "[32mGET /instagram/login HTTP/1.1[0m" 302 -
2025-07-18 13:57:03,586 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:57:04,110 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:57:04,386 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:57:04,387 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:57:36,524 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:57:36,918 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:57:37,190 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:57:37,192 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 13:57:48,238 - werkzeug - INFO -  * Detected change in '/home/<USER>/Desktop/agentic_oauth_codebase/routes/instagram.py', reloading
2025-07-18 13:57:48,722 - werkzeug - INFO -  * Restarting with stat
2025-07-18 13:57:48,993 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 13:57:48,995 - werkzeug - INFO -  * Debugger PIN: 260-225-172
2025-07-18 14:00:36,412 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-18 14:00:36,412 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 14:00:36,413 - werkzeug - INFO -  * Restarting with stat
2025-07-18 14:00:36,705 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 14:00:36,708 - werkzeug - INFO -  * Debugger PIN: 448-746-111
2025-07-18 14:04:49,317 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-07-18 14:04:49,317 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-18 14:04:49,317 - werkzeug - INFO -  * Restarting with stat
2025-07-18 14:04:49,600 - werkzeug - WARNING -  * Debugger is active!
2025-07-18 14:04:49,602 - werkzeug - INFO -  * Debugger PIN: 448-746-111
2025-07-18 14:06:09,480 - root - INFO - Request path: /, Method: GET
2025-07-18 14:06:09,480 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:09,480 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:09,485 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:09] "GET / HTTP/1.1" 200 -
2025-07-18 14:06:11,731 - root - INFO - Request path: /connect, Method: GET
2025-07-18 14:06:11,731 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:11,731 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:11,731 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:11,731 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:11,734 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:11] "GET /connect HTTP/1.1" 200 -
2025-07-18 14:06:14,879 - root - INFO - Request path: /instagram/login, Method: GET
2025-07-18 14:06:14,879 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:14,879 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:14,879 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:16,025 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:16,025 - root - INFO - Instagram login - Session before redirect: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:16,025 - root - INFO - Instagram CLIENT_ID: 1039974727960405
2025-07-18 14:06:16,026 - root - INFO - Instagram REDIRECT_URI: http://localhost:5000/instagram/callback
2025-07-18 14:06:16,026 - root - INFO - Instagram SCOPES: user_profile,user_media
2025-07-18 14:06:16,026 - root - INFO - Instagram auth URL: https://api.instagram.com/oauth/authorize?client_id=1039974727960405&redirect_uri=http://localhost:5000/instagram/callback&scope=user_profile%2Cuser_media&response_type=code&state=agenticoauth_685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:16,028 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:16] "[32mGET /instagram/login HTTP/1.1[0m" 302 -
2025-07-18 14:06:42,836 - root - INFO - Request path: /connect, Method: GET
2025-07-18 14:06:42,836 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:42,836 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:42,837 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:42,837 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:42,838 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:42] "GET /connect HTTP/1.1" 200 -
2025-07-18 14:06:45,652 - root - INFO - Request path: /connect, Method: GET
2025-07-18 14:06:45,652 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:45,652 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:45,652 - root - INFO - Connect route - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:45,652 - root - INFO - Connect route - User ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:45,653 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:45] "GET /connect HTTP/1.1" 200 -
2025-07-18 14:06:47,612 - root - INFO - Request path: /instagram/login, Method: GET
2025-07-18 14:06:47,612 - root - INFO - Session data: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:47,612 - root - INFO - Request cookies: ImmutableMultiDict([('session', 'KeQwW8LmqZm8Uh4HzQaAKwgSS1WZKkjvJ9tjxcS9egU.FX-FWbnzA6loIbxKkxBnaumRfYY')])
2025-07-18 14:06:47,612 - root - INFO - login_required check - Session: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:47,914 - root - INFO - login_required - User is logged in with ID: 685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:47,914 - root - INFO - Instagram login - Session before redirect: {'_permanent': True, 'csrf_token': 'ce0b075baf4ec57acac094dffe1499616d9a5ec4', 'user_id': '685e9e1bd9149ab3af7adb6b'}
2025-07-18 14:06:47,914 - root - INFO - Instagram CLIENT_ID: 1039974727960405
2025-07-18 14:06:47,914 - root - INFO - Instagram REDIRECT_URI: http://localhost:5000/instagram/callback
2025-07-18 14:06:47,914 - root - INFO - Instagram SCOPES: user_profile,user_media
2025-07-18 14:06:47,914 - root - INFO - Instagram auth URL: https://api.instagram.com/oauth/authorize?client_id=1039974727960405&redirect_uri=http://localhost:5000/instagram/callback&scope=user_profile%2Cuser_media&response_type=code&state=agenticoauth_685e9e1bd9149ab3af7adb6b
2025-07-18 14:06:47,916 - werkzeug - INFO - 1******** - - [18/Jul/2025 14:06:47] "[32mGET /instagram/login HTTP/1.1[0m" 302 -
