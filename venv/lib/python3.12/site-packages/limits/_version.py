
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-06-16T08:51:06-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "8a5a8254708e2799a762b28482d2a4da7b71b04e",
 "version": "5.4.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
