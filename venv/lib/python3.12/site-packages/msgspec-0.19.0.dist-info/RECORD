msgspec-0.19.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
msgspec-0.19.0.dist-info/LICENSE,sha256=b96LZyOIXoUIgI9lrDcv65fT7uLqsrkdNNeWt1t6xzQ,1498
msgspec-0.19.0.dist-info/METADATA,sha256=8E21mTozMdjXm15-sfmsFJ07AXATjY-a0iQIYCeuJk8,6907
msgspec-0.19.0.dist-info/RECORD,,
msgspec-0.19.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
msgspec-0.19.0.dist-info/WHEEL,sha256=tRzqFuK6eFjpbf2xTNvU7E3xL2y00S_NWJvyqxej3BA,151
msgspec-0.19.0.dist-info/top_level.txt,sha256=8Ysnxa29QLhdPtjPGjyc1hmx3nOmKeu_OAk2682ZdvU,8
msgspec/__init__.py,sha256=6O0PLIjJk1HSPP3Pu5trzUoUEhKcIxXCqQ7MVa1Ylms,637
msgspec/__init__.pyi,sha256=A-GkzW1sZ9a-pe-58PzeneEyxJLmbudnBhUA83dYUdw,5566
msgspec/__pycache__/__init__.cpython-312.pyc,,
msgspec/__pycache__/_json_schema.cpython-312.pyc,,
msgspec/__pycache__/_utils.cpython-312.pyc,,
msgspec/__pycache__/_version.cpython-312.pyc,,
msgspec/__pycache__/inspect.cpython-312.pyc,,
msgspec/__pycache__/json.cpython-312.pyc,,
msgspec/__pycache__/msgpack.cpython-312.pyc,,
msgspec/__pycache__/structs.cpython-312.pyc,,
msgspec/__pycache__/toml.cpython-312.pyc,,
msgspec/__pycache__/yaml.cpython-312.pyc,,
msgspec/_core.cpython-312-x86_64-linux-gnu.so,sha256=e3xm_RrSxghoFv9Iqmpqy6ZwnBvxLblCQuONDtxdpzk,414176
msgspec/_json_schema.py,sha256=bKTi8sOiBuNyEUGJhrjnX4rUhYdjK09MtcDgU8mEo1E,16658
msgspec/_utils.py,sha256=JkY8T6NuCMReOLNNg6lJMrIfqWZ-Ifwj7BBhDNLvD4Q,10101
msgspec/_version.py,sha256=Dee0CCrWdKy6ga2lPQyuoDgvEhPjYqm6g2dX_XxIxeE,498
msgspec/inspect.py,sha256=uHY4bwjrfNE4UP6rU1PwaZ3-QeyClw-5hpWyXIuFhq4,28896
msgspec/json.py,sha256=kIzpm2NHszkAfAbNcemxq_hUHKt3qxH29yABKH0MnTs,212
msgspec/json.pyi,sha256=nLEHvthIgoMeO_USzf12zcryCZra423VTFGcgdoqB50,3082
msgspec/msgpack.py,sha256=JSRLcEqpyCgwy_wD1loF3qHN_ytnCQLc8RX33uATbCU,154
msgspec/msgpack.pyi,sha256=le3E6_MlrUfFTlru6iPSxBOx24WsXAKpOpWGQ85DPQo,2641
msgspec/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
msgspec/structs.py,sha256=owU3O3w8yi4_lQYAHIRwM0Hre0Oo_puNbz4SM0GP0ew,2942
msgspec/structs.pyi,sha256=6ob7yIjD6QwMUn_vJHC0CgWgt3CtgRAU2AOUq_1EPGU,902
msgspec/toml.py,sha256=eXILfJYg9PPkWoJwHyhxoRH8EmuPrmsM2kYfUQH6y8o,5526
msgspec/yaml.py,sha256=TauuurrHjdm4bb-qIepncZLzg02vse8x4Ju5Rweu8l8,5395
