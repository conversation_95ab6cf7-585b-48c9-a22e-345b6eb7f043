r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.messaging.v2.channels_sender import ChannelsSenderList


class V2(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V2 version of Messaging

        :param domain: The Twilio.messaging domain
        """
        super().__init__(domain, "v2")
        self._channels_senders: Optional[ChannelsSenderList] = None

    @property
    def channels_senders(self) -> ChannelsSenderList:
        if self._channels_senders is None:
            self._channels_senders = ChannelsSenderList(self)
        return self._channels_senders

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V2>"
