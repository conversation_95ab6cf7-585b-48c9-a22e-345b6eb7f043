r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import date
from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class DeactivationsInstance(InstanceResource):
    """
    :ivar redirect_to: Returns an authenticated url that redirects to a file containing the deactivated numbers for the requested day. This url is valid for up to two minutes.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.redirect_to: Optional[str] = payload.get("redirect_to")

        self._context: Optional[DeactivationsContext] = None

    @property
    def _proxy(self) -> "DeactivationsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DeactivationsContext for this DeactivationsInstance
        """
        if self._context is None:
            self._context = DeactivationsContext(
                self._version,
            )
        return self._context

    def fetch(
        self, date: Union[date, object] = values.unset
    ) -> "DeactivationsInstance":
        """
        Fetch the DeactivationsInstance

        :param date: The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.

        :returns: The fetched DeactivationsInstance
        """
        return self._proxy.fetch(
            date=date,
        )

    async def fetch_async(
        self, date: Union[date, object] = values.unset
    ) -> "DeactivationsInstance":
        """
        Asynchronous coroutine to fetch the DeactivationsInstance

        :param date: The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.

        :returns: The fetched DeactivationsInstance
        """
        return await self._proxy.fetch_async(
            date=date,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Messaging.V1.DeactivationsInstance>"


class DeactivationsContext(InstanceContext):

    def __init__(self, version: Version):
        """
        Initialize the DeactivationsContext

        :param version: Version that contains the resource
        """
        super().__init__(version)

        self._uri = "/Deactivations"

    def fetch(self, date: Union[date, object] = values.unset) -> DeactivationsInstance:
        """
        Fetch the DeactivationsInstance

        :param date: The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.

        :returns: The fetched DeactivationsInstance
        """

        data = values.of(
            {
                "Date": serialize.iso8601_date(date),
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return DeactivationsInstance(
            self._version,
            payload,
        )

    async def fetch_async(
        self, date: Union[date, object] = values.unset
    ) -> DeactivationsInstance:
        """
        Asynchronous coroutine to fetch the DeactivationsInstance

        :param date: The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.

        :returns: The fetched DeactivationsInstance
        """

        data = values.of(
            {
                "Date": serialize.iso8601_date(date),
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return DeactivationsInstance(
            self._version,
            payload,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Messaging.V1.DeactivationsContext>"


class DeactivationsList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the DeactivationsList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self) -> DeactivationsContext:
        """
        Constructs a DeactivationsContext

        """
        return DeactivationsContext(self._version)

    def __call__(self) -> DeactivationsContext:
        """
        Constructs a DeactivationsContext

        """
        return DeactivationsContext(self._version)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.DeactivationsList>"
