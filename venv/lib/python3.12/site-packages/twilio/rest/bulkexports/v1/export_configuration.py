r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Bulkexports
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ExportConfigurationInstance(InstanceResource):
    """
    :ivar enabled: If true, <PERSON><PERSON><PERSON> will automatically generate every day's file when the day is over.
    :ivar webhook_url: Stores the URL destination for the method specified in webhook_method.
    :ivar webhook_method: Sets whether Twilio should call a webhook URL when the automatic generation is complete, using GET or POST. The actual destination is set in the webhook_url
    :ivar resource_type: The type of communication – Messages, Calls, Conferences, and Participants
    :ivar url: The URL of this resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        resource_type: Optional[str] = None,
    ):
        super().__init__(version)

        self.enabled: Optional[bool] = payload.get("enabled")
        self.webhook_url: Optional[str] = payload.get("webhook_url")
        self.webhook_method: Optional[str] = payload.get("webhook_method")
        self.resource_type: Optional[str] = payload.get("resource_type")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "resource_type": resource_type or self.resource_type,
        }
        self._context: Optional[ExportConfigurationContext] = None

    @property
    def _proxy(self) -> "ExportConfigurationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ExportConfigurationContext for this ExportConfigurationInstance
        """
        if self._context is None:
            self._context = ExportConfigurationContext(
                self._version,
                resource_type=self._solution["resource_type"],
            )
        return self._context

    def fetch(self) -> "ExportConfigurationInstance":
        """
        Fetch the ExportConfigurationInstance


        :returns: The fetched ExportConfigurationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ExportConfigurationInstance":
        """
        Asynchronous coroutine to fetch the ExportConfigurationInstance


        :returns: The fetched ExportConfigurationInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        enabled: Union[bool, object] = values.unset,
        webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
    ) -> "ExportConfigurationInstance":
        """
        Update the ExportConfigurationInstance

        :param enabled: If true, Twilio will automatically generate every day's file when the day is over.
        :param webhook_url: Stores the URL destination for the method specified in webhook_method.
        :param webhook_method: Sets whether Twilio should call a webhook URL when the automatic generation is complete, using GET or POST. The actual destination is set in the webhook_url

        :returns: The updated ExportConfigurationInstance
        """
        return self._proxy.update(
            enabled=enabled,
            webhook_url=webhook_url,
            webhook_method=webhook_method,
        )

    async def update_async(
        self,
        enabled: Union[bool, object] = values.unset,
        webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
    ) -> "ExportConfigurationInstance":
        """
        Asynchronous coroutine to update the ExportConfigurationInstance

        :param enabled: If true, Twilio will automatically generate every day's file when the day is over.
        :param webhook_url: Stores the URL destination for the method specified in webhook_method.
        :param webhook_method: Sets whether Twilio should call a webhook URL when the automatic generation is complete, using GET or POST. The actual destination is set in the webhook_url

        :returns: The updated ExportConfigurationInstance
        """
        return await self._proxy.update_async(
            enabled=enabled,
            webhook_url=webhook_url,
            webhook_method=webhook_method,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Bulkexports.V1.ExportConfigurationInstance {}>".format(context)


class ExportConfigurationContext(InstanceContext):

    def __init__(self, version: Version, resource_type: str):
        """
        Initialize the ExportConfigurationContext

        :param version: Version that contains the resource
        :param resource_type: The type of communication – Messages, Calls, Conferences, and Participants
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "resource_type": resource_type,
        }
        self._uri = "/Exports/{resource_type}/Configuration".format(**self._solution)

    def fetch(self) -> ExportConfigurationInstance:
        """
        Fetch the ExportConfigurationInstance


        :returns: The fetched ExportConfigurationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ExportConfigurationInstance(
            self._version,
            payload,
            resource_type=self._solution["resource_type"],
        )

    async def fetch_async(self) -> ExportConfigurationInstance:
        """
        Asynchronous coroutine to fetch the ExportConfigurationInstance


        :returns: The fetched ExportConfigurationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ExportConfigurationInstance(
            self._version,
            payload,
            resource_type=self._solution["resource_type"],
        )

    def update(
        self,
        enabled: Union[bool, object] = values.unset,
        webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
    ) -> ExportConfigurationInstance:
        """
        Update the ExportConfigurationInstance

        :param enabled: If true, Twilio will automatically generate every day's file when the day is over.
        :param webhook_url: Stores the URL destination for the method specified in webhook_method.
        :param webhook_method: Sets whether Twilio should call a webhook URL when the automatic generation is complete, using GET or POST. The actual destination is set in the webhook_url

        :returns: The updated ExportConfigurationInstance
        """

        data = values.of(
            {
                "Enabled": serialize.boolean_to_string(enabled),
                "WebhookUrl": webhook_url,
                "WebhookMethod": webhook_method,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ExportConfigurationInstance(
            self._version, payload, resource_type=self._solution["resource_type"]
        )

    async def update_async(
        self,
        enabled: Union[bool, object] = values.unset,
        webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
    ) -> ExportConfigurationInstance:
        """
        Asynchronous coroutine to update the ExportConfigurationInstance

        :param enabled: If true, Twilio will automatically generate every day's file when the day is over.
        :param webhook_url: Stores the URL destination for the method specified in webhook_method.
        :param webhook_method: Sets whether Twilio should call a webhook URL when the automatic generation is complete, using GET or POST. The actual destination is set in the webhook_url

        :returns: The updated ExportConfigurationInstance
        """

        data = values.of(
            {
                "Enabled": serialize.boolean_to_string(enabled),
                "WebhookUrl": webhook_url,
                "WebhookMethod": webhook_method,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ExportConfigurationInstance(
            self._version, payload, resource_type=self._solution["resource_type"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Bulkexports.V1.ExportConfigurationContext {}>".format(context)


class ExportConfigurationList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ExportConfigurationList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, resource_type: str) -> ExportConfigurationContext:
        """
        Constructs a ExportConfigurationContext

        :param resource_type: The type of communication – Messages, Calls, Conferences, and Participants
        """
        return ExportConfigurationContext(self._version, resource_type=resource_type)

    def __call__(self, resource_type: str) -> ExportConfigurationContext:
        """
        Constructs a ExportConfigurationContext

        :param resource_type: The type of communication – Messages, Calls, Conferences, and Participants
        """
        return ExportConfigurationContext(self._version, resource_type=resource_type)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Bulkexports.V1.ExportConfigurationList>"
