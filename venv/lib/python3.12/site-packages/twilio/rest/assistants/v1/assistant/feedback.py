r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Assistants
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class FeedbackInstance(InstanceResource):

    class AssistantsV1ServiceCreateFeedbackRequest(object):
        """
        :ivar message_id: The message ID.
        :ivar score: The score to be given(0-1).
        :ivar session_id: The Session ID.
        :ivar text: The text to be given as feedback.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.message_id: Optional[str] = payload.get("message_id")
            self.score: Optional[float] = payload.get("score")
            self.session_id: Optional[str] = payload.get("session_id")
            self.text: Optional[str] = payload.get("text")

        def to_dict(self):
            return {
                "message_id": self.message_id,
                "score": self.score,
                "session_id": self.session_id,
                "text": self.text,
            }

    """
    :ivar assistant_id: The Assistant ID.
    :ivar id: The Feedback ID.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Feedback.
    :ivar user_sid: The SID of the User created the Feedback.
    :ivar message_id: The Message ID.
    :ivar score: The Score to provide as Feedback (0-1)
    :ivar session_id: The Session ID.
    :ivar text: The text to be given as feedback.
    :ivar date_created: The date and time in GMT when the Feedback was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the Feedback was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], id: str):
        super().__init__(version)

        self.assistant_id: Optional[str] = payload.get("assistant_id")
        self.id: Optional[str] = payload.get("id")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.user_sid: Optional[str] = payload.get("user_sid")
        self.message_id: Optional[str] = payload.get("message_id")
        self.score: Optional[float] = payload.get("score")
        self.session_id: Optional[str] = payload.get("session_id")
        self.text: Optional[str] = payload.get("text")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "id": id,
        }

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Assistants.V1.FeedbackInstance {}>".format(context)


class FeedbackPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> FeedbackInstance:
        """
        Build an instance of FeedbackInstance

        :param payload: Payload response from the API
        """
        return FeedbackInstance(self._version, payload, id=self._solution["id"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.FeedbackPage>"


class FeedbackList(ListResource):

    class AssistantsV1ServiceCreateFeedbackRequest(object):
        """
        :ivar message_id: The message ID.
        :ivar score: The score to be given(0-1).
        :ivar session_id: The Session ID.
        :ivar text: The text to be given as feedback.
        """

        def __init__(self, payload: Dict[str, Any]):

            self.message_id: Optional[str] = payload.get("message_id")
            self.score: Optional[float] = payload.get("score")
            self.session_id: Optional[str] = payload.get("session_id")
            self.text: Optional[str] = payload.get("text")

        def to_dict(self):
            return {
                "message_id": self.message_id,
                "score": self.score,
                "session_id": self.session_id,
                "text": self.text,
            }

    def __init__(self, version: Version, id: str):
        """
        Initialize the FeedbackList

        :param version: Version that contains the resource
        :param id: The assistant ID.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "id": id,
        }
        self._uri = "/Assistants/{id}/Feedbacks".format(**self._solution)

    def create(
        self,
        assistants_v1_service_create_feedback_request: AssistantsV1ServiceCreateFeedbackRequest,
    ) -> FeedbackInstance:
        """
        Create the FeedbackInstance

        :param assistants_v1_service_create_feedback_request:

        :returns: The created FeedbackInstance
        """
        data = assistants_v1_service_create_feedback_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FeedbackInstance(self._version, payload, id=self._solution["id"])

    async def create_async(
        self,
        assistants_v1_service_create_feedback_request: AssistantsV1ServiceCreateFeedbackRequest,
    ) -> FeedbackInstance:
        """
        Asynchronously create the FeedbackInstance

        :param assistants_v1_service_create_feedback_request:

        :returns: The created FeedbackInstance
        """
        data = assistants_v1_service_create_feedback_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FeedbackInstance(self._version, payload, id=self._solution["id"])

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[FeedbackInstance]:
        """
        Streams FeedbackInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[FeedbackInstance]:
        """
        Asynchronously streams FeedbackInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FeedbackInstance]:
        """
        Lists FeedbackInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FeedbackInstance]:
        """
        Asynchronously lists FeedbackInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FeedbackPage:
        """
        Retrieve a single page of FeedbackInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FeedbackInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FeedbackPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FeedbackPage:
        """
        Asynchronously retrieve a single page of FeedbackInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FeedbackInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FeedbackPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> FeedbackPage:
        """
        Retrieve a specific page of FeedbackInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FeedbackInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return FeedbackPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> FeedbackPage:
        """
        Asynchronously retrieve a specific page of FeedbackInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FeedbackInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return FeedbackPage(self._version, response, self._solution)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Assistants.V1.FeedbackList>"
