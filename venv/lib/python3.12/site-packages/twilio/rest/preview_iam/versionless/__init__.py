r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Organization Public API
    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.preview_iam.versionless.organization import OrganizationList


class Versionless(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the Versionless version of PreviewIam

        :param domain: The Twilio.preview_iam domain
        """
        super().__init__(domain, "Organizations")
        self._organization: Optional[OrganizationList] = None

    @property
    def organization(self) -> OrganizationList:
        if self._organization is None:
            self._organization = OrganizationList(self)
        return self._organization

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.PreviewIam.Versionless>"
