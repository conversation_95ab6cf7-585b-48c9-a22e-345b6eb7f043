r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.flex_api.v2.flex_user import FlexUserList
from twilio.rest.flex_api.v2.web_channels import WebChannelsList


class V2(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V2 version of FlexApi

        :param domain: The Twilio.flex_api domain
        """
        super().__init__(domain, "v2")
        self._flex_user: Optional[FlexUserList] = None
        self._web_channels: Optional[WebChannelsList] = None

    @property
    def flex_user(self) -> FlexUserList:
        if self._flex_user is None:
            self._flex_user = FlexUserList(self)
        return self._flex_user

    @property
    def web_channels(self) -> WebChannelsList:
        if self._web_channels is None:
            self._web_channels = WebChannelsList(self)
        return self._web_channels

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V2>"
