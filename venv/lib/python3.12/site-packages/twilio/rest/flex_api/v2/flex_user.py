r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class FlexUserInstance(InstanceResource):
    """
    :ivar account_sid: The unique SID of the account that created the resource.
    :ivar instance_sid: The unique ID created by <PERSON><PERSON><PERSON> to identify a Flex instance.
    :ivar user_sid: The unique SID identifier of the Twilio Unified User.
    :ivar flex_user_sid: The unique SID identifier of the Flex User.
    :ivar worker_sid: The unique SID identifier of the worker.
    :ivar workspace_sid: The unique SID identifier of the workspace.
    :ivar flex_team_sid: The unique SID identifier of the Flex Team.
    :ivar username: Username of the User.
    :ivar email: Email of the User.
    :ivar locale: The locale preference of the user.
    :ivar roles: The roles of the user.
    :ivar created_date: The date that this user was created, given in ISO 8601 format.
    :ivar updated_date: The date that this user was updated, given in ISO 8601 format.
    :ivar version: The current version of the user.
    :ivar url:
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        instance_sid: Optional[str] = None,
        flex_user_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.instance_sid: Optional[str] = payload.get("instance_sid")
        self.user_sid: Optional[str] = payload.get("user_sid")
        self.flex_user_sid: Optional[str] = payload.get("flex_user_sid")
        self.worker_sid: Optional[str] = payload.get("worker_sid")
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.flex_team_sid: Optional[str] = payload.get("flex_team_sid")
        self.username: Optional[str] = payload.get("username")
        self.email: Optional[str] = payload.get("email")
        self.locale: Optional[str] = payload.get("locale")
        self.roles: Optional[List[str]] = payload.get("roles")
        self.created_date: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("created_date")
        )
        self.updated_date: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("updated_date")
        )
        self.version: Optional[int] = deserialize.integer(payload.get("version"))
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "instance_sid": instance_sid or self.instance_sid,
            "flex_user_sid": flex_user_sid or self.flex_user_sid,
        }
        self._context: Optional[FlexUserContext] = None

    @property
    def _proxy(self) -> "FlexUserContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: FlexUserContext for this FlexUserInstance
        """
        if self._context is None:
            self._context = FlexUserContext(
                self._version,
                instance_sid=self._solution["instance_sid"],
                flex_user_sid=self._solution["flex_user_sid"],
            )
        return self._context

    def fetch(self) -> "FlexUserInstance":
        """
        Fetch the FlexUserInstance


        :returns: The fetched FlexUserInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "FlexUserInstance":
        """
        Asynchronous coroutine to fetch the FlexUserInstance


        :returns: The fetched FlexUserInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        email: Union[str, object] = values.unset,
        user_sid: Union[str, object] = values.unset,
        locale: Union[str, object] = values.unset,
    ) -> "FlexUserInstance":
        """
        Update the FlexUserInstance

        :param email: Email of the User.
        :param user_sid: The unique SID identifier of the Twilio Unified User.
        :param locale: The locale preference of the user.

        :returns: The updated FlexUserInstance
        """
        return self._proxy.update(
            email=email,
            user_sid=user_sid,
            locale=locale,
        )

    async def update_async(
        self,
        email: Union[str, object] = values.unset,
        user_sid: Union[str, object] = values.unset,
        locale: Union[str, object] = values.unset,
    ) -> "FlexUserInstance":
        """
        Asynchronous coroutine to update the FlexUserInstance

        :param email: Email of the User.
        :param user_sid: The unique SID identifier of the Twilio Unified User.
        :param locale: The locale preference of the user.

        :returns: The updated FlexUserInstance
        """
        return await self._proxy.update_async(
            email=email,
            user_sid=user_sid,
            locale=locale,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V2.FlexUserInstance {}>".format(context)


class FlexUserContext(InstanceContext):

    def __init__(self, version: Version, instance_sid: str, flex_user_sid: str):
        """
        Initialize the FlexUserContext

        :param version: Version that contains the resource
        :param instance_sid: The unique ID created by Twilio to identify a Flex instance.
        :param flex_user_sid: The unique id for the flex user.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "instance_sid": instance_sid,
            "flex_user_sid": flex_user_sid,
        }
        self._uri = "/Instances/{instance_sid}/Users/<USER>".format(
            **self._solution
        )

    def fetch(self) -> FlexUserInstance:
        """
        Fetch the FlexUserInstance


        :returns: The fetched FlexUserInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return FlexUserInstance(
            self._version,
            payload,
            instance_sid=self._solution["instance_sid"],
            flex_user_sid=self._solution["flex_user_sid"],
        )

    async def fetch_async(self) -> FlexUserInstance:
        """
        Asynchronous coroutine to fetch the FlexUserInstance


        :returns: The fetched FlexUserInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return FlexUserInstance(
            self._version,
            payload,
            instance_sid=self._solution["instance_sid"],
            flex_user_sid=self._solution["flex_user_sid"],
        )

    def update(
        self,
        email: Union[str, object] = values.unset,
        user_sid: Union[str, object] = values.unset,
        locale: Union[str, object] = values.unset,
    ) -> FlexUserInstance:
        """
        Update the FlexUserInstance

        :param email: Email of the User.
        :param user_sid: The unique SID identifier of the Twilio Unified User.
        :param locale: The locale preference of the user.

        :returns: The updated FlexUserInstance
        """

        data = values.of(
            {
                "Email": email,
                "UserSid": user_sid,
                "Locale": locale,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexUserInstance(
            self._version,
            payload,
            instance_sid=self._solution["instance_sid"],
            flex_user_sid=self._solution["flex_user_sid"],
        )

    async def update_async(
        self,
        email: Union[str, object] = values.unset,
        user_sid: Union[str, object] = values.unset,
        locale: Union[str, object] = values.unset,
    ) -> FlexUserInstance:
        """
        Asynchronous coroutine to update the FlexUserInstance

        :param email: Email of the User.
        :param user_sid: The unique SID identifier of the Twilio Unified User.
        :param locale: The locale preference of the user.

        :returns: The updated FlexUserInstance
        """

        data = values.of(
            {
                "Email": email,
                "UserSid": user_sid,
                "Locale": locale,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexUserInstance(
            self._version,
            payload,
            instance_sid=self._solution["instance_sid"],
            flex_user_sid=self._solution["flex_user_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V2.FlexUserContext {}>".format(context)


class FlexUserList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the FlexUserList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, instance_sid: str, flex_user_sid: str) -> FlexUserContext:
        """
        Constructs a FlexUserContext

        :param instance_sid: The unique ID created by Twilio to identify a Flex instance.
        :param flex_user_sid: The unique id for the flex user.
        """
        return FlexUserContext(
            self._version, instance_sid=instance_sid, flex_user_sid=flex_user_sid
        )

    def __call__(self, instance_sid: str, flex_user_sid: str) -> FlexUserContext:
        """
        Constructs a FlexUserContext

        :param instance_sid: The unique ID created by Twilio to identify a Flex instance.
        :param flex_user_sid: The unique id for the flex user.
        """
        return FlexUserContext(
            self._version, instance_sid=instance_sid, flex_user_sid=flex_user_sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V2.FlexUserList>"
