r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class FlexFlowInstance(InstanceResource):

    class ChannelType(object):
        WEB = "web"
        SMS = "sms"
        FACEBOOK = "facebook"
        WHATSAPP = "whatsapp"
        LINE = "line"
        CUSTOM = "custom"

    class IntegrationType(object):
        STUDIO = "studio"
        EXTERNAL = "external"
        TASK = "task"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Flex Flow resource and owns this Workflow.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar sid: The unique string that we created to identify the Flex Flow resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar chat_service_sid: The SID of the chat service.
    :ivar channel_type: 
    :ivar contact_identity: The channel contact's Identity.
    :ivar enabled: Whether the Flex Flow is enabled.
    :ivar integration_type: 
    :ivar integration: An object that contains specific parameters for the integration.
    :ivar long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
    :ivar janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
    :ivar url: The absolute URL of the Flex Flow resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.sid: Optional[str] = payload.get("sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.chat_service_sid: Optional[str] = payload.get("chat_service_sid")
        self.channel_type: Optional["FlexFlowInstance.ChannelType"] = payload.get(
            "channel_type"
        )
        self.contact_identity: Optional[str] = payload.get("contact_identity")
        self.enabled: Optional[bool] = payload.get("enabled")
        self.integration_type: Optional["FlexFlowInstance.IntegrationType"] = (
            payload.get("integration_type")
        )
        self.integration: Optional[Dict[str, object]] = payload.get("integration")
        self.long_lived: Optional[bool] = payload.get("long_lived")
        self.janitor_enabled: Optional[bool] = payload.get("janitor_enabled")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[FlexFlowContext] = None

    @property
    def _proxy(self) -> "FlexFlowContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: FlexFlowContext for this FlexFlowInstance
        """
        if self._context is None:
            self._context = FlexFlowContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the FlexFlowInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the FlexFlowInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "FlexFlowInstance":
        """
        Fetch the FlexFlowInstance


        :returns: The fetched FlexFlowInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "FlexFlowInstance":
        """
        Asynchronous coroutine to fetch the FlexFlowInstance


        :returns: The fetched FlexFlowInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        chat_service_sid: Union[str, object] = values.unset,
        channel_type: Union["FlexFlowInstance.ChannelType", object] = values.unset,
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> "FlexFlowInstance":
        """
        Update the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The updated FlexFlowInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            chat_service_sid=chat_service_sid,
            channel_type=channel_type,
            contact_identity=contact_identity,
            enabled=enabled,
            integration_type=integration_type,
            integration_flow_sid=integration_flow_sid,
            integration_url=integration_url,
            integration_workspace_sid=integration_workspace_sid,
            integration_workflow_sid=integration_workflow_sid,
            integration_channel=integration_channel,
            integration_timeout=integration_timeout,
            integration_priority=integration_priority,
            integration_creation_on_message=integration_creation_on_message,
            long_lived=long_lived,
            janitor_enabled=janitor_enabled,
            integration_retry_count=integration_retry_count,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        chat_service_sid: Union[str, object] = values.unset,
        channel_type: Union["FlexFlowInstance.ChannelType", object] = values.unset,
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> "FlexFlowInstance":
        """
        Asynchronous coroutine to update the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The updated FlexFlowInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            chat_service_sid=chat_service_sid,
            channel_type=channel_type,
            contact_identity=contact_identity,
            enabled=enabled,
            integration_type=integration_type,
            integration_flow_sid=integration_flow_sid,
            integration_url=integration_url,
            integration_workspace_sid=integration_workspace_sid,
            integration_workflow_sid=integration_workflow_sid,
            integration_channel=integration_channel,
            integration_timeout=integration_timeout,
            integration_priority=integration_priority,
            integration_creation_on_message=integration_creation_on_message,
            long_lived=long_lived,
            janitor_enabled=janitor_enabled,
            integration_retry_count=integration_retry_count,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.FlexFlowInstance {}>".format(context)


class FlexFlowContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the FlexFlowContext

        :param version: Version that contains the resource
        :param sid: The SID of the Flex Flow resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/FlexFlows/{sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the FlexFlowInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the FlexFlowInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> FlexFlowInstance:
        """
        Fetch the FlexFlowInstance


        :returns: The fetched FlexFlowInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return FlexFlowInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> FlexFlowInstance:
        """
        Asynchronous coroutine to fetch the FlexFlowInstance


        :returns: The fetched FlexFlowInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return FlexFlowInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        chat_service_sid: Union[str, object] = values.unset,
        channel_type: Union["FlexFlowInstance.ChannelType", object] = values.unset,
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> FlexFlowInstance:
        """
        Update the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The updated FlexFlowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ChatServiceSid": chat_service_sid,
                "ChannelType": channel_type,
                "ContactIdentity": contact_identity,
                "Enabled": serialize.boolean_to_string(enabled),
                "IntegrationType": integration_type,
                "Integration.FlowSid": integration_flow_sid,
                "Integration.Url": integration_url,
                "Integration.WorkspaceSid": integration_workspace_sid,
                "Integration.WorkflowSid": integration_workflow_sid,
                "Integration.Channel": integration_channel,
                "Integration.Timeout": integration_timeout,
                "Integration.Priority": integration_priority,
                "Integration.CreationOnMessage": serialize.boolean_to_string(
                    integration_creation_on_message
                ),
                "LongLived": serialize.boolean_to_string(long_lived),
                "JanitorEnabled": serialize.boolean_to_string(janitor_enabled),
                "Integration.RetryCount": integration_retry_count,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexFlowInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        chat_service_sid: Union[str, object] = values.unset,
        channel_type: Union["FlexFlowInstance.ChannelType", object] = values.unset,
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> FlexFlowInstance:
        """
        Asynchronous coroutine to update the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The updated FlexFlowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ChatServiceSid": chat_service_sid,
                "ChannelType": channel_type,
                "ContactIdentity": contact_identity,
                "Enabled": serialize.boolean_to_string(enabled),
                "IntegrationType": integration_type,
                "Integration.FlowSid": integration_flow_sid,
                "Integration.Url": integration_url,
                "Integration.WorkspaceSid": integration_workspace_sid,
                "Integration.WorkflowSid": integration_workflow_sid,
                "Integration.Channel": integration_channel,
                "Integration.Timeout": integration_timeout,
                "Integration.Priority": integration_priority,
                "Integration.CreationOnMessage": serialize.boolean_to_string(
                    integration_creation_on_message
                ),
                "LongLived": serialize.boolean_to_string(long_lived),
                "JanitorEnabled": serialize.boolean_to_string(janitor_enabled),
                "Integration.RetryCount": integration_retry_count,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexFlowInstance(self._version, payload, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.FlexFlowContext {}>".format(context)


class FlexFlowPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> FlexFlowInstance:
        """
        Build an instance of FlexFlowInstance

        :param payload: Payload response from the API
        """
        return FlexFlowInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.FlexFlowPage>"


class FlexFlowList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the FlexFlowList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/FlexFlows"

    def create(
        self,
        friendly_name: str,
        chat_service_sid: str,
        channel_type: "FlexFlowInstance.ChannelType",
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> FlexFlowInstance:
        """
        Create the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The created FlexFlowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ChatServiceSid": chat_service_sid,
                "ChannelType": channel_type,
                "ContactIdentity": contact_identity,
                "Enabled": serialize.boolean_to_string(enabled),
                "IntegrationType": integration_type,
                "Integration.FlowSid": integration_flow_sid,
                "Integration.Url": integration_url,
                "Integration.WorkspaceSid": integration_workspace_sid,
                "Integration.WorkflowSid": integration_workflow_sid,
                "Integration.Channel": integration_channel,
                "Integration.Timeout": integration_timeout,
                "Integration.Priority": integration_priority,
                "Integration.CreationOnMessage": serialize.boolean_to_string(
                    integration_creation_on_message
                ),
                "LongLived": serialize.boolean_to_string(long_lived),
                "JanitorEnabled": serialize.boolean_to_string(janitor_enabled),
                "Integration.RetryCount": integration_retry_count,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexFlowInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        chat_service_sid: str,
        channel_type: "FlexFlowInstance.ChannelType",
        contact_identity: Union[str, object] = values.unset,
        enabled: Union[bool, object] = values.unset,
        integration_type: Union[
            "FlexFlowInstance.IntegrationType", object
        ] = values.unset,
        integration_flow_sid: Union[str, object] = values.unset,
        integration_url: Union[str, object] = values.unset,
        integration_workspace_sid: Union[str, object] = values.unset,
        integration_workflow_sid: Union[str, object] = values.unset,
        integration_channel: Union[str, object] = values.unset,
        integration_timeout: Union[int, object] = values.unset,
        integration_priority: Union[int, object] = values.unset,
        integration_creation_on_message: Union[bool, object] = values.unset,
        long_lived: Union[bool, object] = values.unset,
        janitor_enabled: Union[bool, object] = values.unset,
        integration_retry_count: Union[int, object] = values.unset,
    ) -> FlexFlowInstance:
        """
        Asynchronously create the FlexFlowInstance

        :param friendly_name: A descriptive string that you create to describe the Flex Flow resource.
        :param chat_service_sid: The SID of the chat service.
        :param channel_type:
        :param contact_identity: The channel contact's Identity.
        :param enabled: Whether the new Flex Flow is enabled.
        :param integration_type:
        :param integration_flow_sid: The SID of the Studio Flow. Required when `integrationType` is `studio`.
        :param integration_url: The URL of the external webhook. Required when `integrationType` is `external`.
        :param integration_workspace_sid: The Workspace SID for a new Task. Required when `integrationType` is `task`.
        :param integration_workflow_sid: The Workflow SID for a new Task. Required when `integrationType` is `task`.
        :param integration_channel: The Task Channel SID (TCXXXX) or unique name (e.g., `sms`) to use for the Task that will be created. Applicable and required when `integrationType` is `task`. The default value is `default`.
        :param integration_timeout: The Task timeout in seconds for a new Task. Default is 86,400 seconds (24 hours). Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_priority: The Task priority of a new Task. The default priority is 0. Optional when `integrationType` is `task`, not applicable otherwise.
        :param integration_creation_on_message: In the context of outbound messaging, defines whether to create a Task immediately (and therefore reserve the conversation to current agent), or delay Task creation until the customer sends the first response. Set to false to create immediately, true to delay Task creation. This setting is only applicable for outbound messaging.
        :param long_lived: When enabled, Flex will keep the chat channel active so that it may be used for subsequent interactions with a contact identity. Defaults to `false`.
        :param janitor_enabled: When enabled, the Messaging Channel Janitor will remove active Proxy sessions if the associated Task is deleted outside of the Flex UI. Defaults to `false`.
        :param integration_retry_count: The number of times to retry the Studio Flow or webhook in case of failure. Takes integer values from 0 to 3 with the default being 3. Optional when `integrationType` is `studio` or `external`, not applicable otherwise.

        :returns: The created FlexFlowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "ChatServiceSid": chat_service_sid,
                "ChannelType": channel_type,
                "ContactIdentity": contact_identity,
                "Enabled": serialize.boolean_to_string(enabled),
                "IntegrationType": integration_type,
                "Integration.FlowSid": integration_flow_sid,
                "Integration.Url": integration_url,
                "Integration.WorkspaceSid": integration_workspace_sid,
                "Integration.WorkflowSid": integration_workflow_sid,
                "Integration.Channel": integration_channel,
                "Integration.Timeout": integration_timeout,
                "Integration.Priority": integration_priority,
                "Integration.CreationOnMessage": serialize.boolean_to_string(
                    integration_creation_on_message
                ),
                "LongLived": serialize.boolean_to_string(long_lived),
                "JanitorEnabled": serialize.boolean_to_string(janitor_enabled),
                "Integration.RetryCount": integration_retry_count,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlexFlowInstance(self._version, payload)

    def stream(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[FlexFlowInstance]:
        """
        Streams FlexFlowInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(friendly_name=friendly_name, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[FlexFlowInstance]:
        """
        Asynchronously streams FlexFlowInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            friendly_name=friendly_name, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FlexFlowInstance]:
        """
        Lists FlexFlowInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FlexFlowInstance]:
        """
        Asynchronously lists FlexFlowInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FlexFlowPage:
        """
        Retrieve a single page of FlexFlowInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FlexFlowInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FlexFlowPage(self._version, response)

    async def page_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FlexFlowPage:
        """
        Asynchronously retrieve a single page of FlexFlowInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Flex Flow resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FlexFlowInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FlexFlowPage(self._version, response)

    def get_page(self, target_url: str) -> FlexFlowPage:
        """
        Retrieve a specific page of FlexFlowInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FlexFlowInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return FlexFlowPage(self._version, response)

    async def get_page_async(self, target_url: str) -> FlexFlowPage:
        """
        Asynchronously retrieve a specific page of FlexFlowInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FlexFlowInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return FlexFlowPage(self._version, response)

    def get(self, sid: str) -> FlexFlowContext:
        """
        Constructs a FlexFlowContext

        :param sid: The SID of the Flex Flow resource to update.
        """
        return FlexFlowContext(self._version, sid=sid)

    def __call__(self, sid: str) -> FlexFlowContext:
        """
        Constructs a FlexFlowContext

        :param sid: The SID of the Flex Flow resource to update.
        """
        return FlexFlowContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.FlexFlowList>"
