r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class InteractionTransferInstance(InstanceResource):

    class TransferStatus(object):
        ACTIVE = "active"
        FAILED = "failed"
        COMPLETED = "completed"

    class TransferType(object):
        WARM = "warm"
        COLD = "cold"
        EXTERNAL = "external"

    """
    :ivar sid: The unique string created by Twilio to identify an Interaction Transfer resource.
    :ivar instance_sid: The SID of the Instance associated with the Transfer.
    :ivar account_sid: The SID of the Account that created the Transfer.
    :ivar interaction_sid: The Interaction Sid for this channel.
    :ivar channel_sid: The Channel Sid for this Transfer.
    :ivar execution_sid: The Execution SID associated with the Transfer.
    :ivar type: 
    :ivar status: 
    :ivar _from: The SID of the Participant initiating the Transfer.
    :ivar to: The SID of the Participant receiving the Transfer.
    :ivar note_sid: The SID of the Note associated with the Transfer.
    :ivar summary_sid: The SID of the Summary associated with the Transfer.
    :ivar date_created: The date and time when the Transfer was created.
    :ivar date_updated: The date and time when the Transfer was last updated.
    :ivar url: 
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        interaction_sid: str,
        channel_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.instance_sid: Optional[str] = payload.get("instance_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.interaction_sid: Optional[str] = payload.get("interaction_sid")
        self.channel_sid: Optional[str] = payload.get("channel_sid")
        self.execution_sid: Optional[str] = payload.get("execution_sid")
        self.type: Optional["InteractionTransferInstance.TransferType"] = payload.get(
            "type"
        )
        self.status: Optional["InteractionTransferInstance.TransferStatus"] = (
            payload.get("status")
        )
        self._from: Optional[str] = payload.get("from")
        self.to: Optional[str] = payload.get("to")
        self.note_sid: Optional[str] = payload.get("note_sid")
        self.summary_sid: Optional[str] = payload.get("summary_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "interaction_sid": interaction_sid,
            "channel_sid": channel_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[InteractionTransferContext] = None

    @property
    def _proxy(self) -> "InteractionTransferContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: InteractionTransferContext for this InteractionTransferInstance
        """
        if self._context is None:
            self._context = InteractionTransferContext(
                self._version,
                interaction_sid=self._solution["interaction_sid"],
                channel_sid=self._solution["channel_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "InteractionTransferInstance":
        """
        Fetch the InteractionTransferInstance


        :returns: The fetched InteractionTransferInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "InteractionTransferInstance":
        """
        Asynchronous coroutine to fetch the InteractionTransferInstance


        :returns: The fetched InteractionTransferInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self, body: Union[object, object] = values.unset
    ) -> "InteractionTransferInstance":
        """
        Update the InteractionTransferInstance

        :param body:

        :returns: The updated InteractionTransferInstance
        """
        return self._proxy.update(
            body=body,
        )

    async def update_async(
        self, body: Union[object, object] = values.unset
    ) -> "InteractionTransferInstance":
        """
        Asynchronous coroutine to update the InteractionTransferInstance

        :param body:

        :returns: The updated InteractionTransferInstance
        """
        return await self._proxy.update_async(
            body=body,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.InteractionTransferInstance {}>".format(context)


class InteractionTransferContext(InstanceContext):

    def __init__(
        self, version: Version, interaction_sid: str, channel_sid: str, sid: str
    ):
        """
        Initialize the InteractionTransferContext

        :param version: Version that contains the resource
        :param interaction_sid: The Interaction Sid for this channel.
        :param channel_sid: The Channel Sid for this Transfer.
        :param sid: The unique string created by Twilio to identify a Transfer resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "interaction_sid": interaction_sid,
            "channel_sid": channel_sid,
            "sid": sid,
        }
        self._uri = "/Interactions/{interaction_sid}/Channels/{channel_sid}/Transfers/{sid}".format(
            **self._solution
        )

    def fetch(self) -> InteractionTransferInstance:
        """
        Fetch the InteractionTransferInstance


        :returns: The fetched InteractionTransferInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> InteractionTransferInstance:
        """
        Asynchronous coroutine to fetch the InteractionTransferInstance


        :returns: The fetched InteractionTransferInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self, body: Union[object, object] = values.unset
    ) -> InteractionTransferInstance:
        """
        Update the InteractionTransferInstance

        :param body:

        :returns: The updated InteractionTransferInstance
        """
        data = body.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self, body: Union[object, object] = values.unset
    ) -> InteractionTransferInstance:
        """
        Asynchronous coroutine to update the InteractionTransferInstance

        :param body:

        :returns: The updated InteractionTransferInstance
        """
        data = body.to_dict()

        headers = values.of({})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.InteractionTransferContext {}>".format(context)


class InteractionTransferList(ListResource):

    def __init__(self, version: Version, interaction_sid: str, channel_sid: str):
        """
        Initialize the InteractionTransferList

        :param version: Version that contains the resource
        :param interaction_sid: The Interaction Sid for the Interaction
        :param channel_sid: The Channel Sid for the Channel.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "interaction_sid": interaction_sid,
            "channel_sid": channel_sid,
        }
        self._uri = (
            "/Interactions/{interaction_sid}/Channels/{channel_sid}/Transfers".format(
                **self._solution
            )
        )

    def create(
        self, body: Union[object, object] = values.unset
    ) -> InteractionTransferInstance:
        """
        Create the InteractionTransferInstance

        :param body:

        :returns: The created InteractionTransferInstance
        """
        data = body.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
        )

    async def create_async(
        self, body: Union[object, object] = values.unset
    ) -> InteractionTransferInstance:
        """
        Asynchronously create the InteractionTransferInstance

        :param body:

        :returns: The created InteractionTransferInstance
        """
        data = body.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return InteractionTransferInstance(
            self._version,
            payload,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
        )

    def get(self, sid: str) -> InteractionTransferContext:
        """
        Constructs a InteractionTransferContext

        :param sid: The unique string created by Twilio to identify a Transfer resource.
        """
        return InteractionTransferContext(
            self._version,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> InteractionTransferContext:
        """
        Constructs a InteractionTransferContext

        :param sid: The unique string created by Twilio to identify a Transfer resource.
        """
        return InteractionTransferContext(
            self._version,
            interaction_sid=self._solution["interaction_sid"],
            channel_sid=self._solution["channel_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.InteractionTransferList>"
