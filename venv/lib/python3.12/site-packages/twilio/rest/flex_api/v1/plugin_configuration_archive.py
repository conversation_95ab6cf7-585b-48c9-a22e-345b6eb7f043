r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PluginConfigurationArchiveInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Flex Plugin Configuration resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Flex Plugin Configuration resource and owns this resource.
    :ivar name: The name of this Flex Plugin Configuration.
    :ivar description: The description of the Flex Plugin Configuration resource.
    :ivar archived: Whether the Flex Plugin Configuration is archived. The default value is false.
    :ivar date_created: The date and time in GMT when the Flex Plugin Configuration was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Flex Plugin Configuration resource.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.name: Optional[str] = payload.get("name")
        self.description: Optional[str] = payload.get("description")
        self.archived: Optional[bool] = payload.get("archived")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[PluginConfigurationArchiveContext] = None

    @property
    def _proxy(self) -> "PluginConfigurationArchiveContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: PluginConfigurationArchiveContext for this PluginConfigurationArchiveInstance
        """
        if self._context is None:
            self._context = PluginConfigurationArchiveContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def update(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginConfigurationArchiveInstance":
        """
        Update the PluginConfigurationArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginConfigurationArchiveInstance
        """
        return self._proxy.update(
            flex_metadata=flex_metadata,
        )

    async def update_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "PluginConfigurationArchiveInstance":
        """
        Asynchronous coroutine to update the PluginConfigurationArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginConfigurationArchiveInstance
        """
        return await self._proxy.update_async(
            flex_metadata=flex_metadata,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginConfigurationArchiveInstance {}>".format(
            context
        )


class PluginConfigurationArchiveContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the PluginConfigurationArchiveContext

        :param version: Version that contains the resource
        :param sid: The SID of the Flex Plugin Configuration resource to archive.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/PluginService/Configurations/{sid}/Archive".format(
            **self._solution
        )

    def update(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginConfigurationArchiveInstance:
        """
        Update the PluginConfigurationArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginConfigurationArchiveInstance
        """

        data = values.of({})
        headers = values.of({})

        if not (
            flex_metadata is values.unset
            or (isinstance(flex_metadata, str) and not flex_metadata)
        ):
            headers["Flex-Metadata"] = flex_metadata

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginConfigurationArchiveInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    async def update_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> PluginConfigurationArchiveInstance:
        """
        Asynchronous coroutine to update the PluginConfigurationArchiveInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The updated PluginConfigurationArchiveInstance
        """

        data = values.of({})
        headers = values.of({})

        if not (
            flex_metadata is values.unset
            or (isinstance(flex_metadata, str) and not flex_metadata)
        ):
            headers["Flex-Metadata"] = flex_metadata

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return PluginConfigurationArchiveInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.PluginConfigurationArchiveContext {}>".format(
            context
        )


class PluginConfigurationArchiveList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PluginConfigurationArchiveList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, sid: str) -> PluginConfigurationArchiveContext:
        """
        Constructs a PluginConfigurationArchiveContext

        :param sid: The SID of the Flex Plugin Configuration resource to archive.
        """
        return PluginConfigurationArchiveContext(self._version, sid=sid)

    def __call__(self, sid: str) -> PluginConfigurationArchiveContext:
        """
        Constructs a PluginConfigurationArchiveContext

        :param sid: The SID of the Flex Plugin Configuration resource to archive.
        """
        return PluginConfigurationArchiveContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.PluginConfigurationArchiveList>"
