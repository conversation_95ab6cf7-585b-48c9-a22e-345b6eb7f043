r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Chat
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.chat.v2.service.binding import BindingList
from twilio.rest.chat.v2.service.channel import ChannelList
from twilio.rest.chat.v2.service.role import RoleList
from twilio.rest.chat.v2.service.user import UserList


class ServiceInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Service resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Service resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar default_service_role_sid: The service role assigned to users when they are added to the service. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
    :ivar default_channel_role_sid: The channel role assigned to users when they are added to a channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
    :ivar default_channel_creator_role_sid: The channel role assigned to a channel creator when they join a new channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
    :ivar read_status_enabled: Whether the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature is enabled. The default is `true`.
    :ivar reachability_enabled: Whether the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) is enabled for this Service instance. The default is `false`.
    :ivar typing_indicator_timeout: How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
    :ivar consumption_report_interval: DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
    :ivar limits: An object that describes the limits of the service instance. The `limits` object contains  `channel_members` to describe the members/channel limit and `user_channels` to describe the channels/user limit. `channel_members` can be 1,000 or less, with a default of 250. `user_channels` can be 1,000 or less, with a default value of 100.
    :ivar pre_webhook_url: The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
    :ivar post_webhook_url: The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
    :ivar webhook_method: The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
    :ivar webhook_filters: The list of webhook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
    :ivar pre_webhook_retry_count: The number of times to retry a call to the `pre_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. Default retry count is 0 times, which means the call won't be retried.
    :ivar post_webhook_retry_count: The number of times to retry a call to the `post_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. The default is 0, which means the call won't be retried.
    :ivar notifications: The notification configuration for the Service instance. See [Push Notification Configuration](https://www.twilio.com/docs/chat/push-notification-configuration) for more info.
    :ivar media: An object that describes the properties of media that the service supports. The object contains the `size_limit_mb` property, which describes the size of the largest media file in MB; and the `compatibility_message` property, which contains the message text to send when a media message does not have any text.
    :ivar url: The absolute URL of the Service resource.
    :ivar links: The absolute URLs of the Service's [Channels](https://www.twilio.com/docs/chat/channels), [Roles](https://www.twilio.com/docs/chat/rest/role-resource), [Bindings](https://www.twilio.com/docs/chat/rest/binding-resource), and [Users](https://www.twilio.com/docs/chat/rest/user-resource).
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.default_service_role_sid: Optional[str] = payload.get(
            "default_service_role_sid"
        )
        self.default_channel_role_sid: Optional[str] = payload.get(
            "default_channel_role_sid"
        )
        self.default_channel_creator_role_sid: Optional[str] = payload.get(
            "default_channel_creator_role_sid"
        )
        self.read_status_enabled: Optional[bool] = payload.get("read_status_enabled")
        self.reachability_enabled: Optional[bool] = payload.get("reachability_enabled")
        self.typing_indicator_timeout: Optional[int] = deserialize.integer(
            payload.get("typing_indicator_timeout")
        )
        self.consumption_report_interval: Optional[int] = deserialize.integer(
            payload.get("consumption_report_interval")
        )
        self.limits: Optional[Dict[str, object]] = payload.get("limits")
        self.pre_webhook_url: Optional[str] = payload.get("pre_webhook_url")
        self.post_webhook_url: Optional[str] = payload.get("post_webhook_url")
        self.webhook_method: Optional[str] = payload.get("webhook_method")
        self.webhook_filters: Optional[List[str]] = payload.get("webhook_filters")
        self.pre_webhook_retry_count: Optional[int] = deserialize.integer(
            payload.get("pre_webhook_retry_count")
        )
        self.post_webhook_retry_count: Optional[int] = deserialize.integer(
            payload.get("post_webhook_retry_count")
        )
        self.notifications: Optional[Dict[str, object]] = payload.get("notifications")
        self.media: Optional[Dict[str, object]] = payload.get("media")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ServiceContext] = None

    @property
    def _proxy(self) -> "ServiceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ServiceContext for this ServiceInstance
        """
        if self._context is None:
            self._context = ServiceContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ServiceInstance":
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ServiceInstance":
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_new_message_sound: Union[str, object] = values.unset,
        notifications_new_message_badge_count_enabled: Union[
            bool, object
        ] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_added_to_channel_sound: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_sound: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_sound: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
        media_compatibility_message: Union[str, object] = values.unset,
        pre_webhook_retry_count: Union[int, object] = values.unset,
        post_webhook_retry_count: Union[int, object] = values.unset,
        notifications_log_enabled: Union[bool, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the resource.
        :param default_service_role_sid: The service role assigned to users when they are added to the service. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_role_sid: The channel role assigned to users when they are added to a channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_creator_role_sid: The channel role assigned to a channel creator when they join a new channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param read_status_enabled: Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
        :param reachability_enabled: Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
        :param typing_indicator_timeout: How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
        :param consumption_report_interval: DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
        :param notifications_new_message_enabled: Whether to send a notification when a new message is added to a channel. The default is `false`.
        :param notifications_new_message_template: The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_sound: The name of the sound to play when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param notifications_added_to_channel_enabled: Whether to send a notification when a member is added to a channel. The default is `false`.
        :param notifications_added_to_channel_template: The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_added_to_channel_sound: The name of the sound to play when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_removed_from_channel_enabled: Whether to send a notification to a user when they are removed from a channel. The default is `false`.
        :param notifications_removed_from_channel_template: The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_removed_from_channel_sound: The name of the sound to play to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_invited_to_channel_enabled: Whether to send a notification when a user is invited to a channel. The default is `false`.
        :param notifications_invited_to_channel_template: The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param notifications_invited_to_channel_sound: The name of the sound to play when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param pre_webhook_url: The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param post_webhook_url: The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_method: The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_filters: The list of webhook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param limits_channel_members: The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
        :param limits_user_channels: The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
        :param media_compatibility_message: The message to send when a media message has no text. Can be used as placeholder message.
        :param pre_webhook_retry_count: The number of times to retry a call to the `pre_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. Default retry count is 0 times, which means the call won't be retried.
        :param post_webhook_retry_count: The number of times to retry a call to the `post_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. The default is 0, which means the call won't be retried.
        :param notifications_log_enabled: Whether to log notifications. The default is `false`.

        :returns: The updated ServiceInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            default_service_role_sid=default_service_role_sid,
            default_channel_role_sid=default_channel_role_sid,
            default_channel_creator_role_sid=default_channel_creator_role_sid,
            read_status_enabled=read_status_enabled,
            reachability_enabled=reachability_enabled,
            typing_indicator_timeout=typing_indicator_timeout,
            consumption_report_interval=consumption_report_interval,
            notifications_new_message_enabled=notifications_new_message_enabled,
            notifications_new_message_template=notifications_new_message_template,
            notifications_new_message_sound=notifications_new_message_sound,
            notifications_new_message_badge_count_enabled=notifications_new_message_badge_count_enabled,
            notifications_added_to_channel_enabled=notifications_added_to_channel_enabled,
            notifications_added_to_channel_template=notifications_added_to_channel_template,
            notifications_added_to_channel_sound=notifications_added_to_channel_sound,
            notifications_removed_from_channel_enabled=notifications_removed_from_channel_enabled,
            notifications_removed_from_channel_template=notifications_removed_from_channel_template,
            notifications_removed_from_channel_sound=notifications_removed_from_channel_sound,
            notifications_invited_to_channel_enabled=notifications_invited_to_channel_enabled,
            notifications_invited_to_channel_template=notifications_invited_to_channel_template,
            notifications_invited_to_channel_sound=notifications_invited_to_channel_sound,
            pre_webhook_url=pre_webhook_url,
            post_webhook_url=post_webhook_url,
            webhook_method=webhook_method,
            webhook_filters=webhook_filters,
            limits_channel_members=limits_channel_members,
            limits_user_channels=limits_user_channels,
            media_compatibility_message=media_compatibility_message,
            pre_webhook_retry_count=pre_webhook_retry_count,
            post_webhook_retry_count=post_webhook_retry_count,
            notifications_log_enabled=notifications_log_enabled,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_new_message_sound: Union[str, object] = values.unset,
        notifications_new_message_badge_count_enabled: Union[
            bool, object
        ] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_added_to_channel_sound: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_sound: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_sound: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
        media_compatibility_message: Union[str, object] = values.unset,
        pre_webhook_retry_count: Union[int, object] = values.unset,
        post_webhook_retry_count: Union[int, object] = values.unset,
        notifications_log_enabled: Union[bool, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the resource.
        :param default_service_role_sid: The service role assigned to users when they are added to the service. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_role_sid: The channel role assigned to users when they are added to a channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_creator_role_sid: The channel role assigned to a channel creator when they join a new channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param read_status_enabled: Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
        :param reachability_enabled: Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
        :param typing_indicator_timeout: How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
        :param consumption_report_interval: DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
        :param notifications_new_message_enabled: Whether to send a notification when a new message is added to a channel. The default is `false`.
        :param notifications_new_message_template: The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_sound: The name of the sound to play when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param notifications_added_to_channel_enabled: Whether to send a notification when a member is added to a channel. The default is `false`.
        :param notifications_added_to_channel_template: The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_added_to_channel_sound: The name of the sound to play when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_removed_from_channel_enabled: Whether to send a notification to a user when they are removed from a channel. The default is `false`.
        :param notifications_removed_from_channel_template: The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_removed_from_channel_sound: The name of the sound to play to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_invited_to_channel_enabled: Whether to send a notification when a user is invited to a channel. The default is `false`.
        :param notifications_invited_to_channel_template: The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param notifications_invited_to_channel_sound: The name of the sound to play when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param pre_webhook_url: The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param post_webhook_url: The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_method: The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_filters: The list of webhook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param limits_channel_members: The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
        :param limits_user_channels: The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
        :param media_compatibility_message: The message to send when a media message has no text. Can be used as placeholder message.
        :param pre_webhook_retry_count: The number of times to retry a call to the `pre_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. Default retry count is 0 times, which means the call won't be retried.
        :param post_webhook_retry_count: The number of times to retry a call to the `post_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. The default is 0, which means the call won't be retried.
        :param notifications_log_enabled: Whether to log notifications. The default is `false`.

        :returns: The updated ServiceInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            default_service_role_sid=default_service_role_sid,
            default_channel_role_sid=default_channel_role_sid,
            default_channel_creator_role_sid=default_channel_creator_role_sid,
            read_status_enabled=read_status_enabled,
            reachability_enabled=reachability_enabled,
            typing_indicator_timeout=typing_indicator_timeout,
            consumption_report_interval=consumption_report_interval,
            notifications_new_message_enabled=notifications_new_message_enabled,
            notifications_new_message_template=notifications_new_message_template,
            notifications_new_message_sound=notifications_new_message_sound,
            notifications_new_message_badge_count_enabled=notifications_new_message_badge_count_enabled,
            notifications_added_to_channel_enabled=notifications_added_to_channel_enabled,
            notifications_added_to_channel_template=notifications_added_to_channel_template,
            notifications_added_to_channel_sound=notifications_added_to_channel_sound,
            notifications_removed_from_channel_enabled=notifications_removed_from_channel_enabled,
            notifications_removed_from_channel_template=notifications_removed_from_channel_template,
            notifications_removed_from_channel_sound=notifications_removed_from_channel_sound,
            notifications_invited_to_channel_enabled=notifications_invited_to_channel_enabled,
            notifications_invited_to_channel_template=notifications_invited_to_channel_template,
            notifications_invited_to_channel_sound=notifications_invited_to_channel_sound,
            pre_webhook_url=pre_webhook_url,
            post_webhook_url=post_webhook_url,
            webhook_method=webhook_method,
            webhook_filters=webhook_filters,
            limits_channel_members=limits_channel_members,
            limits_user_channels=limits_user_channels,
            media_compatibility_message=media_compatibility_message,
            pre_webhook_retry_count=pre_webhook_retry_count,
            post_webhook_retry_count=post_webhook_retry_count,
            notifications_log_enabled=notifications_log_enabled,
        )

    @property
    def bindings(self) -> BindingList:
        """
        Access the bindings
        """
        return self._proxy.bindings

    @property
    def channels(self) -> ChannelList:
        """
        Access the channels
        """
        return self._proxy.channels

    @property
    def roles(self) -> RoleList:
        """
        Access the roles
        """
        return self._proxy.roles

    @property
    def users(self) -> UserList:
        """
        Access the users
        """
        return self._proxy.users

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Chat.V2.ServiceInstance {}>".format(context)


class ServiceContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ServiceContext

        :param version: Version that contains the resource
        :param sid: The SID of the Service resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Services/{sid}".format(**self._solution)

        self._bindings: Optional[BindingList] = None
        self._channels: Optional[ChannelList] = None
        self._roles: Optional[RoleList] = None
        self._users: Optional[UserList] = None

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ServiceInstance:
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ServiceInstance:
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_new_message_sound: Union[str, object] = values.unset,
        notifications_new_message_badge_count_enabled: Union[
            bool, object
        ] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_added_to_channel_sound: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_sound: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_sound: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
        media_compatibility_message: Union[str, object] = values.unset,
        pre_webhook_retry_count: Union[int, object] = values.unset,
        post_webhook_retry_count: Union[int, object] = values.unset,
        notifications_log_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the resource.
        :param default_service_role_sid: The service role assigned to users when they are added to the service. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_role_sid: The channel role assigned to users when they are added to a channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_creator_role_sid: The channel role assigned to a channel creator when they join a new channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param read_status_enabled: Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
        :param reachability_enabled: Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
        :param typing_indicator_timeout: How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
        :param consumption_report_interval: DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
        :param notifications_new_message_enabled: Whether to send a notification when a new message is added to a channel. The default is `false`.
        :param notifications_new_message_template: The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_sound: The name of the sound to play when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param notifications_added_to_channel_enabled: Whether to send a notification when a member is added to a channel. The default is `false`.
        :param notifications_added_to_channel_template: The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_added_to_channel_sound: The name of the sound to play when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_removed_from_channel_enabled: Whether to send a notification to a user when they are removed from a channel. The default is `false`.
        :param notifications_removed_from_channel_template: The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_removed_from_channel_sound: The name of the sound to play to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_invited_to_channel_enabled: Whether to send a notification when a user is invited to a channel. The default is `false`.
        :param notifications_invited_to_channel_template: The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param notifications_invited_to_channel_sound: The name of the sound to play when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param pre_webhook_url: The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param post_webhook_url: The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_method: The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_filters: The list of webhook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param limits_channel_members: The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
        :param limits_user_channels: The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
        :param media_compatibility_message: The message to send when a media message has no text. Can be used as placeholder message.
        :param pre_webhook_retry_count: The number of times to retry a call to the `pre_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. Default retry count is 0 times, which means the call won't be retried.
        :param post_webhook_retry_count: The number of times to retry a call to the `post_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. The default is 0, which means the call won't be retried.
        :param notifications_log_enabled: Whether to log notifications. The default is `false`.

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "DefaultServiceRoleSid": default_service_role_sid,
                "DefaultChannelRoleSid": default_channel_role_sid,
                "DefaultChannelCreatorRoleSid": default_channel_creator_role_sid,
                "ReadStatusEnabled": serialize.boolean_to_string(read_status_enabled),
                "ReachabilityEnabled": serialize.boolean_to_string(
                    reachability_enabled
                ),
                "TypingIndicatorTimeout": typing_indicator_timeout,
                "ConsumptionReportInterval": consumption_report_interval,
                "Notifications.NewMessage.Enabled": serialize.boolean_to_string(
                    notifications_new_message_enabled
                ),
                "Notifications.NewMessage.Template": notifications_new_message_template,
                "Notifications.NewMessage.Sound": notifications_new_message_sound,
                "Notifications.NewMessage.BadgeCountEnabled": serialize.boolean_to_string(
                    notifications_new_message_badge_count_enabled
                ),
                "Notifications.AddedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_added_to_channel_enabled
                ),
                "Notifications.AddedToChannel.Template": notifications_added_to_channel_template,
                "Notifications.AddedToChannel.Sound": notifications_added_to_channel_sound,
                "Notifications.RemovedFromChannel.Enabled": serialize.boolean_to_string(
                    notifications_removed_from_channel_enabled
                ),
                "Notifications.RemovedFromChannel.Template": notifications_removed_from_channel_template,
                "Notifications.RemovedFromChannel.Sound": notifications_removed_from_channel_sound,
                "Notifications.InvitedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_invited_to_channel_enabled
                ),
                "Notifications.InvitedToChannel.Template": notifications_invited_to_channel_template,
                "Notifications.InvitedToChannel.Sound": notifications_invited_to_channel_sound,
                "PreWebhookUrl": pre_webhook_url,
                "PostWebhookUrl": post_webhook_url,
                "WebhookMethod": webhook_method,
                "WebhookFilters": serialize.map(webhook_filters, lambda e: e),
                "Limits.ChannelMembers": limits_channel_members,
                "Limits.UserChannels": limits_user_channels,
                "Media.CompatibilityMessage": media_compatibility_message,
                "PreWebhookRetryCount": pre_webhook_retry_count,
                "PostWebhookRetryCount": post_webhook_retry_count,
                "Notifications.LogEnabled": serialize.boolean_to_string(
                    notifications_log_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        default_service_role_sid: Union[str, object] = values.unset,
        default_channel_role_sid: Union[str, object] = values.unset,
        default_channel_creator_role_sid: Union[str, object] = values.unset,
        read_status_enabled: Union[bool, object] = values.unset,
        reachability_enabled: Union[bool, object] = values.unset,
        typing_indicator_timeout: Union[int, object] = values.unset,
        consumption_report_interval: Union[int, object] = values.unset,
        notifications_new_message_enabled: Union[bool, object] = values.unset,
        notifications_new_message_template: Union[str, object] = values.unset,
        notifications_new_message_sound: Union[str, object] = values.unset,
        notifications_new_message_badge_count_enabled: Union[
            bool, object
        ] = values.unset,
        notifications_added_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_added_to_channel_template: Union[str, object] = values.unset,
        notifications_added_to_channel_sound: Union[str, object] = values.unset,
        notifications_removed_from_channel_enabled: Union[bool, object] = values.unset,
        notifications_removed_from_channel_template: Union[str, object] = values.unset,
        notifications_removed_from_channel_sound: Union[str, object] = values.unset,
        notifications_invited_to_channel_enabled: Union[bool, object] = values.unset,
        notifications_invited_to_channel_template: Union[str, object] = values.unset,
        notifications_invited_to_channel_sound: Union[str, object] = values.unset,
        pre_webhook_url: Union[str, object] = values.unset,
        post_webhook_url: Union[str, object] = values.unset,
        webhook_method: Union[str, object] = values.unset,
        webhook_filters: Union[List[str], object] = values.unset,
        limits_channel_members: Union[int, object] = values.unset,
        limits_user_channels: Union[int, object] = values.unset,
        media_compatibility_message: Union[str, object] = values.unset,
        pre_webhook_retry_count: Union[int, object] = values.unset,
        post_webhook_retry_count: Union[int, object] = values.unset,
        notifications_log_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the resource.
        :param default_service_role_sid: The service role assigned to users when they are added to the service. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_role_sid: The channel role assigned to users when they are added to a channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param default_channel_creator_role_sid: The channel role assigned to a channel creator when they join a new channel. See the [Role resource](https://www.twilio.com/docs/chat/rest/role-resource) for more info about roles.
        :param read_status_enabled: Whether to enable the [Message Consumption Horizon](https://www.twilio.com/docs/chat/consumption-horizon) feature. The default is `true`.
        :param reachability_enabled: Whether to enable the [Reachability Indicator](https://www.twilio.com/docs/chat/reachability-indicator) for this Service instance. The default is `false`.
        :param typing_indicator_timeout: How long in seconds after a `started typing` event until clients should assume that user is no longer typing, even if no `ended typing` message was received.  The default is 5 seconds.
        :param consumption_report_interval: DEPRECATED. The interval in seconds between consumption reports submission batches from client endpoints.
        :param notifications_new_message_enabled: Whether to send a notification when a new message is added to a channel. The default is `false`.
        :param notifications_new_message_template: The template to use to create the notification text displayed when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_sound: The name of the sound to play when a new message is added to a channel and `notifications.new_message.enabled` is `true`.
        :param notifications_new_message_badge_count_enabled: Whether the new message badge is enabled. The default is `false`.
        :param notifications_added_to_channel_enabled: Whether to send a notification when a member is added to a channel. The default is `false`.
        :param notifications_added_to_channel_template: The template to use to create the notification text displayed when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_added_to_channel_sound: The name of the sound to play when a member is added to a channel and `notifications.added_to_channel.enabled` is `true`.
        :param notifications_removed_from_channel_enabled: Whether to send a notification to a user when they are removed from a channel. The default is `false`.
        :param notifications_removed_from_channel_template: The template to use to create the notification text displayed to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_removed_from_channel_sound: The name of the sound to play to a user when they are removed from a channel and `notifications.removed_from_channel.enabled` is `true`.
        :param notifications_invited_to_channel_enabled: Whether to send a notification when a user is invited to a channel. The default is `false`.
        :param notifications_invited_to_channel_template: The template to use to create the notification text displayed when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param notifications_invited_to_channel_sound: The name of the sound to play when a user is invited to a channel and `notifications.invited_to_channel.enabled` is `true`.
        :param pre_webhook_url: The URL for pre-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param post_webhook_url: The URL for post-event webhooks, which are called by using the `webhook_method`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_method: The HTTP method to use for calls to the `pre_webhook_url` and `post_webhook_url` webhooks.  Can be: `POST` or `GET` and the default is `POST`. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param webhook_filters: The list of webhook events that are enabled for this Service instance. See [Webhook Events](https://www.twilio.com/docs/chat/webhook-events) for more details.
        :param limits_channel_members: The maximum number of Members that can be added to Channels within this Service. Can be up to 1,000.
        :param limits_user_channels: The maximum number of Channels Users can be a Member of within this Service. Can be up to 1,000.
        :param media_compatibility_message: The message to send when a media message has no text. Can be used as placeholder message.
        :param pre_webhook_retry_count: The number of times to retry a call to the `pre_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. Default retry count is 0 times, which means the call won't be retried.
        :param post_webhook_retry_count: The number of times to retry a call to the `post_webhook_url` if the request times out (after 5 seconds) or it receives a 429, 503, or 504 HTTP response. The default is 0, which means the call won't be retried.
        :param notifications_log_enabled: Whether to log notifications. The default is `false`.

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "DefaultServiceRoleSid": default_service_role_sid,
                "DefaultChannelRoleSid": default_channel_role_sid,
                "DefaultChannelCreatorRoleSid": default_channel_creator_role_sid,
                "ReadStatusEnabled": serialize.boolean_to_string(read_status_enabled),
                "ReachabilityEnabled": serialize.boolean_to_string(
                    reachability_enabled
                ),
                "TypingIndicatorTimeout": typing_indicator_timeout,
                "ConsumptionReportInterval": consumption_report_interval,
                "Notifications.NewMessage.Enabled": serialize.boolean_to_string(
                    notifications_new_message_enabled
                ),
                "Notifications.NewMessage.Template": notifications_new_message_template,
                "Notifications.NewMessage.Sound": notifications_new_message_sound,
                "Notifications.NewMessage.BadgeCountEnabled": serialize.boolean_to_string(
                    notifications_new_message_badge_count_enabled
                ),
                "Notifications.AddedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_added_to_channel_enabled
                ),
                "Notifications.AddedToChannel.Template": notifications_added_to_channel_template,
                "Notifications.AddedToChannel.Sound": notifications_added_to_channel_sound,
                "Notifications.RemovedFromChannel.Enabled": serialize.boolean_to_string(
                    notifications_removed_from_channel_enabled
                ),
                "Notifications.RemovedFromChannel.Template": notifications_removed_from_channel_template,
                "Notifications.RemovedFromChannel.Sound": notifications_removed_from_channel_sound,
                "Notifications.InvitedToChannel.Enabled": serialize.boolean_to_string(
                    notifications_invited_to_channel_enabled
                ),
                "Notifications.InvitedToChannel.Template": notifications_invited_to_channel_template,
                "Notifications.InvitedToChannel.Sound": notifications_invited_to_channel_sound,
                "PreWebhookUrl": pre_webhook_url,
                "PostWebhookUrl": post_webhook_url,
                "WebhookMethod": webhook_method,
                "WebhookFilters": serialize.map(webhook_filters, lambda e: e),
                "Limits.ChannelMembers": limits_channel_members,
                "Limits.UserChannels": limits_user_channels,
                "Media.CompatibilityMessage": media_compatibility_message,
                "PreWebhookRetryCount": pre_webhook_retry_count,
                "PostWebhookRetryCount": post_webhook_retry_count,
                "Notifications.LogEnabled": serialize.boolean_to_string(
                    notifications_log_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    @property
    def bindings(self) -> BindingList:
        """
        Access the bindings
        """
        if self._bindings is None:
            self._bindings = BindingList(
                self._version,
                self._solution["sid"],
            )
        return self._bindings

    @property
    def channels(self) -> ChannelList:
        """
        Access the channels
        """
        if self._channels is None:
            self._channels = ChannelList(
                self._version,
                self._solution["sid"],
            )
        return self._channels

    @property
    def roles(self) -> RoleList:
        """
        Access the roles
        """
        if self._roles is None:
            self._roles = RoleList(
                self._version,
                self._solution["sid"],
            )
        return self._roles

    @property
    def users(self) -> UserList:
        """
        Access the users
        """
        if self._users is None:
            self._users = UserList(
                self._version,
                self._solution["sid"],
            )
        return self._users

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Chat.V2.ServiceContext {}>".format(context)


class ServicePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ServiceInstance:
        """
        Build an instance of ServiceInstance

        :param payload: Payload response from the API
        """
        return ServiceInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Chat.V2.ServicePage>"


class ServiceList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ServiceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Services"

    def create(self, friendly_name: str) -> ServiceInstance:
        """
        Create the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the new resource.

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    async def create_async(self, friendly_name: str) -> ServiceInstance:
        """
        Asynchronously create the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the new resource.

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ServiceInstance]:
        """
        Streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ServiceInstance]:
        """
        Asynchronously streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Asynchronously lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Asynchronously retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    def get_page(self, target_url: str) -> ServicePage:
        """
        Retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ServicePage(self._version, response)

    async def get_page_async(self, target_url: str) -> ServicePage:
        """
        Asynchronously retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ServicePage(self._version, response)

    def get(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid: The SID of the Service resource to update.
        """
        return ServiceContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid: The SID of the Service resource to update.
        """
        return ServiceContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Chat.V2.ServiceList>"
