r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Marketplace
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional
from twilio.base import values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ReferralConversionInstance(InstanceResource):

    class CreateReferralConversionRequest(object):
        """
        :ivar referral_account_sid:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.referral_account_sid: Optional[str] = payload.get(
                "referral_account_sid"
            )

        def to_dict(self):
            return {
                "referral_account_sid": self.referral_account_sid,
            }

    """
    :ivar converted_account_sid: 
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.converted_account_sid: Optional[str] = payload.get("converted_account_sid")

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Marketplace.V1.ReferralConversionInstance>"


class ReferralConversionList(ListResource):

    class CreateReferralConversionRequest(object):
        """
        :ivar referral_account_sid:
        """

        def __init__(self, payload: Dict[str, Any]):

            self.referral_account_sid: Optional[str] = payload.get(
                "referral_account_sid"
            )

        def to_dict(self):
            return {
                "referral_account_sid": self.referral_account_sid,
            }

    def __init__(self, version: Version):
        """
        Initialize the ReferralConversionList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/ReferralConversion"

    def create(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> ReferralConversionInstance:
        """
        Create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        data = create_referral_conversion_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ReferralConversionInstance(self._version, payload)

    async def create_async(
        self, create_referral_conversion_request: CreateReferralConversionRequest
    ) -> ReferralConversionInstance:
        """
        Asynchronously create the ReferralConversionInstance

        :param create_referral_conversion_request:

        :returns: The created ReferralConversionInstance
        """
        data = create_referral_conversion_request.to_dict()

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/json"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ReferralConversionInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Marketplace.V1.ReferralConversionList>"
