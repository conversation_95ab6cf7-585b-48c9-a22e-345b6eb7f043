r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Marketplace
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ModuleDataManagementInstance(InstanceResource):
    """
    :ivar url: URL to query the subresource.
    :ivar sid: ModuleSid that identifies this Listing.
    :ivar description: A JSON object describing the module and is displayed under the Description tab of the Module detail page. You can define the main body of the description, highlight key features or aspects of the module and if applicable, provide code samples for developers
    :ivar support: A JSON object containing information on how customers can obtain support for the module. Use this parameter to provide details such as contact information and support description.
    :ivar policies: A JSON object describing the module's privacy and legal policies and is displayed under the Policies tab of the Module detail page. The maximum file size for Policies is 5MB
    :ivar module_info: A JSON object containing essential attributes that define a module. This information is presented on the Module detail page in the Twilio Marketplace Catalog. You can pass the following attributes in the JSON object
    :ivar documentation: A JSON object for providing comprehensive information, instructions, and resources related to the module
    :ivar configuration: A JSON object for providing listing specific configuration. Contains button setup, notification url, among others.
    :ivar pricing: A JSON object for providing Listing specific pricing information.
    :ivar listings:
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.url: Optional[str] = payload.get("url")
        self.sid: Optional[str] = payload.get("sid")
        self.description: Optional[Dict[str, object]] = payload.get("description")
        self.support: Optional[Dict[str, object]] = payload.get("support")
        self.policies: Optional[Dict[str, object]] = payload.get("policies")
        self.module_info: Optional[Dict[str, object]] = payload.get("module_info")
        self.documentation: Optional[Dict[str, object]] = payload.get("documentation")
        self.configuration: Optional[Dict[str, object]] = payload.get("configuration")
        self.pricing: Optional[Dict[str, object]] = payload.get("pricing")
        self.listings: Optional[List[Dict[str, object]]] = payload.get("listings")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ModuleDataManagementContext] = None

    @property
    def _proxy(self) -> "ModuleDataManagementContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ModuleDataManagementContext for this ModuleDataManagementInstance
        """
        if self._context is None:
            self._context = ModuleDataManagementContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "ModuleDataManagementInstance":
        """
        Fetch the ModuleDataManagementInstance


        :returns: The fetched ModuleDataManagementInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ModuleDataManagementInstance":
        """
        Asynchronous coroutine to fetch the ModuleDataManagementInstance


        :returns: The fetched ModuleDataManagementInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        module_info: Union[str, object] = values.unset,
        description: Union[str, object] = values.unset,
        documentation: Union[str, object] = values.unset,
        policies: Union[str, object] = values.unset,
        support: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        pricing: Union[str, object] = values.unset,
    ) -> "ModuleDataManagementInstance":
        """
        Update the ModuleDataManagementInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param description: A JSON object describing the Listing. You can define the main body of the description, highlight key features or aspects of the Listing, and provide code samples for developers if applicable.
        :param documentation: A JSON object for providing comprehensive information, instructions, and resources related to the Listing.
        :param policies: A JSON object describing the Listing's privacy and legal policies. The maximum file size for Policies is 5MB.
        :param support: A JSON object containing information on how Marketplace users can obtain support for the Listing. Use this parameter to provide details such as contact information and support description.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
        :param pricing: A JSON object for providing Listing's purchase options.

        :returns: The updated ModuleDataManagementInstance
        """
        return self._proxy.update(
            module_info=module_info,
            description=description,
            documentation=documentation,
            policies=policies,
            support=support,
            configuration=configuration,
            pricing=pricing,
        )

    async def update_async(
        self,
        module_info: Union[str, object] = values.unset,
        description: Union[str, object] = values.unset,
        documentation: Union[str, object] = values.unset,
        policies: Union[str, object] = values.unset,
        support: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        pricing: Union[str, object] = values.unset,
    ) -> "ModuleDataManagementInstance":
        """
        Asynchronous coroutine to update the ModuleDataManagementInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param description: A JSON object describing the Listing. You can define the main body of the description, highlight key features or aspects of the Listing, and provide code samples for developers if applicable.
        :param documentation: A JSON object for providing comprehensive information, instructions, and resources related to the Listing.
        :param policies: A JSON object describing the Listing's privacy and legal policies. The maximum file size for Policies is 5MB.
        :param support: A JSON object containing information on how Marketplace users can obtain support for the Listing. Use this parameter to provide details such as contact information and support description.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
        :param pricing: A JSON object for providing Listing's purchase options.

        :returns: The updated ModuleDataManagementInstance
        """
        return await self._proxy.update_async(
            module_info=module_info,
            description=description,
            documentation=documentation,
            policies=policies,
            support=support,
            configuration=configuration,
            pricing=pricing,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Marketplace.V1.ModuleDataManagementInstance {}>".format(context)


class ModuleDataManagementContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ModuleDataManagementContext

        :param version: Version that contains the resource
        :param sid: SID that uniquely identifies the Listing.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Listing/{sid}".format(**self._solution)

    def fetch(self) -> ModuleDataManagementInstance:
        """
        Fetch the ModuleDataManagementInstance


        :returns: The fetched ModuleDataManagementInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ModuleDataManagementInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ModuleDataManagementInstance:
        """
        Asynchronous coroutine to fetch the ModuleDataManagementInstance


        :returns: The fetched ModuleDataManagementInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ModuleDataManagementInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        module_info: Union[str, object] = values.unset,
        description: Union[str, object] = values.unset,
        documentation: Union[str, object] = values.unset,
        policies: Union[str, object] = values.unset,
        support: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        pricing: Union[str, object] = values.unset,
    ) -> ModuleDataManagementInstance:
        """
        Update the ModuleDataManagementInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param description: A JSON object describing the Listing. You can define the main body of the description, highlight key features or aspects of the Listing, and provide code samples for developers if applicable.
        :param documentation: A JSON object for providing comprehensive information, instructions, and resources related to the Listing.
        :param policies: A JSON object describing the Listing's privacy and legal policies. The maximum file size for Policies is 5MB.
        :param support: A JSON object containing information on how Marketplace users can obtain support for the Listing. Use this parameter to provide details such as contact information and support description.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
        :param pricing: A JSON object for providing Listing's purchase options.

        :returns: The updated ModuleDataManagementInstance
        """

        data = values.of(
            {
                "ModuleInfo": module_info,
                "Description": description,
                "Documentation": documentation,
                "Policies": policies,
                "Support": support,
                "Configuration": configuration,
                "Pricing": pricing,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ModuleDataManagementInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    async def update_async(
        self,
        module_info: Union[str, object] = values.unset,
        description: Union[str, object] = values.unset,
        documentation: Union[str, object] = values.unset,
        policies: Union[str, object] = values.unset,
        support: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        pricing: Union[str, object] = values.unset,
    ) -> ModuleDataManagementInstance:
        """
        Asynchronous coroutine to update the ModuleDataManagementInstance

        :param module_info: A JSON object containing essential attributes that define a Listing.
        :param description: A JSON object describing the Listing. You can define the main body of the description, highlight key features or aspects of the Listing, and provide code samples for developers if applicable.
        :param documentation: A JSON object for providing comprehensive information, instructions, and resources related to the Listing.
        :param policies: A JSON object describing the Listing's privacy and legal policies. The maximum file size for Policies is 5MB.
        :param support: A JSON object containing information on how Marketplace users can obtain support for the Listing. Use this parameter to provide details such as contact information and support description.
        :param configuration: A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
        :param pricing: A JSON object for providing Listing's purchase options.

        :returns: The updated ModuleDataManagementInstance
        """

        data = values.of(
            {
                "ModuleInfo": module_info,
                "Description": description,
                "Documentation": documentation,
                "Policies": policies,
                "Support": support,
                "Configuration": configuration,
                "Pricing": pricing,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ModuleDataManagementInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Marketplace.V1.ModuleDataManagementContext {}>".format(context)


class ModuleDataManagementList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ModuleDataManagementList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, sid: str) -> ModuleDataManagementContext:
        """
        Constructs a ModuleDataManagementContext

        :param sid: SID that uniquely identifies the Listing.
        """
        return ModuleDataManagementContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ModuleDataManagementContext:
        """
        Constructs a ModuleDataManagementContext

        :param sid: SID that uniquely identifies the Listing.
        """
        return ModuleDataManagementContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Marketplace.V1.ModuleDataManagementList>"
