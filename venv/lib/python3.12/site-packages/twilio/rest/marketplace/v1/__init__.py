r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Marketplace
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.marketplace.v1.available_add_on import AvailableAddOnList
from twilio.rest.marketplace.v1.installed_add_on import InstalledAddOnList
from twilio.rest.marketplace.v1.module_data import ModuleDataList
from twilio.rest.marketplace.v1.module_data_management import ModuleDataManagementList
from twilio.rest.marketplace.v1.referral_conversion import ReferralConversionList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of Marketplace

        :param domain: The Twilio.marketplace domain
        """
        super().__init__(domain, "v1")
        self._available_add_ons: Optional[AvailableAddOnList] = None
        self._installed_add_ons: Optional[InstalledAddOnList] = None
        self._module_data: Optional[ModuleDataList] = None
        self._module_data_management: Optional[ModuleDataManagementList] = None
        self._referral_conversion: Optional[ReferralConversionList] = None

    @property
    def available_add_ons(self) -> AvailableAddOnList:
        if self._available_add_ons is None:
            self._available_add_ons = AvailableAddOnList(self)
        return self._available_add_ons

    @property
    def installed_add_ons(self) -> InstalledAddOnList:
        if self._installed_add_ons is None:
            self._installed_add_ons = InstalledAddOnList(self)
        return self._installed_add_ons

    @property
    def module_data(self) -> ModuleDataList:
        if self._module_data is None:
            self._module_data = ModuleDataList(self)
        return self._module_data

    @property
    def module_data_management(self) -> ModuleDataManagementList:
        if self._module_data_management is None:
            self._module_data_management = ModuleDataManagementList(self)
        return self._module_data_management

    @property
    def referral_conversion(self) -> ReferralConversionList:
        if self._referral_conversion is None:
            self._referral_conversion = ReferralConversionList(self)
        return self._referral_conversion

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Marketplace.V1>"
