r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.api.v2010.account.sip.domain.auth_types import AuthTypesList
from twilio.rest.api.v2010.account.sip.domain.credential_list_mapping import (
    CredentialListMappingList,
)
from twilio.rest.api.v2010.account.sip.domain.ip_access_control_list_mapping import (
    IpAccessControlListMappingList,
)


class DomainInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the SipDomain resource.
    :ivar api_version: The API version used to process the call.
    :ivar auth_type: The types of authentication you have mapped to your domain. Can be: `IP_ACL` and `CREDENTIAL_LIST`. If you have both defined for your domain, both will be returned in a comma delimited string. If `auth_type` is not defined, the domain will not be able to receive any traffic.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \"-\" and must end with `sip.twilio.com`.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar sid: The unique string that that we created to identify the SipDomain resource.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    :ivar voice_fallback_method: The HTTP method we use to call `voice_fallback_url`. Can be: `GET` or `POST`.
    :ivar voice_fallback_url: The URL that we call when an error occurs while retrieving or executing the TwiML requested from `voice_url`.
    :ivar voice_method: The HTTP method we use to call `voice_url`. Can be: `GET` or `POST`.
    :ivar voice_status_callback_method: The HTTP method we use to call `voice_status_callback_url`. Either `GET` or `POST`.
    :ivar voice_status_callback_url: The URL that we call to pass status parameters (such as call ended) to your application.
    :ivar voice_url: The URL we call using the `voice_method` when the domain receives a call.
    :ivar subresource_uris: A list of mapping resources associated with the SIP Domain resource identified by their relative URIs.
    :ivar sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls.
    :ivar emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
    :ivar secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
    :ivar byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
    :ivar emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.api_version: Optional[str] = payload.get("api_version")
        self.auth_type: Optional[str] = payload.get("auth_type")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.domain_name: Optional[str] = payload.get("domain_name")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.sid: Optional[str] = payload.get("sid")
        self.uri: Optional[str] = payload.get("uri")
        self.voice_fallback_method: Optional[str] = payload.get("voice_fallback_method")
        self.voice_fallback_url: Optional[str] = payload.get("voice_fallback_url")
        self.voice_method: Optional[str] = payload.get("voice_method")
        self.voice_status_callback_method: Optional[str] = payload.get(
            "voice_status_callback_method"
        )
        self.voice_status_callback_url: Optional[str] = payload.get(
            "voice_status_callback_url"
        )
        self.voice_url: Optional[str] = payload.get("voice_url")
        self.subresource_uris: Optional[Dict[str, object]] = payload.get(
            "subresource_uris"
        )
        self.sip_registration: Optional[bool] = payload.get("sip_registration")
        self.emergency_calling_enabled: Optional[bool] = payload.get(
            "emergency_calling_enabled"
        )
        self.secure: Optional[bool] = payload.get("secure")
        self.byoc_trunk_sid: Optional[str] = payload.get("byoc_trunk_sid")
        self.emergency_caller_sid: Optional[str] = payload.get("emergency_caller_sid")

        self._solution = {
            "account_sid": account_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[DomainContext] = None

    @property
    def _proxy(self) -> "DomainContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DomainContext for this DomainInstance
        """
        if self._context is None:
            self._context = DomainContext(
                self._version,
                account_sid=self._solution["account_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the DomainInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the DomainInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "DomainInstance":
        """
        Fetch the DomainInstance


        :returns: The fetched DomainInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "DomainInstance":
        """
        Asynchronous coroutine to fetch the DomainInstance


        :returns: The fetched DomainInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        domain_name: Union[str, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> "DomainInstance":
        """
        Update the DomainInstance

        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML requested by `voice_url`.
        :param voice_method: The HTTP method we should use to call `voice_url`
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_url: The URL we should call when the domain receives a call.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The updated DomainInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            voice_fallback_method=voice_fallback_method,
            voice_fallback_url=voice_fallback_url,
            voice_method=voice_method,
            voice_status_callback_method=voice_status_callback_method,
            voice_status_callback_url=voice_status_callback_url,
            voice_url=voice_url,
            sip_registration=sip_registration,
            domain_name=domain_name,
            emergency_calling_enabled=emergency_calling_enabled,
            secure=secure,
            byoc_trunk_sid=byoc_trunk_sid,
            emergency_caller_sid=emergency_caller_sid,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        domain_name: Union[str, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> "DomainInstance":
        """
        Asynchronous coroutine to update the DomainInstance

        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML requested by `voice_url`.
        :param voice_method: The HTTP method we should use to call `voice_url`
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_url: The URL we should call when the domain receives a call.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The updated DomainInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            voice_fallback_method=voice_fallback_method,
            voice_fallback_url=voice_fallback_url,
            voice_method=voice_method,
            voice_status_callback_method=voice_status_callback_method,
            voice_status_callback_url=voice_status_callback_url,
            voice_url=voice_url,
            sip_registration=sip_registration,
            domain_name=domain_name,
            emergency_calling_enabled=emergency_calling_enabled,
            secure=secure,
            byoc_trunk_sid=byoc_trunk_sid,
            emergency_caller_sid=emergency_caller_sid,
        )

    @property
    def auth(self) -> AuthTypesList:
        """
        Access the auth
        """
        return self._proxy.auth

    @property
    def credential_list_mappings(self) -> CredentialListMappingList:
        """
        Access the credential_list_mappings
        """
        return self._proxy.credential_list_mappings

    @property
    def ip_access_control_list_mappings(self) -> IpAccessControlListMappingList:
        """
        Access the ip_access_control_list_mappings
        """
        return self._proxy.ip_access_control_list_mappings

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.DomainInstance {}>".format(context)


class DomainContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, sid: str):
        """
        Initialize the DomainContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the SipDomain resource to update.
        :param sid: The Twilio-provided string that uniquely identifies the SipDomain resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/SIP/Domains/{sid}.json".format(
            **self._solution
        )

        self._auth: Optional[AuthTypesList] = None
        self._credential_list_mappings: Optional[CredentialListMappingList] = None
        self._ip_access_control_list_mappings: Optional[
            IpAccessControlListMappingList
        ] = None

    def delete(self) -> bool:
        """
        Deletes the DomainInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the DomainInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> DomainInstance:
        """
        Fetch the DomainInstance


        :returns: The fetched DomainInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return DomainInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> DomainInstance:
        """
        Asynchronous coroutine to fetch the DomainInstance


        :returns: The fetched DomainInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return DomainInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        domain_name: Union[str, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> DomainInstance:
        """
        Update the DomainInstance

        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML requested by `voice_url`.
        :param voice_method: The HTTP method we should use to call `voice_url`
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_url: The URL we should call when the domain receives a call.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The updated DomainInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "VoiceFallbackMethod": voice_fallback_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceMethod": voice_method,
                "VoiceStatusCallbackMethod": voice_status_callback_method,
                "VoiceStatusCallbackUrl": voice_status_callback_url,
                "VoiceUrl": voice_url,
                "SipRegistration": serialize.boolean_to_string(sip_registration),
                "DomainName": domain_name,
                "EmergencyCallingEnabled": serialize.boolean_to_string(
                    emergency_calling_enabled
                ),
                "Secure": serialize.boolean_to_string(secure),
                "ByocTrunkSid": byoc_trunk_sid,
                "EmergencyCallerSid": emergency_caller_sid,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return DomainInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        domain_name: Union[str, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> DomainInstance:
        """
        Asynchronous coroutine to update the DomainInstance

        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML requested by `voice_url`.
        :param voice_method: The HTTP method we should use to call `voice_url`
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_url: The URL we should call when the domain receives a call.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The updated DomainInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "VoiceFallbackMethod": voice_fallback_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceMethod": voice_method,
                "VoiceStatusCallbackMethod": voice_status_callback_method,
                "VoiceStatusCallbackUrl": voice_status_callback_url,
                "VoiceUrl": voice_url,
                "SipRegistration": serialize.boolean_to_string(sip_registration),
                "DomainName": domain_name,
                "EmergencyCallingEnabled": serialize.boolean_to_string(
                    emergency_calling_enabled
                ),
                "Secure": serialize.boolean_to_string(secure),
                "ByocTrunkSid": byoc_trunk_sid,
                "EmergencyCallerSid": emergency_caller_sid,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return DomainInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    @property
    def auth(self) -> AuthTypesList:
        """
        Access the auth
        """
        if self._auth is None:
            self._auth = AuthTypesList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._auth

    @property
    def credential_list_mappings(self) -> CredentialListMappingList:
        """
        Access the credential_list_mappings
        """
        if self._credential_list_mappings is None:
            self._credential_list_mappings = CredentialListMappingList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._credential_list_mappings

    @property
    def ip_access_control_list_mappings(self) -> IpAccessControlListMappingList:
        """
        Access the ip_access_control_list_mappings
        """
        if self._ip_access_control_list_mappings is None:
            self._ip_access_control_list_mappings = IpAccessControlListMappingList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._ip_access_control_list_mappings

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.DomainContext {}>".format(context)


class DomainPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> DomainInstance:
        """
        Build an instance of DomainInstance

        :param payload: Payload response from the API
        """
        return DomainInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.DomainPage>"


class DomainList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the DomainList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the SipDomain resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/SIP/Domains.json".format(**self._solution)

    def create(
        self,
        domain_name: str,
        friendly_name: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> DomainInstance:
        """
        Create the DomainInstance

        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_url: The URL we should when the domain receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `voice_url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The created DomainInstance
        """

        data = values.of(
            {
                "DomainName": domain_name,
                "FriendlyName": friendly_name,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "VoiceStatusCallbackUrl": voice_status_callback_url,
                "VoiceStatusCallbackMethod": voice_status_callback_method,
                "SipRegistration": serialize.boolean_to_string(sip_registration),
                "EmergencyCallingEnabled": serialize.boolean_to_string(
                    emergency_calling_enabled
                ),
                "Secure": serialize.boolean_to_string(secure),
                "ByocTrunkSid": byoc_trunk_sid,
                "EmergencyCallerSid": emergency_caller_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return DomainInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    async def create_async(
        self,
        domain_name: str,
        friendly_name: Union[str, object] = values.unset,
        voice_url: Union[str, object] = values.unset,
        voice_method: Union[str, object] = values.unset,
        voice_fallback_url: Union[str, object] = values.unset,
        voice_fallback_method: Union[str, object] = values.unset,
        voice_status_callback_url: Union[str, object] = values.unset,
        voice_status_callback_method: Union[str, object] = values.unset,
        sip_registration: Union[bool, object] = values.unset,
        emergency_calling_enabled: Union[bool, object] = values.unset,
        secure: Union[bool, object] = values.unset,
        byoc_trunk_sid: Union[str, object] = values.unset,
        emergency_caller_sid: Union[str, object] = values.unset,
    ) -> DomainInstance:
        """
        Asynchronously create the DomainInstance

        :param domain_name: The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and \\\"-\\\" and must end with `sip.twilio.com`.
        :param friendly_name: A descriptive string that you created to describe the resource. It can be up to 64 characters long.
        :param voice_url: The URL we should when the domain receives a call.
        :param voice_method: The HTTP method we should use to call `voice_url`. Can be: `GET` or `POST`.
        :param voice_fallback_url: The URL that we should call when an error occurs while retrieving or executing the TwiML from `voice_url`.
        :param voice_fallback_method: The HTTP method we should use to call `voice_fallback_url`. Can be: `GET` or `POST`.
        :param voice_status_callback_url: The URL that we should call to pass status parameters (such as call ended) to your application.
        :param voice_status_callback_method: The HTTP method we should use to call `voice_status_callback_url`. Can be: `GET` or `POST`.
        :param sip_registration: Whether to allow SIP Endpoints to register with the domain to receive calls. Can be `true` or `false`. `true` allows SIP Endpoints to register with the domain to receive calls, `false` does not.
        :param emergency_calling_enabled: Whether emergency calling is enabled for the domain. If enabled, allows emergency calls on the domain from phone numbers with validated addresses.
        :param secure: Whether secure SIP is enabled for the domain. If enabled, TLS will be enforced and SRTP will be negotiated on all incoming calls to this sip domain.
        :param byoc_trunk_sid: The SID of the BYOC Trunk(Bring Your Own Carrier) resource that the Sip Domain will be associated with.
        :param emergency_caller_sid: Whether an emergency caller sid is configured for the domain. If present, this phone number will be used as the callback for the emergency call.

        :returns: The created DomainInstance
        """

        data = values.of(
            {
                "DomainName": domain_name,
                "FriendlyName": friendly_name,
                "VoiceUrl": voice_url,
                "VoiceMethod": voice_method,
                "VoiceFallbackUrl": voice_fallback_url,
                "VoiceFallbackMethod": voice_fallback_method,
                "VoiceStatusCallbackUrl": voice_status_callback_url,
                "VoiceStatusCallbackMethod": voice_status_callback_method,
                "SipRegistration": serialize.boolean_to_string(sip_registration),
                "EmergencyCallingEnabled": serialize.boolean_to_string(
                    emergency_calling_enabled
                ),
                "Secure": serialize.boolean_to_string(secure),
                "ByocTrunkSid": byoc_trunk_sid,
                "EmergencyCallerSid": emergency_caller_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return DomainInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[DomainInstance]:
        """
        Streams DomainInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[DomainInstance]:
        """
        Asynchronously streams DomainInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DomainInstance]:
        """
        Lists DomainInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[DomainInstance]:
        """
        Asynchronously lists DomainInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DomainPage:
        """
        Retrieve a single page of DomainInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DomainInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return DomainPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> DomainPage:
        """
        Asynchronously retrieve a single page of DomainInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of DomainInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return DomainPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> DomainPage:
        """
        Retrieve a specific page of DomainInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DomainInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return DomainPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> DomainPage:
        """
        Asynchronously retrieve a specific page of DomainInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of DomainInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return DomainPage(self._version, response, self._solution)

    def get(self, sid: str) -> DomainContext:
        """
        Constructs a DomainContext

        :param sid: The Twilio-provided string that uniquely identifies the SipDomain resource to update.
        """
        return DomainContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __call__(self, sid: str) -> DomainContext:
        """
        Constructs a DomainContext

        :param sid: The Twilio-provided string that uniquely identifies the SipDomain resource to update.
        """
        return DomainContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.DomainList>"
