r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.api.v2010.account.call.event import EventList
from twilio.rest.api.v2010.account.call.notification import NotificationList
from twilio.rest.api.v2010.account.call.payment import PaymentList
from twilio.rest.api.v2010.account.call.recording import RecordingList
from twilio.rest.api.v2010.account.call.siprec import SiprecList
from twilio.rest.api.v2010.account.call.stream import StreamList
from twilio.rest.api.v2010.account.call.transcription import TranscriptionList
from twilio.rest.api.v2010.account.call.user_defined_message import (
    UserDefinedMessageList,
)
from twilio.rest.api.v2010.account.call.user_defined_message_subscription import (
    UserDefinedMessageSubscriptionList,
)


class CallInstance(InstanceResource):

    class Status(object):
        QUEUED = "queued"
        RINGING = "ringing"
        IN_PROGRESS = "in-progress"
        COMPLETED = "completed"
        BUSY = "busy"
        FAILED = "failed"
        NO_ANSWER = "no-answer"
        CANCELED = "canceled"

    class UpdateStatus(object):
        CANCELED = "canceled"
        COMPLETED = "completed"

    """
    :ivar sid: The unique string that we created to identify this Call resource.
    :ivar date_created: The date and time in UTC that this resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in UTC that this resource was last updated, specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar parent_call_sid: The SID that identifies the call that created this leg.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Call resource.
    :ivar to: The phone number, SIP address, Client identifier or SIM SID that received this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). SIP addresses are formatted as `<EMAIL>`. Client identifiers are formatted `client:name`. SIM SIDs are formatted as `sim:sid`.
    :ivar to_formatted: The phone number, SIP address or Client identifier that received this call. Formatted for display. Non-North American phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +************).
    :ivar _from: The phone number, SIP address, Client identifier or SIM SID that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). SIP addresses are formatted as `<EMAIL>`. Client identifiers are formatted `client:name`. SIM SIDs are formatted as `sim:sid`.
    :ivar from_formatted: The calling phone number, SIP address, or Client identifier formatted for display. Non-North American phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +************).
    :ivar phone_number_sid: If the call was inbound, this is the SID of the IncomingPhoneNumber resource that received the call. If the call was outbound, it is the SID of the OutgoingCallerId resource from which the call was placed.
    :ivar status: 
    :ivar start_time: The start time of the call, given as UTC in [RFC 2822](https://www.php.net/manual/en/class.datetime.php#datetime.constants.rfc2822) format. Empty if the call has not yet been dialed.
    :ivar end_time: The time the call ended, given as UTC in [RFC 2822](https://www.php.net/manual/en/class.datetime.php#datetime.constants.rfc2822) format. Empty if the call did not complete successfully.
    :ivar duration: The length of the call in seconds. This value is empty for busy, failed, unanswered, or ongoing calls.
    :ivar price: The charge for this call, in the currency associated with the account. Populated after the call is completed. May not be immediately available. The price associated with a call only reflects the charge for connectivity.  Charges for other call-related features such as Answering Machine Detection, Text-To-Speech, and SIP REFER are not included in this value.
    :ivar price_unit: The currency in which `Price` is measured, in [ISO 4127](https://www.iso.org/iso/home/<USER>/currency_codes.htm) format (e.g., `USD`, `EUR`, `JPY`). Always capitalized for calls.
    :ivar direction: A string describing the direction of the call. Can be: `inbound` for inbound calls, `outbound-api` for calls initiated via the REST API or `outbound-dial` for calls initiated by a `<Dial>` verb. Using [Elastic SIP Trunking](https://www.twilio.com/docs/sip-trunking), the values can be [`trunking-terminating`](https://www.twilio.com/docs/sip-trunking#termination) for outgoing calls from your communications infrastructure to the PSTN or [`trunking-originating`](https://www.twilio.com/docs/sip-trunking#origination) for incoming calls to your communications infrastructure from the PSTN.
    :ivar answered_by: Either `human` or `machine` if this call was initiated with answering machine detection. Empty otherwise.
    :ivar api_version: The API version used to create the call.
    :ivar forwarded_from: The forwarding phone number if this call was an incoming call forwarded from another number (depends on carrier supporting forwarding). Otherwise, empty.
    :ivar group_sid: The Group SID associated with this call. If no Group is associated with the call, the field is empty.
    :ivar caller_name: The caller's name if this call was an incoming call to a phone number with caller ID Lookup enabled. Otherwise, empty.
    :ivar queue_time: The wait time in milliseconds before the call is placed.
    :ivar trunk_sid: The unique identifier of the trunk resource that was used for this call. The field is empty if the call was not made using a SIP trunk or if the call is not terminated.
    :ivar uri: The URI of this resource, relative to `https://api.twilio.com`.
    :ivar subresource_uris: A list of subresources available to this call, identified by their URIs relative to `https://api.twilio.com`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.parent_call_sid: Optional[str] = payload.get("parent_call_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.to: Optional[str] = payload.get("to")
        self.to_formatted: Optional[str] = payload.get("to_formatted")
        self._from: Optional[str] = payload.get("from")
        self.from_formatted: Optional[str] = payload.get("from_formatted")
        self.phone_number_sid: Optional[str] = payload.get("phone_number_sid")
        self.status: Optional["CallInstance.Status"] = payload.get("status")
        self.start_time: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("start_time")
        )
        self.end_time: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("end_time")
        )
        self.duration: Optional[str] = payload.get("duration")
        self.price: Optional[str] = payload.get("price")
        self.price_unit: Optional[str] = payload.get("price_unit")
        self.direction: Optional[str] = payload.get("direction")
        self.answered_by: Optional[str] = payload.get("answered_by")
        self.api_version: Optional[str] = payload.get("api_version")
        self.forwarded_from: Optional[str] = payload.get("forwarded_from")
        self.group_sid: Optional[str] = payload.get("group_sid")
        self.caller_name: Optional[str] = payload.get("caller_name")
        self.queue_time: Optional[str] = payload.get("queue_time")
        self.trunk_sid: Optional[str] = payload.get("trunk_sid")
        self.uri: Optional[str] = payload.get("uri")
        self.subresource_uris: Optional[Dict[str, object]] = payload.get(
            "subresource_uris"
        )

        self._solution = {
            "account_sid": account_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[CallContext] = None

    @property
    def _proxy(self) -> "CallContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: CallContext for this CallInstance
        """
        if self._context is None:
            self._context = CallContext(
                self._version,
                account_sid=self._solution["account_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the CallInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CallInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "CallInstance":
        """
        Fetch the CallInstance


        :returns: The fetched CallInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "CallInstance":
        """
        Asynchronous coroutine to fetch the CallInstance


        :returns: The fetched CallInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        url: Union[str, object] = values.unset,
        method: Union[str, object] = values.unset,
        status: Union["CallInstance.UpdateStatus", object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
    ) -> "CallInstance":
        """
        Update the CallInstance

        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param method: The HTTP method we should use when calling the `url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status:
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_method: The HTTP method we should use when requesting the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url. Twiml and url parameters are mutually exclusive
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.

        :returns: The updated CallInstance
        """
        return self._proxy.update(
            url=url,
            method=method,
            status=status,
            fallback_url=fallback_url,
            fallback_method=fallback_method,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            twiml=twiml,
            time_limit=time_limit,
        )

    async def update_async(
        self,
        url: Union[str, object] = values.unset,
        method: Union[str, object] = values.unset,
        status: Union["CallInstance.UpdateStatus", object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
    ) -> "CallInstance":
        """
        Asynchronous coroutine to update the CallInstance

        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param method: The HTTP method we should use when calling the `url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status:
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_method: The HTTP method we should use when requesting the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url. Twiml and url parameters are mutually exclusive
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.

        :returns: The updated CallInstance
        """
        return await self._proxy.update_async(
            url=url,
            method=method,
            status=status,
            fallback_url=fallback_url,
            fallback_method=fallback_method,
            status_callback=status_callback,
            status_callback_method=status_callback_method,
            twiml=twiml,
            time_limit=time_limit,
        )

    @property
    def events(self) -> EventList:
        """
        Access the events
        """
        return self._proxy.events

    @property
    def notifications(self) -> NotificationList:
        """
        Access the notifications
        """
        return self._proxy.notifications

    @property
    def payments(self) -> PaymentList:
        """
        Access the payments
        """
        return self._proxy.payments

    @property
    def recordings(self) -> RecordingList:
        """
        Access the recordings
        """
        return self._proxy.recordings

    @property
    def siprec(self) -> SiprecList:
        """
        Access the siprec
        """
        return self._proxy.siprec

    @property
    def streams(self) -> StreamList:
        """
        Access the streams
        """
        return self._proxy.streams

    @property
    def transcriptions(self) -> TranscriptionList:
        """
        Access the transcriptions
        """
        return self._proxy.transcriptions

    @property
    def user_defined_messages(self) -> UserDefinedMessageList:
        """
        Access the user_defined_messages
        """
        return self._proxy.user_defined_messages

    @property
    def user_defined_message_subscriptions(self) -> UserDefinedMessageSubscriptionList:
        """
        Access the user_defined_message_subscriptions
        """
        return self._proxy.user_defined_message_subscriptions

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.CallInstance {}>".format(context)


class CallContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, sid: str):
        """
        Initialize the CallContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Call resource(s) to update.
        :param sid: The Twilio-provided string that uniquely identifies the Call resource to update
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/Calls/{sid}.json".format(**self._solution)

        self._events: Optional[EventList] = None
        self._notifications: Optional[NotificationList] = None
        self._payments: Optional[PaymentList] = None
        self._recordings: Optional[RecordingList] = None
        self._siprec: Optional[SiprecList] = None
        self._streams: Optional[StreamList] = None
        self._transcriptions: Optional[TranscriptionList] = None
        self._user_defined_messages: Optional[UserDefinedMessageList] = None
        self._user_defined_message_subscriptions: Optional[
            UserDefinedMessageSubscriptionList
        ] = None

    def delete(self) -> bool:
        """
        Deletes the CallInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the CallInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> CallInstance:
        """
        Fetch the CallInstance


        :returns: The fetched CallInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return CallInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> CallInstance:
        """
        Asynchronous coroutine to fetch the CallInstance


        :returns: The fetched CallInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return CallInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        url: Union[str, object] = values.unset,
        method: Union[str, object] = values.unset,
        status: Union["CallInstance.UpdateStatus", object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
    ) -> CallInstance:
        """
        Update the CallInstance

        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param method: The HTTP method we should use when calling the `url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status:
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_method: The HTTP method we should use when requesting the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url. Twiml and url parameters are mutually exclusive
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.

        :returns: The updated CallInstance
        """

        data = values.of(
            {
                "Url": url,
                "Method": method,
                "Status": status,
                "FallbackUrl": fallback_url,
                "FallbackMethod": fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "Twiml": twiml,
                "TimeLimit": time_limit,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CallInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        url: Union[str, object] = values.unset,
        method: Union[str, object] = values.unset,
        status: Union["CallInstance.UpdateStatus", object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
    ) -> CallInstance:
        """
        Asynchronous coroutine to update the CallInstance

        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param method: The HTTP method we should use when calling the `url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status:
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_method: The HTTP method we should use when requesting the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url. Twiml and url parameters are mutually exclusive
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.

        :returns: The updated CallInstance
        """

        data = values.of(
            {
                "Url": url,
                "Method": method,
                "Status": status,
                "FallbackUrl": fallback_url,
                "FallbackMethod": fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "Twiml": twiml,
                "TimeLimit": time_limit,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CallInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    @property
    def events(self) -> EventList:
        """
        Access the events
        """
        if self._events is None:
            self._events = EventList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._events

    @property
    def notifications(self) -> NotificationList:
        """
        Access the notifications
        """
        if self._notifications is None:
            self._notifications = NotificationList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._notifications

    @property
    def payments(self) -> PaymentList:
        """
        Access the payments
        """
        if self._payments is None:
            self._payments = PaymentList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._payments

    @property
    def recordings(self) -> RecordingList:
        """
        Access the recordings
        """
        if self._recordings is None:
            self._recordings = RecordingList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._recordings

    @property
    def siprec(self) -> SiprecList:
        """
        Access the siprec
        """
        if self._siprec is None:
            self._siprec = SiprecList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._siprec

    @property
    def streams(self) -> StreamList:
        """
        Access the streams
        """
        if self._streams is None:
            self._streams = StreamList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._streams

    @property
    def transcriptions(self) -> TranscriptionList:
        """
        Access the transcriptions
        """
        if self._transcriptions is None:
            self._transcriptions = TranscriptionList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._transcriptions

    @property
    def user_defined_messages(self) -> UserDefinedMessageList:
        """
        Access the user_defined_messages
        """
        if self._user_defined_messages is None:
            self._user_defined_messages = UserDefinedMessageList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._user_defined_messages

    @property
    def user_defined_message_subscriptions(self) -> UserDefinedMessageSubscriptionList:
        """
        Access the user_defined_message_subscriptions
        """
        if self._user_defined_message_subscriptions is None:
            self._user_defined_message_subscriptions = (
                UserDefinedMessageSubscriptionList(
                    self._version,
                    self._solution["account_sid"],
                    self._solution["sid"],
                )
            )
        return self._user_defined_message_subscriptions

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.CallContext {}>".format(context)


class CallPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> CallInstance:
        """
        Build an instance of CallInstance

        :param payload: Payload response from the API
        """
        return CallInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.CallPage>"


class CallList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the CallList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Call resource(s) to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/Calls.json".format(**self._solution)

    def create(
        self,
        to: str,
        from_: str,
        method: Union[str, object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_event: Union[List[str], object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        send_digits: Union[str, object] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        machine_detection: Union[str, object] = values.unset,
        machine_detection_timeout: Union[int, object] = values.unset,
        recording_status_callback_event: Union[List[str], object] = values.unset,
        trim: Union[str, object] = values.unset,
        caller_id: Union[str, object] = values.unset,
        machine_detection_speech_threshold: Union[int, object] = values.unset,
        machine_detection_speech_end_threshold: Union[int, object] = values.unset,
        machine_detection_silence_timeout: Union[int, object] = values.unset,
        async_amd: Union[str, object] = values.unset,
        async_amd_status_callback: Union[str, object] = values.unset,
        async_amd_status_callback_method: Union[str, object] = values.unset,
        byoc: Union[str, object] = values.unset,
        call_reason: Union[str, object] = values.unset,
        call_token: Union[str, object] = values.unset,
        recording_track: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
        url: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        application_sid: Union[str, object] = values.unset,
    ) -> CallInstance:
        """
        Create the CallInstance

        :param to: The phone number, SIP address, or client identifier to call.
        :param from_: The phone number or client identifier to use as the caller id. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `From` must also be a phone number.
        :param method: The HTTP method we should use when calling the `url` parameter's value. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_event: The call progress events that we will send to the `status_callback` URL. Can be: `initiated`, `ringing`, `answered`, and `completed`. If no event is specified, we send the `completed` status. If you want to receive multiple events, specify each one in a separate `status_callback_event` parameter. See the code sample for [monitoring call progress](https://www.twilio.com/docs/voice/api/call-resource?code-sample=code-create-a-call-resource-and-specify-a-statuscallbackevent&code-sdk-version=json). If an `application_sid` is present, this parameter is ignored.
        :param status_callback_method: The HTTP method we should use when calling the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param send_digits: The string of keys to dial after connecting to the number, with a maximum length of 32 digits. Valid digits in the string include any digit (`0`-`9`), '`A`', '`B`', '`C`', '`D`', '`#`', and '`*`'. You can also use '`w`' to insert a half-second pause and '`W`' to insert a one-second pause. For example, to pause for one second after connecting and then dial extension 1234 followed by the # key, set this parameter to `W1234#`. Be sure to URL-encode this string because the '`#`' character has special meaning in a URL. If both `SendDigits` and `MachineDetection` parameters are provided, then `MachineDetection` will be ignored.
        :param timeout: The integer number of seconds that we should allow the phone to ring before assuming there is no answer. The default is `60` seconds and the maximum is `600` seconds. For some call flows, we will add a 5-second buffer to the timeout value you provide. For this reason, a timeout value of 10 seconds could result in an actual timeout closer to 15 seconds. You can set this to a short time, such as `15` seconds, to hang up before reaching an answering machine or voicemail.
        :param record: Whether to record the call. Can be `true` to record the phone call, or `false` to not. The default is `false`. The `recording_url` is sent to the `status_callback` URL.
        :param recording_channels: The number of channels in the final recording. Can be: `mono` or `dual`. The default is `mono`. `mono` records both legs of the call in a single channel of the recording file. `dual` records each leg to a separate channel of the recording file. The first channel of a dual-channel recording contains the parent call and the second channel contains the child call.
        :param recording_status_callback: The URL that we call when the recording is available to be accessed.
        :param recording_status_callback_method: The HTTP method we should use when calling the `recording_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param sip_auth_username: The username used to authenticate the caller making a SIP call.
        :param sip_auth_password: The password required to authenticate the user account specified in `sip_auth_username`.
        :param machine_detection: Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. If `send_digits` is provided, this parameter is ignored. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
        :param machine_detection_timeout: The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
        :param recording_status_callback_event: The recording status events that will trigger calls to the URL specified in `recording_status_callback`. Can be: `in-progress`, `completed` and `absent`. Defaults to `completed`. Separate  multiple values with a space.
        :param trim: Whether to trim any leading and trailing silence from the recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
        :param caller_id: The phone number, SIP address, or Client identifier that made this call. Phone numbers are in [E.164 format](https://wwnw.twilio.com/docs/glossary/what-e164) (e.g., +***********). SIP addresses are formatted as `<EMAIL>`.
        :param machine_detection_speech_threshold: The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
        :param machine_detection_speech_end_threshold: The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
        :param machine_detection_silence_timeout: The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
        :param async_amd: Select whether to perform answering machine detection in the background. Default, blocks the execution of the call until Answering Machine Detection is completed. Can be: `true` or `false`.
        :param async_amd_status_callback: The URL that we should call using the `async_amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
        :param async_amd_status_callback_method: The HTTP method we should use when calling the `async_amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param byoc: The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
        :param call_reason: The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
        :param call_token: A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
        :param recording_track: The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is generated from Twilio. `both` records the audio that is received and generated by Twilio.
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.
        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url parameter. If both `twiml` and `url` are provided then `twiml` parameter will be ignored. Max 4000 characters.
        :param application_sid: The SID of the Application resource that will handle the call, if the call will be handled by an application.

        :returns: The created CallInstance
        """

        data = values.of(
            {
                "To": to,
                "From": from_,
                "Method": method,
                "FallbackUrl": fallback_url,
                "FallbackMethod": fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "StatusCallbackMethod": status_callback_method,
                "SendDigits": send_digits,
                "Timeout": timeout,
                "Record": serialize.boolean_to_string(record),
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "MachineDetection": machine_detection,
                "MachineDetectionTimeout": machine_detection_timeout,
                "RecordingStatusCallbackEvent": serialize.map(
                    recording_status_callback_event, lambda e: e
                ),
                "Trim": trim,
                "CallerId": caller_id,
                "MachineDetectionSpeechThreshold": machine_detection_speech_threshold,
                "MachineDetectionSpeechEndThreshold": machine_detection_speech_end_threshold,
                "MachineDetectionSilenceTimeout": machine_detection_silence_timeout,
                "AsyncAmd": async_amd,
                "AsyncAmdStatusCallback": async_amd_status_callback,
                "AsyncAmdStatusCallbackMethod": async_amd_status_callback_method,
                "Byoc": byoc,
                "CallReason": call_reason,
                "CallToken": call_token,
                "RecordingTrack": recording_track,
                "TimeLimit": time_limit,
                "Url": url,
                "Twiml": twiml,
                "ApplicationSid": application_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CallInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    async def create_async(
        self,
        to: str,
        from_: str,
        method: Union[str, object] = values.unset,
        fallback_url: Union[str, object] = values.unset,
        fallback_method: Union[str, object] = values.unset,
        status_callback: Union[str, object] = values.unset,
        status_callback_event: Union[List[str], object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        send_digits: Union[str, object] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        machine_detection: Union[str, object] = values.unset,
        machine_detection_timeout: Union[int, object] = values.unset,
        recording_status_callback_event: Union[List[str], object] = values.unset,
        trim: Union[str, object] = values.unset,
        caller_id: Union[str, object] = values.unset,
        machine_detection_speech_threshold: Union[int, object] = values.unset,
        machine_detection_speech_end_threshold: Union[int, object] = values.unset,
        machine_detection_silence_timeout: Union[int, object] = values.unset,
        async_amd: Union[str, object] = values.unset,
        async_amd_status_callback: Union[str, object] = values.unset,
        async_amd_status_callback_method: Union[str, object] = values.unset,
        byoc: Union[str, object] = values.unset,
        call_reason: Union[str, object] = values.unset,
        call_token: Union[str, object] = values.unset,
        recording_track: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
        url: Union[str, object] = values.unset,
        twiml: Union[str, object] = values.unset,
        application_sid: Union[str, object] = values.unset,
    ) -> CallInstance:
        """
        Asynchronously create the CallInstance

        :param to: The phone number, SIP address, or client identifier to call.
        :param from_: The phone number or client identifier to use as the caller id. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `From` must also be a phone number.
        :param method: The HTTP method we should use when calling the `url` parameter's value. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_url: The URL that we call using the `fallback_method` if an error occurs when requesting or executing the TwiML at `url`. If an `application_sid` parameter is present, this parameter is ignored.
        :param fallback_method: The HTTP method that we should use to request the `fallback_url`. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application. If no `status_callback_event` is specified, we will send the `completed` status. If an `application_sid` parameter is present, this parameter is ignored. URLs must contain a valid hostname (underscores are not permitted).
        :param status_callback_event: The call progress events that we will send to the `status_callback` URL. Can be: `initiated`, `ringing`, `answered`, and `completed`. If no event is specified, we send the `completed` status. If you want to receive multiple events, specify each one in a separate `status_callback_event` parameter. See the code sample for [monitoring call progress](https://www.twilio.com/docs/voice/api/call-resource?code-sample=code-create-a-call-resource-and-specify-a-statuscallbackevent&code-sdk-version=json). If an `application_sid` is present, this parameter is ignored.
        :param status_callback_method: The HTTP method we should use when calling the `status_callback` URL. Can be: `GET` or `POST` and the default is `POST`. If an `application_sid` parameter is present, this parameter is ignored.
        :param send_digits: The string of keys to dial after connecting to the number, with a maximum length of 32 digits. Valid digits in the string include any digit (`0`-`9`), '`A`', '`B`', '`C`', '`D`', '`#`', and '`*`'. You can also use '`w`' to insert a half-second pause and '`W`' to insert a one-second pause. For example, to pause for one second after connecting and then dial extension 1234 followed by the # key, set this parameter to `W1234#`. Be sure to URL-encode this string because the '`#`' character has special meaning in a URL. If both `SendDigits` and `MachineDetection` parameters are provided, then `MachineDetection` will be ignored.
        :param timeout: The integer number of seconds that we should allow the phone to ring before assuming there is no answer. The default is `60` seconds and the maximum is `600` seconds. For some call flows, we will add a 5-second buffer to the timeout value you provide. For this reason, a timeout value of 10 seconds could result in an actual timeout closer to 15 seconds. You can set this to a short time, such as `15` seconds, to hang up before reaching an answering machine or voicemail.
        :param record: Whether to record the call. Can be `true` to record the phone call, or `false` to not. The default is `false`. The `recording_url` is sent to the `status_callback` URL.
        :param recording_channels: The number of channels in the final recording. Can be: `mono` or `dual`. The default is `mono`. `mono` records both legs of the call in a single channel of the recording file. `dual` records each leg to a separate channel of the recording file. The first channel of a dual-channel recording contains the parent call and the second channel contains the child call.
        :param recording_status_callback: The URL that we call when the recording is available to be accessed.
        :param recording_status_callback_method: The HTTP method we should use when calling the `recording_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param sip_auth_username: The username used to authenticate the caller making a SIP call.
        :param sip_auth_password: The password required to authenticate the user account specified in `sip_auth_username`.
        :param machine_detection: Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. If `send_digits` is provided, this parameter is ignored. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
        :param machine_detection_timeout: The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
        :param recording_status_callback_event: The recording status events that will trigger calls to the URL specified in `recording_status_callback`. Can be: `in-progress`, `completed` and `absent`. Defaults to `completed`. Separate  multiple values with a space.
        :param trim: Whether to trim any leading and trailing silence from the recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
        :param caller_id: The phone number, SIP address, or Client identifier that made this call. Phone numbers are in [E.164 format](https://wwnw.twilio.com/docs/glossary/what-e164) (e.g., +***********). SIP addresses are formatted as `<EMAIL>`.
        :param machine_detection_speech_threshold: The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
        :param machine_detection_speech_end_threshold: The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
        :param machine_detection_silence_timeout: The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
        :param async_amd: Select whether to perform answering machine detection in the background. Default, blocks the execution of the call until Answering Machine Detection is completed. Can be: `true` or `false`.
        :param async_amd_status_callback: The URL that we should call using the `async_amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
        :param async_amd_status_callback_method: The HTTP method we should use when calling the `async_amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param byoc: The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
        :param call_reason: The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
        :param call_token: A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
        :param recording_track: The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is generated from Twilio. `both` records the audio that is received and generated by Twilio.
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.
        :param url: The absolute URL that returns the TwiML instructions for the call. We will call this URL using the `method` when the call connects. For more information, see the [Url Parameter](https://www.twilio.com/docs/voice/make-calls#specify-a-url-parameter) section in [Making Calls](https://www.twilio.com/docs/voice/make-calls).
        :param twiml: TwiML instructions for the call Twilio will use without fetching Twiml from url parameter. If both `twiml` and `url` are provided then `twiml` parameter will be ignored. Max 4000 characters.
        :param application_sid: The SID of the Application resource that will handle the call, if the call will be handled by an application.

        :returns: The created CallInstance
        """

        data = values.of(
            {
                "To": to,
                "From": from_,
                "Method": method,
                "FallbackUrl": fallback_url,
                "FallbackMethod": fallback_method,
                "StatusCallback": status_callback,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "StatusCallbackMethod": status_callback_method,
                "SendDigits": send_digits,
                "Timeout": timeout,
                "Record": serialize.boolean_to_string(record),
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "MachineDetection": machine_detection,
                "MachineDetectionTimeout": machine_detection_timeout,
                "RecordingStatusCallbackEvent": serialize.map(
                    recording_status_callback_event, lambda e: e
                ),
                "Trim": trim,
                "CallerId": caller_id,
                "MachineDetectionSpeechThreshold": machine_detection_speech_threshold,
                "MachineDetectionSpeechEndThreshold": machine_detection_speech_end_threshold,
                "MachineDetectionSilenceTimeout": machine_detection_silence_timeout,
                "AsyncAmd": async_amd,
                "AsyncAmdStatusCallback": async_amd_status_callback,
                "AsyncAmdStatusCallbackMethod": async_amd_status_callback_method,
                "Byoc": byoc,
                "CallReason": call_reason,
                "CallToken": call_token,
                "RecordingTrack": recording_track,
                "TimeLimit": time_limit,
                "Url": url,
                "Twiml": twiml,
                "ApplicationSid": application_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return CallInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def stream(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[CallInstance]:
        """
        Streams CallInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param str from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param str parent_call_sid: Only include calls spawned by calls with this SID.
        :param &quot;CallInstance.Status&quot; status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param datetime start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            to=to,
            from_=from_,
            parent_call_sid=parent_call_sid,
            status=status,
            start_time=start_time,
            start_time_before=start_time_before,
            start_time_after=start_time_after,
            end_time=end_time,
            end_time_before=end_time_before,
            end_time_after=end_time_after,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[CallInstance]:
        """
        Asynchronously streams CallInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param str from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param str parent_call_sid: Only include calls spawned by calls with this SID.
        :param &quot;CallInstance.Status&quot; status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param datetime start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            to=to,
            from_=from_,
            parent_call_sid=parent_call_sid,
            status=status,
            start_time=start_time,
            start_time_before=start_time_before,
            start_time_after=start_time_after,
            end_time=end_time,
            end_time_before=end_time_before,
            end_time_after=end_time_after,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CallInstance]:
        """
        Lists CallInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param str from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param str parent_call_sid: Only include calls spawned by calls with this SID.
        :param &quot;CallInstance.Status&quot; status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param datetime start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                to=to,
                from_=from_,
                parent_call_sid=parent_call_sid,
                status=status,
                start_time=start_time,
                start_time_before=start_time_before,
                start_time_after=start_time_after,
                end_time=end_time,
                end_time_before=end_time_before,
                end_time_after=end_time_after,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CallInstance]:
        """
        Asynchronously lists CallInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param str from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param str parent_call_sid: Only include calls spawned by calls with this SID.
        :param &quot;CallInstance.Status&quot; status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param datetime start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param datetime end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param datetime end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                to=to,
                from_=from_,
                parent_call_sid=parent_call_sid,
                status=status,
                start_time=start_time,
                start_time_before=start_time_before,
                start_time_after=start_time_after,
                end_time=end_time,
                end_time_before=end_time_before,
                end_time_after=end_time_after,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CallPage:
        """
        Retrieve a single page of CallInstance records from the API.
        Request is executed immediately

        :param to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param parent_call_sid: Only include calls spawned by calls with this SID.
        :param status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CallInstance
        """
        data = values.of(
            {
                "To": to,
                "From": from_,
                "ParentCallSid": parent_call_sid,
                "Status": status,
                "StartTime": serialize.iso8601_datetime(start_time),
                "StartTime<": serialize.iso8601_datetime(start_time_before),
                "StartTime>": serialize.iso8601_datetime(start_time_after),
                "EndTime": serialize.iso8601_datetime(end_time),
                "EndTime<": serialize.iso8601_datetime(end_time_before),
                "EndTime>": serialize.iso8601_datetime(end_time_after),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CallPage(self._version, response, self._solution)

    async def page_async(
        self,
        to: Union[str, object] = values.unset,
        from_: Union[str, object] = values.unset,
        parent_call_sid: Union[str, object] = values.unset,
        status: Union["CallInstance.Status", object] = values.unset,
        start_time: Union[datetime, object] = values.unset,
        start_time_before: Union[datetime, object] = values.unset,
        start_time_after: Union[datetime, object] = values.unset,
        end_time: Union[datetime, object] = values.unset,
        end_time_before: Union[datetime, object] = values.unset,
        end_time_after: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CallPage:
        """
        Asynchronously retrieve a single page of CallInstance records from the API.
        Request is executed immediately

        :param to: Only show calls made to this phone number, SIP address, Client identifier or SIM SID.
        :param from_: Only include calls from this phone number, SIP address, Client identifier or SIM SID.
        :param parent_call_sid: Only include calls spawned by calls with this SID.
        :param status: The status of the calls to include. Can be: `queued`, `ringing`, `in-progress`, `canceled`, `completed`, `failed`, `busy`, or `no-answer`.
        :param start_time: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param start_time_before: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param start_time_after: Only include calls that started on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that started on this date. You can also specify an inequality, such as `StartTime<=YYYY-MM-DD`, to read calls that started on or before midnight of this date, and `StartTime>=YYYY-MM-DD` to read calls that started on or after midnight of this date.
        :param end_time: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param end_time_before: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param end_time_after: Only include calls that ended on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only calls that ended on this date. You can also specify an inequality, such as `EndTime<=YYYY-MM-DD`, to read calls that ended on or before midnight of this date, and `EndTime>=YYYY-MM-DD` to read calls that ended on or after midnight of this date.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CallInstance
        """
        data = values.of(
            {
                "To": to,
                "From": from_,
                "ParentCallSid": parent_call_sid,
                "Status": status,
                "StartTime": serialize.iso8601_datetime(start_time),
                "StartTime<": serialize.iso8601_datetime(start_time_before),
                "StartTime>": serialize.iso8601_datetime(start_time_after),
                "EndTime": serialize.iso8601_datetime(end_time),
                "EndTime<": serialize.iso8601_datetime(end_time_before),
                "EndTime>": serialize.iso8601_datetime(end_time_after),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CallPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> CallPage:
        """
        Retrieve a specific page of CallInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CallInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return CallPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> CallPage:
        """
        Asynchronously retrieve a specific page of CallInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CallInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return CallPage(self._version, response, self._solution)

    def get(self, sid: str) -> CallContext:
        """
        Constructs a CallContext

        :param sid: The Twilio-provided string that uniquely identifies the Call resource to update
        """
        return CallContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __call__(self, sid: str) -> CallContext:
        """
        Constructs a CallContext

        :param sid: The Twilio-provided string that uniquely identifies the Call resource to update
        """
        return CallContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.CallList>"
