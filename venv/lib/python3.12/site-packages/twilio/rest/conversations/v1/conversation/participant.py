r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Conversations
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ParticipantInstance(InstanceResource):

    class WebhookEnabledType(object):
        TRUE = "true"
        FALSE = "false"

    """
    :ivar account_sid: The unique ID of the [Account](https://www.twilio.com/docs/iam/api/account) responsible for this participant.
    :ivar conversation_sid: The unique ID of the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for this participant.
    :ivar sid: A 34 character string that uniquely identifies this resource.
    :ivar identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
    :ivar attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \"{}\" will be returned.
    :ivar messaging_binding: Information about how this participant exchanges messages with the conversation. A JSON parameter consisting of type and address fields of the participant.
    :ivar role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.
    :ivar date_created: The date that this resource was created.
    :ivar date_updated: The date that this resource was last updated.
    :ivar url: An absolute API resource URL for this participant.
    :ivar last_read_message_index: Index of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
    :ivar last_read_timestamp: Timestamp of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        conversation_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.conversation_sid: Optional[str] = payload.get("conversation_sid")
        self.sid: Optional[str] = payload.get("sid")
        self.identity: Optional[str] = payload.get("identity")
        self.attributes: Optional[str] = payload.get("attributes")
        self.messaging_binding: Optional[Dict[str, object]] = payload.get(
            "messaging_binding"
        )
        self.role_sid: Optional[str] = payload.get("role_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")
        self.last_read_message_index: Optional[int] = deserialize.integer(
            payload.get("last_read_message_index")
        )
        self.last_read_timestamp: Optional[str] = payload.get("last_read_timestamp")

        self._solution = {
            "conversation_sid": conversation_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[ParticipantContext] = None

    @property
    def _proxy(self) -> "ParticipantContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ParticipantContext for this ParticipantInstance
        """
        if self._context is None:
            self._context = ParticipantContext(
                self._version,
                conversation_sid=self._solution["conversation_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
    ) -> bool:
        """
        Deletes the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete(
            x_twilio_webhook_enabled=x_twilio_webhook_enabled,
        )

    async def delete_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
    ) -> bool:
        """
        Asynchronous coroutine that deletes the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async(
            x_twilio_webhook_enabled=x_twilio_webhook_enabled,
        )

    def fetch(self) -> "ParticipantInstance":
        """
        Fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ParticipantInstance":
        """
        Asynchronous coroutine to fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        identity: Union[str, object] = values.unset,
        last_read_message_index: Union[int, object] = values.unset,
        last_read_timestamp: Union[str, object] = values.unset,
    ) -> "ParticipantInstance":
        """
        Update the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.
        :param messaging_binding_proxy_address: The address of the Twilio phone number that the participant is in contact with. 'null' value will remove it.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. 'null' value will remove it.
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param last_read_message_index: Index of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
        :param last_read_timestamp: Timestamp of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.

        :returns: The updated ParticipantInstance
        """
        return self._proxy.update(
            x_twilio_webhook_enabled=x_twilio_webhook_enabled,
            date_created=date_created,
            date_updated=date_updated,
            attributes=attributes,
            role_sid=role_sid,
            messaging_binding_proxy_address=messaging_binding_proxy_address,
            messaging_binding_projected_address=messaging_binding_projected_address,
            identity=identity,
            last_read_message_index=last_read_message_index,
            last_read_timestamp=last_read_timestamp,
        )

    async def update_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        identity: Union[str, object] = values.unset,
        last_read_message_index: Union[int, object] = values.unset,
        last_read_timestamp: Union[str, object] = values.unset,
    ) -> "ParticipantInstance":
        """
        Asynchronous coroutine to update the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.
        :param messaging_binding_proxy_address: The address of the Twilio phone number that the participant is in contact with. 'null' value will remove it.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. 'null' value will remove it.
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param last_read_message_index: Index of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
        :param last_read_timestamp: Timestamp of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.

        :returns: The updated ParticipantInstance
        """
        return await self._proxy.update_async(
            x_twilio_webhook_enabled=x_twilio_webhook_enabled,
            date_created=date_created,
            date_updated=date_updated,
            attributes=attributes,
            role_sid=role_sid,
            messaging_binding_proxy_address=messaging_binding_proxy_address,
            messaging_binding_projected_address=messaging_binding_projected_address,
            identity=identity,
            last_read_message_index=last_read_message_index,
            last_read_timestamp=last_read_timestamp,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Conversations.V1.ParticipantInstance {}>".format(context)


class ParticipantContext(InstanceContext):

    def __init__(self, version: Version, conversation_sid: str, sid: str):
        """
        Initialize the ParticipantContext

        :param version: Version that contains the resource
        :param conversation_sid: The unique ID of the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for this participant.
        :param sid: A 34 character string that uniquely identifies this resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "conversation_sid": conversation_sid,
            "sid": sid,
        }
        self._uri = "/Conversations/{conversation_sid}/Participants/{sid}".format(
            **self._solution
        )

    def delete(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
    ) -> bool:
        """
        Deletes the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
            }
        )

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
    ) -> bool:
        """
        Asynchronous coroutine that deletes the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header

        :returns: True if delete succeeds, False otherwise
        """
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
            }
        )

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ParticipantInstance:
        """
        Fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ParticipantInstance(
            self._version,
            payload,
            conversation_sid=self._solution["conversation_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ParticipantInstance:
        """
        Asynchronous coroutine to fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            conversation_sid=self._solution["conversation_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        identity: Union[str, object] = values.unset,
        last_read_message_index: Union[int, object] = values.unset,
        last_read_timestamp: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Update the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.
        :param messaging_binding_proxy_address: The address of the Twilio phone number that the participant is in contact with. 'null' value will remove it.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. 'null' value will remove it.
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param last_read_message_index: Index of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
        :param last_read_timestamp: Timestamp of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.

        :returns: The updated ParticipantInstance
        """

        data = values.of(
            {
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "Attributes": attributes,
                "RoleSid": role_sid,
                "MessagingBinding.ProxyAddress": messaging_binding_proxy_address,
                "MessagingBinding.ProjectedAddress": messaging_binding_projected_address,
                "Identity": identity,
                "LastReadMessageIndex": last_read_message_index,
                "LastReadTimestamp": last_read_timestamp,
            }
        )
        headers = values.of({})

        if not (
            x_twilio_webhook_enabled is values.unset
            or (
                isinstance(x_twilio_webhook_enabled, str)
                and not x_twilio_webhook_enabled
            )
        ):
            headers["X-Twilio-Webhook-Enabled"] = x_twilio_webhook_enabled

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            conversation_sid=self._solution["conversation_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        identity: Union[str, object] = values.unset,
        last_read_message_index: Union[int, object] = values.unset,
        last_read_timestamp: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Asynchronous coroutine to update the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.
        :param messaging_binding_proxy_address: The address of the Twilio phone number that the participant is in contact with. 'null' value will remove it.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. 'null' value will remove it.
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param last_read_message_index: Index of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.
        :param last_read_timestamp: Timestamp of last “read” message in the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for the Participant.

        :returns: The updated ParticipantInstance
        """

        data = values.of(
            {
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "Attributes": attributes,
                "RoleSid": role_sid,
                "MessagingBinding.ProxyAddress": messaging_binding_proxy_address,
                "MessagingBinding.ProjectedAddress": messaging_binding_projected_address,
                "Identity": identity,
                "LastReadMessageIndex": last_read_message_index,
                "LastReadTimestamp": last_read_timestamp,
            }
        )
        headers = values.of({})

        if not (
            x_twilio_webhook_enabled is values.unset
            or (
                isinstance(x_twilio_webhook_enabled, str)
                and not x_twilio_webhook_enabled
            )
        ):
            headers["X-Twilio-Webhook-Enabled"] = x_twilio_webhook_enabled

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            conversation_sid=self._solution["conversation_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Conversations.V1.ParticipantContext {}>".format(context)


class ParticipantPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ParticipantInstance:
        """
        Build an instance of ParticipantInstance

        :param payload: Payload response from the API
        """
        return ParticipantInstance(
            self._version, payload, conversation_sid=self._solution["conversation_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Conversations.V1.ParticipantPage>"


class ParticipantList(ListResource):

    def __init__(self, version: Version, conversation_sid: str):
        """
        Initialize the ParticipantList

        :param version: Version that contains the resource
        :param conversation_sid: The unique ID of the [Conversation](https://www.twilio.com/docs/conversations/api/conversation-resource) for participants.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "conversation_sid": conversation_sid,
        }
        self._uri = "/Conversations/{conversation_sid}/Participants".format(
            **self._solution
        )

    def create(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        identity: Union[str, object] = values.unset,
        messaging_binding_address: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Create the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param messaging_binding_address: The address of the participant's device, e.g. a phone or WhatsApp number. Together with the Proxy address, this determines a participant uniquely. This field (with proxy_address) is only null when the participant is interacting from an SDK endpoint (see the 'identity' field).
        :param messaging_binding_proxy_address: The address of the Twilio phone number (or WhatsApp number) that the participant is in contact with. This field, together with participant address, is only null when the participant is interacting from an SDK endpoint (see the 'identity' field).
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. Communication mask for the Conversation participant with Identity.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.

        :returns: The created ParticipantInstance
        """

        data = values.of(
            {
                "Identity": identity,
                "MessagingBinding.Address": messaging_binding_address,
                "MessagingBinding.ProxyAddress": messaging_binding_proxy_address,
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "Attributes": attributes,
                "MessagingBinding.ProjectedAddress": messaging_binding_projected_address,
                "RoleSid": role_sid,
            }
        )
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version, payload, conversation_sid=self._solution["conversation_sid"]
        )

    async def create_async(
        self,
        x_twilio_webhook_enabled: Union[
            "ParticipantInstance.WebhookEnabledType", object
        ] = values.unset,
        identity: Union[str, object] = values.unset,
        messaging_binding_address: Union[str, object] = values.unset,
        messaging_binding_proxy_address: Union[str, object] = values.unset,
        date_created: Union[datetime, object] = values.unset,
        date_updated: Union[datetime, object] = values.unset,
        attributes: Union[str, object] = values.unset,
        messaging_binding_projected_address: Union[str, object] = values.unset,
        role_sid: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Asynchronously create the ParticipantInstance

        :param x_twilio_webhook_enabled: The X-Twilio-Webhook-Enabled HTTP request header
        :param identity: A unique string identifier for the conversation participant as [Conversation User](https://www.twilio.com/docs/conversations/api/user-resource). This parameter is non-null if (and only if) the participant is using the Conversations SDK to communicate. Limited to 256 characters.
        :param messaging_binding_address: The address of the participant's device, e.g. a phone or WhatsApp number. Together with the Proxy address, this determines a participant uniquely. This field (with proxy_address) is only null when the participant is interacting from an SDK endpoint (see the 'identity' field).
        :param messaging_binding_proxy_address: The address of the Twilio phone number (or WhatsApp number) that the participant is in contact with. This field, together with participant address, is only null when the participant is interacting from an SDK endpoint (see the 'identity' field).
        :param date_created: The date that this resource was created.
        :param date_updated: The date that this resource was last updated.
        :param attributes: An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
        :param messaging_binding_projected_address: The address of the Twilio phone number that is used in Group MMS. Communication mask for the Conversation participant with Identity.
        :param role_sid: The SID of a conversation-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the participant.

        :returns: The created ParticipantInstance
        """

        data = values.of(
            {
                "Identity": identity,
                "MessagingBinding.Address": messaging_binding_address,
                "MessagingBinding.ProxyAddress": messaging_binding_proxy_address,
                "DateCreated": serialize.iso8601_datetime(date_created),
                "DateUpdated": serialize.iso8601_datetime(date_updated),
                "Attributes": attributes,
                "MessagingBinding.ProjectedAddress": messaging_binding_projected_address,
                "RoleSid": role_sid,
            }
        )
        headers = values.of(
            {
                "X-Twilio-Webhook-Enabled": x_twilio_webhook_enabled,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version, payload, conversation_sid=self._solution["conversation_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ParticipantInstance]:
        """
        Streams ParticipantInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ParticipantInstance]:
        """
        Asynchronously streams ParticipantInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ParticipantInstance]:
        """
        Lists ParticipantInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ParticipantInstance]:
        """
        Asynchronously lists ParticipantInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ParticipantPage:
        """
        Retrieve a single page of ParticipantInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ParticipantInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ParticipantPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ParticipantPage:
        """
        Asynchronously retrieve a single page of ParticipantInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ParticipantInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ParticipantPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ParticipantPage:
        """
        Retrieve a specific page of ParticipantInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ParticipantInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ParticipantPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ParticipantPage:
        """
        Asynchronously retrieve a specific page of ParticipantInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ParticipantInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ParticipantPage(self._version, response, self._solution)

    def get(self, sid: str) -> ParticipantContext:
        """
        Constructs a ParticipantContext

        :param sid: A 34 character string that uniquely identifies this resource.
        """
        return ParticipantContext(
            self._version, conversation_sid=self._solution["conversation_sid"], sid=sid
        )

    def __call__(self, sid: str) -> ParticipantContext:
        """
        Constructs a ParticipantContext

        :param sid: A 34 character string that uniquely identifies this resource.
        """
        return ParticipantContext(
            self._version, conversation_sid=self._solution["conversation_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Conversations.V1.ParticipantList>"
