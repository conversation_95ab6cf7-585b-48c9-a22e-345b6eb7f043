r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class BundleCloneInstance(InstanceResource):

    class Status(object):
        DRAFT = "draft"
        PENDING_REVIEW = "pending-review"
        IN_REVIEW = "in-review"
        TWILIO_REJECTED = "twilio-rejected"
        TWILIO_APPROVED = "twilio-approved"
        PROVISIONALLY_APPROVED = "provisionally-approved"

    """
    :ivar bundle_sid: The unique string that we created to identify the Bundle resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Bundle resource.
    :ivar regulation_sid: The unique string of a regulation that is associated to the Bundle resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar status: 
    :ivar valid_until: The date and time in GMT in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format when the resource will be valid until.
    :ivar email: The email address that will receive updates when the Bundle resource changes status.
    :ivar status_callback: The URL we call to inform your application of status changes.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The URL of this resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        bundle_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.bundle_sid: Optional[str] = payload.get("bundle_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.regulation_sid: Optional[str] = payload.get("regulation_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.status: Optional["BundleCloneInstance.Status"] = payload.get("status")
        self.valid_until: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("valid_until")
        )
        self.email: Optional[str] = payload.get("email")
        self.status_callback: Optional[str] = payload.get("status_callback")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "bundle_sid": bundle_sid or self.bundle_sid,
        }
        self._context: Optional[BundleCloneContext] = None

    @property
    def _proxy(self) -> "BundleCloneContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: BundleCloneContext for this BundleCloneInstance
        """
        if self._context is None:
            self._context = BundleCloneContext(
                self._version,
                bundle_sid=self._solution["bundle_sid"],
            )
        return self._context

    def create(
        self,
        target_account_sid: str,
        move_to_draft: Union[bool, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> "BundleCloneInstance":
        """
        Create the BundleCloneInstance

        :param target_account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) where the bundle needs to be cloned.
        :param move_to_draft: If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
        :param friendly_name: The string that you assigned to describe the cloned bundle.

        :returns: The created BundleCloneInstance
        """
        return self._proxy.create(
            target_account_sid,
            move_to_draft=move_to_draft,
            friendly_name=friendly_name,
        )

    async def create_async(
        self,
        target_account_sid: str,
        move_to_draft: Union[bool, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> "BundleCloneInstance":
        """
        Asynchronous coroutine to create the BundleCloneInstance

        :param target_account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) where the bundle needs to be cloned.
        :param move_to_draft: If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
        :param friendly_name: The string that you assigned to describe the cloned bundle.

        :returns: The created BundleCloneInstance
        """
        return await self._proxy.create_async(
            target_account_sid,
            move_to_draft=move_to_draft,
            friendly_name=friendly_name,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.BundleCloneInstance {}>".format(context)


class BundleCloneContext(InstanceContext):

    def __init__(self, version: Version, bundle_sid: str):
        """
        Initialize the BundleCloneContext

        :param version: Version that contains the resource
        :param bundle_sid: The unique string that identifies the Bundle to be cloned.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "bundle_sid": bundle_sid,
        }
        self._uri = "/RegulatoryCompliance/Bundles/{bundle_sid}/Clones".format(
            **self._solution
        )

    def create(
        self,
        target_account_sid: str,
        move_to_draft: Union[bool, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> BundleCloneInstance:
        """
        Create the BundleCloneInstance

        :param target_account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) where the bundle needs to be cloned.
        :param move_to_draft: If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
        :param friendly_name: The string that you assigned to describe the cloned bundle.

        :returns: The created BundleCloneInstance
        """
        data = values.of(
            {
                "TargetAccountSid": target_account_sid,
                "MoveToDraft": serialize.boolean_to_string(move_to_draft),
                "FriendlyName": friendly_name,
            }
        )

        payload = self._version.create(method="POST", uri=self._uri, data=data)

        return BundleCloneInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    async def create_async(
        self,
        target_account_sid: str,
        move_to_draft: Union[bool, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
    ) -> BundleCloneInstance:
        """
        Asynchronous coroutine to create the BundleCloneInstance

        :param target_account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) where the bundle needs to be cloned.
        :param move_to_draft: If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
        :param friendly_name: The string that you assigned to describe the cloned bundle.

        :returns: The created BundleCloneInstance
        """
        data = values.of(
            {
                "TargetAccountSid": target_account_sid,
                "MoveToDraft": serialize.boolean_to_string(move_to_draft),
                "FriendlyName": friendly_name,
            }
        )

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data
        )

        return BundleCloneInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.BundleCloneContext {}>".format(context)


class BundleCloneList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the BundleCloneList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, bundle_sid: str) -> BundleCloneContext:
        """
        Constructs a BundleCloneContext

        :param bundle_sid: The unique string that identifies the Bundle to be cloned.
        """
        return BundleCloneContext(self._version, bundle_sid=bundle_sid)

    def __call__(self, bundle_sid: str) -> BundleCloneContext:
        """
        Constructs a BundleCloneContext

        :param bundle_sid: The unique string that identifies the Bundle to be cloned.
        """
        return BundleCloneContext(self._version, bundle_sid=bundle_sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.BundleCloneList>"
