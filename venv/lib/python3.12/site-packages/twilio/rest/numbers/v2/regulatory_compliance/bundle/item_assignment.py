r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ItemAssignmentInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Item Assignment resource.
    :ivar bundle_sid: The unique string that we created to identify the Bundle resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Item Assignment resource.
    :ivar object_sid: The SID of an object bag that holds information of the different items.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Identity resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        bundle_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.bundle_sid: Optional[str] = payload.get("bundle_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.object_sid: Optional[str] = payload.get("object_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "bundle_sid": bundle_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[ItemAssignmentContext] = None

    @property
    def _proxy(self) -> "ItemAssignmentContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ItemAssignmentContext for this ItemAssignmentInstance
        """
        if self._context is None:
            self._context = ItemAssignmentContext(
                self._version,
                bundle_sid=self._solution["bundle_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ItemAssignmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ItemAssignmentInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ItemAssignmentInstance":
        """
        Fetch the ItemAssignmentInstance


        :returns: The fetched ItemAssignmentInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ItemAssignmentInstance":
        """
        Asynchronous coroutine to fetch the ItemAssignmentInstance


        :returns: The fetched ItemAssignmentInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.ItemAssignmentInstance {}>".format(context)


class ItemAssignmentContext(InstanceContext):

    def __init__(self, version: Version, bundle_sid: str, sid: str):
        """
        Initialize the ItemAssignmentContext

        :param version: Version that contains the resource
        :param bundle_sid: The unique string that we created to identify the Bundle resource.
        :param sid: The unique string that we created to identify the Identity resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "bundle_sid": bundle_sid,
            "sid": sid,
        }
        self._uri = (
            "/RegulatoryCompliance/Bundles/{bundle_sid}/ItemAssignments/{sid}".format(
                **self._solution
            )
        )

    def delete(self) -> bool:
        """
        Deletes the ItemAssignmentInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ItemAssignmentInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ItemAssignmentInstance:
        """
        Fetch the ItemAssignmentInstance


        :returns: The fetched ItemAssignmentInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ItemAssignmentInstance(
            self._version,
            payload,
            bundle_sid=self._solution["bundle_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ItemAssignmentInstance:
        """
        Asynchronous coroutine to fetch the ItemAssignmentInstance


        :returns: The fetched ItemAssignmentInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ItemAssignmentInstance(
            self._version,
            payload,
            bundle_sid=self._solution["bundle_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.ItemAssignmentContext {}>".format(context)


class ItemAssignmentPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ItemAssignmentInstance:
        """
        Build an instance of ItemAssignmentInstance

        :param payload: Payload response from the API
        """
        return ItemAssignmentInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.ItemAssignmentPage>"


class ItemAssignmentList(ListResource):

    def __init__(self, version: Version, bundle_sid: str):
        """
        Initialize the ItemAssignmentList

        :param version: Version that contains the resource
        :param bundle_sid: The unique string that we created to identify the Bundle resource.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "bundle_sid": bundle_sid,
        }
        self._uri = "/RegulatoryCompliance/Bundles/{bundle_sid}/ItemAssignments".format(
            **self._solution
        )

    def create(self, object_sid: str) -> ItemAssignmentInstance:
        """
        Create the ItemAssignmentInstance

        :param object_sid: The SID of an object bag that holds information of the different items.

        :returns: The created ItemAssignmentInstance
        """

        data = values.of(
            {
                "ObjectSid": object_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ItemAssignmentInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    async def create_async(self, object_sid: str) -> ItemAssignmentInstance:
        """
        Asynchronously create the ItemAssignmentInstance

        :param object_sid: The SID of an object bag that holds information of the different items.

        :returns: The created ItemAssignmentInstance
        """

        data = values.of(
            {
                "ObjectSid": object_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ItemAssignmentInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ItemAssignmentInstance]:
        """
        Streams ItemAssignmentInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ItemAssignmentInstance]:
        """
        Asynchronously streams ItemAssignmentInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ItemAssignmentInstance]:
        """
        Lists ItemAssignmentInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ItemAssignmentInstance]:
        """
        Asynchronously lists ItemAssignmentInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ItemAssignmentPage:
        """
        Retrieve a single page of ItemAssignmentInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ItemAssignmentInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ItemAssignmentPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ItemAssignmentPage:
        """
        Asynchronously retrieve a single page of ItemAssignmentInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ItemAssignmentInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ItemAssignmentPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ItemAssignmentPage:
        """
        Retrieve a specific page of ItemAssignmentInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ItemAssignmentInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ItemAssignmentPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ItemAssignmentPage:
        """
        Asynchronously retrieve a specific page of ItemAssignmentInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ItemAssignmentInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ItemAssignmentPage(self._version, response, self._solution)

    def get(self, sid: str) -> ItemAssignmentContext:
        """
        Constructs a ItemAssignmentContext

        :param sid: The unique string that we created to identify the Identity resource.
        """
        return ItemAssignmentContext(
            self._version, bundle_sid=self._solution["bundle_sid"], sid=sid
        )

    def __call__(self, sid: str) -> ItemAssignmentContext:
        """
        Constructs a ItemAssignmentContext

        :param sid: The unique string that we created to identify the Identity resource.
        """
        return ItemAssignmentContext(
            self._version, bundle_sid=self._solution["bundle_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.ItemAssignmentList>"
