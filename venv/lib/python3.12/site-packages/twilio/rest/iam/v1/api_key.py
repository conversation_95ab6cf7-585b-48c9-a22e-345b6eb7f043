r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Iam
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class ApiKeyInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Key resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar policy: The \\`Policy\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.policy: Optional[Dict[str, object]] = payload.get("policy")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ApiKeyContext] = None

    @property
    def _proxy(self) -> "ApiKeyContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ApiKeyContext for this ApiKeyInstance
        """
        if self._context is None:
            self._context = ApiKeyContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ApiKeyInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ApiKeyInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ApiKeyInstance":
        """
        Fetch the ApiKeyInstance


        :returns: The fetched ApiKeyInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ApiKeyInstance":
        """
        Asynchronous coroutine to fetch the ApiKeyInstance


        :returns: The fetched ApiKeyInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        policy: Union[object, object] = values.unset,
    ) -> "ApiKeyInstance":
        """
        Update the ApiKeyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param policy: The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).

        :returns: The updated ApiKeyInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            policy=policy,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        policy: Union[object, object] = values.unset,
    ) -> "ApiKeyInstance":
        """
        Asynchronous coroutine to update the ApiKeyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param policy: The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).

        :returns: The updated ApiKeyInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            policy=policy,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Iam.V1.ApiKeyInstance {}>".format(context)


class ApiKeyContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ApiKeyContext

        :param version: Version that contains the resource
        :param sid: The Twilio-provided string that uniquely identifies the Key resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Keys/{sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the ApiKeyInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ApiKeyInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ApiKeyInstance:
        """
        Fetch the ApiKeyInstance


        :returns: The fetched ApiKeyInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ApiKeyInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ApiKeyInstance:
        """
        Asynchronous coroutine to fetch the ApiKeyInstance


        :returns: The fetched ApiKeyInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ApiKeyInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        policy: Union[object, object] = values.unset,
    ) -> ApiKeyInstance:
        """
        Update the ApiKeyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param policy: The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).

        :returns: The updated ApiKeyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Policy": serialize.object(policy),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApiKeyInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        policy: Union[object, object] = values.unset,
    ) -> ApiKeyInstance:
        """
        Asynchronous coroutine to update the ApiKeyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It can be up to 64 characters long.
        :param policy: The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).

        :returns: The updated ApiKeyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Policy": serialize.object(policy),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ApiKeyInstance(self._version, payload, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Iam.V1.ApiKeyContext {}>".format(context)


class ApiKeyList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ApiKeyList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, sid: str) -> ApiKeyContext:
        """
        Constructs a ApiKeyContext

        :param sid: The Twilio-provided string that uniquely identifies the Key resource to update.
        """
        return ApiKeyContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ApiKeyContext:
        """
        Constructs a ApiKeyContext

        :param sid: The Twilio-provided string that uniquely identifies the Key resource to update.
        """
        return ApiKeyContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Iam.V1.ApiKeyList>"
