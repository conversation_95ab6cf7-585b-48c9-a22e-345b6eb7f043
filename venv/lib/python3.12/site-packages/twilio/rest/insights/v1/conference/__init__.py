r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Insights
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.insights.v1.conference.conference_participant import (
    ConferenceParticipantList,
)


class ConferenceInstance(InstanceResource):

    class ConferenceEndReason(object):
        LAST_PARTICIPANT_LEFT = "last_participant_left"
        CONFERENCE_ENDED_VIA_API = "conference_ended_via_api"
        PARTICIPANT_WITH_END_CONFERENCE_ON_EXIT_LEFT = (
            "participant_with_end_conference_on_exit_left"
        )
        LAST_PARTICIPANT_KICKED = "last_participant_kicked"
        PARTICIPANT_WITH_END_CONFERENCE_ON_EXIT_KICKED = (
            "participant_with_end_conference_on_exit_kicked"
        )

    class ConferenceStatus(object):
        IN_PROGRESS = "in_progress"
        NOT_STARTED = "not_started"
        COMPLETED = "completed"
        SUMMARY_TIMEOUT = "summary_timeout"

    class ProcessingState(object):
        COMPLETE = "complete"
        IN_PROGRESS = "in_progress"
        TIMEOUT = "timeout"

    class Region(object):
        US1 = "us1"
        US2 = "us2"
        AU1 = "au1"
        BR1 = "br1"
        IE1 = "ie1"
        JP1 = "jp1"
        SG1 = "sg1"
        DE1 = "de1"
        IN1 = "in1"

    class Tag(object):
        INVALID_REQUESTED_REGION = "invalid_requested_region"
        DUPLICATE_IDENTITY = "duplicate_identity"
        START_FAILURE = "start_failure"
        REGION_CONFIGURATION_ISSUES = "region_configuration_issues"
        QUALITY_WARNINGS = "quality_warnings"
        PARTICIPANT_BEHAVIOR_ISSUES = "participant_behavior_issues"
        HIGH_PACKET_LOSS = "high_packet_loss"
        HIGH_JITTER = "high_jitter"
        HIGH_LATENCY = "high_latency"
        LOW_MOS = "low_mos"
        DETECTED_SILENCE = "detected_silence"
        NO_CONCURRENT_PARTICIPANTS = "no_concurrent_participants"

    """
    :ivar conference_sid: The unique SID identifier of the Conference.
    :ivar account_sid: The unique SID identifier of the Account.
    :ivar friendly_name: Custom label for the conference resource, up to 64 characters.
    :ivar create_time: Conference creation date and time in ISO 8601 format.
    :ivar start_time: Timestamp in ISO 8601 format when the conference started. Conferences do not start until at least two participants join, at least one of whom has startConferenceOnEnter=true.
    :ivar end_time: Conference end date and time in ISO 8601 format.
    :ivar duration_seconds: Conference duration in seconds.
    :ivar connect_duration_seconds: Duration of the between conference start event and conference end event in seconds.
    :ivar status: 
    :ivar max_participants: Maximum number of concurrent participants as specified by the configuration.
    :ivar max_concurrent_participants: Actual maximum number of concurrent participants in the conference.
    :ivar unique_participants: Unique conference participants based on caller ID.
    :ivar end_reason: 
    :ivar ended_by: Call SID of the participant whose actions ended the conference.
    :ivar mixer_region: 
    :ivar mixer_region_requested: 
    :ivar recording_enabled: Boolean. Indicates whether recording was enabled at the conference mixer.
    :ivar detected_issues: Potential issues detected by Twilio during the conference.
    :ivar tags: Tags for detected conference conditions and participant behaviors which may be of interest.
    :ivar tag_info: Object. Contains details about conference tags including severity.
    :ivar processing_state: 
    :ivar url: The URL of this resource.
    :ivar links: Contains a dictionary of URL links to nested resources of this Conference.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        conference_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.conference_sid: Optional[str] = payload.get("conference_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.create_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("create_time")
        )
        self.start_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("start_time")
        )
        self.end_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("end_time")
        )
        self.duration_seconds: Optional[int] = deserialize.integer(
            payload.get("duration_seconds")
        )
        self.connect_duration_seconds: Optional[int] = deserialize.integer(
            payload.get("connect_duration_seconds")
        )
        self.status: Optional["ConferenceInstance.ConferenceStatus"] = payload.get(
            "status"
        )
        self.max_participants: Optional[int] = deserialize.integer(
            payload.get("max_participants")
        )
        self.max_concurrent_participants: Optional[int] = deserialize.integer(
            payload.get("max_concurrent_participants")
        )
        self.unique_participants: Optional[int] = deserialize.integer(
            payload.get("unique_participants")
        )
        self.end_reason: Optional["ConferenceInstance.ConferenceEndReason"] = (
            payload.get("end_reason")
        )
        self.ended_by: Optional[str] = payload.get("ended_by")
        self.mixer_region: Optional["ConferenceInstance.Region"] = payload.get(
            "mixer_region"
        )
        self.mixer_region_requested: Optional["ConferenceInstance.Region"] = (
            payload.get("mixer_region_requested")
        )
        self.recording_enabled: Optional[bool] = payload.get("recording_enabled")
        self.detected_issues: Optional[Dict[str, object]] = payload.get(
            "detected_issues"
        )
        self.tags: Optional[List["ConferenceInstance.Tag"]] = payload.get("tags")
        self.tag_info: Optional[Dict[str, object]] = payload.get("tag_info")
        self.processing_state: Optional["ConferenceInstance.ProcessingState"] = (
            payload.get("processing_state")
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "conference_sid": conference_sid or self.conference_sid,
        }
        self._context: Optional[ConferenceContext] = None

    @property
    def _proxy(self) -> "ConferenceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ConferenceContext for this ConferenceInstance
        """
        if self._context is None:
            self._context = ConferenceContext(
                self._version,
                conference_sid=self._solution["conference_sid"],
            )
        return self._context

    def fetch(self) -> "ConferenceInstance":
        """
        Fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ConferenceInstance":
        """
        Asynchronous coroutine to fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """
        return await self._proxy.fetch_async()

    @property
    def conference_participants(self) -> ConferenceParticipantList:
        """
        Access the conference_participants
        """
        return self._proxy.conference_participants

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Insights.V1.ConferenceInstance {}>".format(context)


class ConferenceContext(InstanceContext):

    def __init__(self, version: Version, conference_sid: str):
        """
        Initialize the ConferenceContext

        :param version: Version that contains the resource
        :param conference_sid: The unique SID identifier of the Conference.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "conference_sid": conference_sid,
        }
        self._uri = "/Conferences/{conference_sid}".format(**self._solution)

        self._conference_participants: Optional[ConferenceParticipantList] = None

    def fetch(self) -> ConferenceInstance:
        """
        Fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ConferenceInstance(
            self._version,
            payload,
            conference_sid=self._solution["conference_sid"],
        )

    async def fetch_async(self) -> ConferenceInstance:
        """
        Asynchronous coroutine to fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ConferenceInstance(
            self._version,
            payload,
            conference_sid=self._solution["conference_sid"],
        )

    @property
    def conference_participants(self) -> ConferenceParticipantList:
        """
        Access the conference_participants
        """
        if self._conference_participants is None:
            self._conference_participants = ConferenceParticipantList(
                self._version,
                self._solution["conference_sid"],
            )
        return self._conference_participants

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Insights.V1.ConferenceContext {}>".format(context)


class ConferencePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ConferenceInstance:
        """
        Build an instance of ConferenceInstance

        :param payload: Payload response from the API
        """
        return ConferenceInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Insights.V1.ConferencePage>"


class ConferenceList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ConferenceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Conferences"

    def stream(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ConferenceInstance]:
        """
        Streams ConferenceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str conference_sid: The SID of the conference.
        :param str friendly_name: Custom label for the conference resource, up to 64 characters.
        :param str status: Conference status.
        :param str created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param str created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param str mixer_region: Twilio region where the conference media was mixed.
        :param str tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param str subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param str detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param str end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            conference_sid=conference_sid,
            friendly_name=friendly_name,
            status=status,
            created_after=created_after,
            created_before=created_before,
            mixer_region=mixer_region,
            tags=tags,
            subaccount=subaccount,
            detected_issues=detected_issues,
            end_reason=end_reason,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ConferenceInstance]:
        """
        Asynchronously streams ConferenceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str conference_sid: The SID of the conference.
        :param str friendly_name: Custom label for the conference resource, up to 64 characters.
        :param str status: Conference status.
        :param str created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param str created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param str mixer_region: Twilio region where the conference media was mixed.
        :param str tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param str subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param str detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param str end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            conference_sid=conference_sid,
            friendly_name=friendly_name,
            status=status,
            created_after=created_after,
            created_before=created_before,
            mixer_region=mixer_region,
            tags=tags,
            subaccount=subaccount,
            detected_issues=detected_issues,
            end_reason=end_reason,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConferenceInstance]:
        """
        Lists ConferenceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str conference_sid: The SID of the conference.
        :param str friendly_name: Custom label for the conference resource, up to 64 characters.
        :param str status: Conference status.
        :param str created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param str created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param str mixer_region: Twilio region where the conference media was mixed.
        :param str tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param str subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param str detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param str end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                conference_sid=conference_sid,
                friendly_name=friendly_name,
                status=status,
                created_after=created_after,
                created_before=created_before,
                mixer_region=mixer_region,
                tags=tags,
                subaccount=subaccount,
                detected_issues=detected_issues,
                end_reason=end_reason,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConferenceInstance]:
        """
        Asynchronously lists ConferenceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str conference_sid: The SID of the conference.
        :param str friendly_name: Custom label for the conference resource, up to 64 characters.
        :param str status: Conference status.
        :param str created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param str created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param str mixer_region: Twilio region where the conference media was mixed.
        :param str tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param str subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param str detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param str end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                conference_sid=conference_sid,
                friendly_name=friendly_name,
                status=status,
                created_after=created_after,
                created_before=created_before,
                mixer_region=mixer_region,
                tags=tags,
                subaccount=subaccount,
                detected_issues=detected_issues,
                end_reason=end_reason,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConferencePage:
        """
        Retrieve a single page of ConferenceInstance records from the API.
        Request is executed immediately

        :param conference_sid: The SID of the conference.
        :param friendly_name: Custom label for the conference resource, up to 64 characters.
        :param status: Conference status.
        :param created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param mixer_region: Twilio region where the conference media was mixed.
        :param tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConferenceInstance
        """
        data = values.of(
            {
                "ConferenceSid": conference_sid,
                "FriendlyName": friendly_name,
                "Status": status,
                "CreatedAfter": created_after,
                "CreatedBefore": created_before,
                "MixerRegion": mixer_region,
                "Tags": tags,
                "Subaccount": subaccount,
                "DetectedIssues": detected_issues,
                "EndReason": end_reason,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConferencePage(self._version, response)

    async def page_async(
        self,
        conference_sid: Union[str, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union[str, object] = values.unset,
        created_after: Union[str, object] = values.unset,
        created_before: Union[str, object] = values.unset,
        mixer_region: Union[str, object] = values.unset,
        tags: Union[str, object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        detected_issues: Union[str, object] = values.unset,
        end_reason: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConferencePage:
        """
        Asynchronously retrieve a single page of ConferenceInstance records from the API.
        Request is executed immediately

        :param conference_sid: The SID of the conference.
        :param friendly_name: Custom label for the conference resource, up to 64 characters.
        :param status: Conference status.
        :param created_after: Conferences created after the provided timestamp specified in ISO 8601 format
        :param created_before: Conferences created before the provided timestamp specified in ISO 8601 format.
        :param mixer_region: Twilio region where the conference media was mixed.
        :param tags: Tags applied by Twilio for common potential configuration, quality, or performance issues.
        :param subaccount: Account SID for the subaccount whose resources you wish to retrieve.
        :param detected_issues: Potential configuration, behavior, or performance issues detected during the conference.
        :param end_reason: Conference end reason; e.g. last participant left, modified by API, etc.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConferenceInstance
        """
        data = values.of(
            {
                "ConferenceSid": conference_sid,
                "FriendlyName": friendly_name,
                "Status": status,
                "CreatedAfter": created_after,
                "CreatedBefore": created_before,
                "MixerRegion": mixer_region,
                "Tags": tags,
                "Subaccount": subaccount,
                "DetectedIssues": detected_issues,
                "EndReason": end_reason,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConferencePage(self._version, response)

    def get_page(self, target_url: str) -> ConferencePage:
        """
        Retrieve a specific page of ConferenceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConferenceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ConferencePage(self._version, response)

    async def get_page_async(self, target_url: str) -> ConferencePage:
        """
        Asynchronously retrieve a specific page of ConferenceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConferenceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ConferencePage(self._version, response)

    def get(self, conference_sid: str) -> ConferenceContext:
        """
        Constructs a ConferenceContext

        :param conference_sid: The unique SID identifier of the Conference.
        """
        return ConferenceContext(self._version, conference_sid=conference_sid)

    def __call__(self, conference_sid: str) -> ConferenceContext:
        """
        Constructs a ConferenceContext

        :param conference_sid: The unique SID identifier of the Conference.
        """
        return ConferenceContext(self._version, conference_sid=conference_sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Insights.V1.ConferenceList>"
