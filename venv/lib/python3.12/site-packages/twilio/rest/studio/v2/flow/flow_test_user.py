r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Studio
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class FlowTestUserInstance(InstanceResource):
    """
    :ivar sid: Unique identifier of the flow.
    :ivar test_users: List of test user identities that can test draft versions of the flow.
    :ivar url: The URL of this resource.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], sid: str):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.test_users: Optional[List[str]] = payload.get("test_users")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid,
        }
        self._context: Optional[FlowTestUserContext] = None

    @property
    def _proxy(self) -> "FlowTestUserContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: FlowTestUserContext for this FlowTestUserInstance
        """
        if self._context is None:
            self._context = FlowTestUserContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "FlowTestUserInstance":
        """
        Fetch the FlowTestUserInstance


        :returns: The fetched FlowTestUserInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "FlowTestUserInstance":
        """
        Asynchronous coroutine to fetch the FlowTestUserInstance


        :returns: The fetched FlowTestUserInstance
        """
        return await self._proxy.fetch_async()

    def update(self, test_users: List[str]) -> "FlowTestUserInstance":
        """
        Update the FlowTestUserInstance

        :param test_users: List of test user identities that can test draft versions of the flow.

        :returns: The updated FlowTestUserInstance
        """
        return self._proxy.update(
            test_users=test_users,
        )

    async def update_async(self, test_users: List[str]) -> "FlowTestUserInstance":
        """
        Asynchronous coroutine to update the FlowTestUserInstance

        :param test_users: List of test user identities that can test draft versions of the flow.

        :returns: The updated FlowTestUserInstance
        """
        return await self._proxy.update_async(
            test_users=test_users,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Studio.V2.FlowTestUserInstance {}>".format(context)


class FlowTestUserContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the FlowTestUserContext

        :param version: Version that contains the resource
        :param sid: Unique identifier of the flow.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Flows/{sid}/TestUsers".format(**self._solution)

    def fetch(self) -> FlowTestUserInstance:
        """
        Fetch the FlowTestUserInstance


        :returns: The fetched FlowTestUserInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return FlowTestUserInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> FlowTestUserInstance:
        """
        Asynchronous coroutine to fetch the FlowTestUserInstance


        :returns: The fetched FlowTestUserInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return FlowTestUserInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(self, test_users: List[str]) -> FlowTestUserInstance:
        """
        Update the FlowTestUserInstance

        :param test_users: List of test user identities that can test draft versions of the flow.

        :returns: The updated FlowTestUserInstance
        """

        data = values.of(
            {
                "TestUsers": serialize.map(test_users, lambda e: e),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlowTestUserInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(self, test_users: List[str]) -> FlowTestUserInstance:
        """
        Asynchronous coroutine to update the FlowTestUserInstance

        :param test_users: List of test user identities that can test draft versions of the flow.

        :returns: The updated FlowTestUserInstance
        """

        data = values.of(
            {
                "TestUsers": serialize.map(test_users, lambda e: e),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return FlowTestUserInstance(self._version, payload, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Studio.V2.FlowTestUserContext {}>".format(context)


class FlowTestUserList(ListResource):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the FlowTestUserList

        :param version: Version that contains the resource
        :param sid: Unique identifier of the flow.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }

    def get(self) -> FlowTestUserContext:
        """
        Constructs a FlowTestUserContext

        """
        return FlowTestUserContext(self._version, sid=self._solution["sid"])

    def __call__(self) -> FlowTestUserContext:
        """
        Constructs a FlowTestUserContext

        """
        return FlowTestUserContext(self._version, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Studio.V2.FlowTestUserList>"
