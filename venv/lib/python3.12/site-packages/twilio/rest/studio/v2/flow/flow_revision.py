r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Studio
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class FlowRevisionInstance(InstanceResource):

    class Status(object):
        DRAFT = "draft"
        PUBLISHED = "published"

    """
    :ivar sid: The unique string that we created to identify the Flow resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Flow resource.
    :ivar friendly_name: The string that you assigned to describe the Flow.
    :ivar definition: JSON representation of flow definition.
    :ivar status: 
    :ivar revision: The latest revision number of the Flow's definition.
    :ivar commit_message: Description of change made in the revision.
    :ivar valid: Boolean if the flow definition is valid.
    :ivar errors: List of error in the flow definition.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        sid: str,
        revision: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.definition: Optional[Dict[str, object]] = payload.get("definition")
        self.status: Optional["FlowRevisionInstance.Status"] = payload.get("status")
        self.revision: Optional[int] = deserialize.integer(payload.get("revision"))
        self.commit_message: Optional[str] = payload.get("commit_message")
        self.valid: Optional[bool] = payload.get("valid")
        self.errors: Optional[List[Dict[str, object]]] = payload.get("errors")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "sid": sid,
            "revision": revision or self.revision,
        }
        self._context: Optional[FlowRevisionContext] = None

    @property
    def _proxy(self) -> "FlowRevisionContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: FlowRevisionContext for this FlowRevisionInstance
        """
        if self._context is None:
            self._context = FlowRevisionContext(
                self._version,
                sid=self._solution["sid"],
                revision=self._solution["revision"],
            )
        return self._context

    def fetch(self) -> "FlowRevisionInstance":
        """
        Fetch the FlowRevisionInstance


        :returns: The fetched FlowRevisionInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "FlowRevisionInstance":
        """
        Asynchronous coroutine to fetch the FlowRevisionInstance


        :returns: The fetched FlowRevisionInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Studio.V2.FlowRevisionInstance {}>".format(context)


class FlowRevisionContext(InstanceContext):

    def __init__(self, version: Version, sid: str, revision: str):
        """
        Initialize the FlowRevisionContext

        :param version: Version that contains the resource
        :param sid: The SID of the Flow resource to fetch.
        :param revision: Specific Revision number or can be `LatestPublished` and `LatestRevision`.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
            "revision": revision,
        }
        self._uri = "/Flows/{sid}/Revisions/{revision}".format(**self._solution)

    def fetch(self) -> FlowRevisionInstance:
        """
        Fetch the FlowRevisionInstance


        :returns: The fetched FlowRevisionInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return FlowRevisionInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
            revision=self._solution["revision"],
        )

    async def fetch_async(self) -> FlowRevisionInstance:
        """
        Asynchronous coroutine to fetch the FlowRevisionInstance


        :returns: The fetched FlowRevisionInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return FlowRevisionInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
            revision=self._solution["revision"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Studio.V2.FlowRevisionContext {}>".format(context)


class FlowRevisionPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> FlowRevisionInstance:
        """
        Build an instance of FlowRevisionInstance

        :param payload: Payload response from the API
        """
        return FlowRevisionInstance(self._version, payload, sid=self._solution["sid"])

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Studio.V2.FlowRevisionPage>"


class FlowRevisionList(ListResource):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the FlowRevisionList

        :param version: Version that contains the resource
        :param sid: The SID of the Flow resource to fetch.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Flows/{sid}/Revisions".format(**self._solution)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[FlowRevisionInstance]:
        """
        Streams FlowRevisionInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[FlowRevisionInstance]:
        """
        Asynchronously streams FlowRevisionInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FlowRevisionInstance]:
        """
        Lists FlowRevisionInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[FlowRevisionInstance]:
        """
        Asynchronously lists FlowRevisionInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FlowRevisionPage:
        """
        Retrieve a single page of FlowRevisionInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FlowRevisionInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FlowRevisionPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> FlowRevisionPage:
        """
        Asynchronously retrieve a single page of FlowRevisionInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of FlowRevisionInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return FlowRevisionPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> FlowRevisionPage:
        """
        Retrieve a specific page of FlowRevisionInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FlowRevisionInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return FlowRevisionPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> FlowRevisionPage:
        """
        Asynchronously retrieve a specific page of FlowRevisionInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of FlowRevisionInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return FlowRevisionPage(self._version, response, self._solution)

    def get(self, revision: str) -> FlowRevisionContext:
        """
        Constructs a FlowRevisionContext

        :param revision: Specific Revision number or can be `LatestPublished` and `LatestRevision`.
        """
        return FlowRevisionContext(
            self._version, sid=self._solution["sid"], revision=revision
        )

    def __call__(self, revision: str) -> FlowRevisionContext:
        """
        Constructs a FlowRevisionContext

        :param revision: Specific Revision number or can be `LatestPublished` and `LatestRevision`.
        """
        return FlowRevisionContext(
            self._version, sid=self._solution["sid"], revision=revision
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Studio.V2.FlowRevisionList>"
