#!/usr/bin/env python3
import os, csv, sys
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
from dotenv import load_dotenv

load_dotenv()

MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
DB_NAME = os.getenv("MONGO_DB", "ai_moderation")

client = MongoClient(MONGO_URI)
db = client[DB_NAME]
users = db["users"]

def rotate(user_id, key_id="n8n-default", keep_old_days=1):
    from datetime import datetime, timedelta
    import secrets
    uid = ObjectId(user_id)
    doc = users.find_one({"_id": uid}, {"service_api": 1})
    if not doc or "service_api" not in doc:
        # initialize
        secret = secrets.token_urlsafe(32)
        users.update_one({"_id": uid}, {"$set": {"service_api": {"key_id": key_id, "secret": secret, "created_at": datetime.utcnow(), "last_rotated_at": datetime.utcnow()}}}, upsert=False)
        return True, secret
    sa = doc["service_api"]
    old_secret = sa.get("secret")
    if not old_secret:
        import secrets
        secret = secrets.token_urlsafe(32)
        users.update_one({"_id": uid}, {"$set": {"service_api": {"key_id": key_id, "secret": secret, "created_at": datetime.utcnow(), "last_rotated_at": datetime.utcnow()}}})
        return True, secret
    # move old to keys[] with expires_at
    expires_at = datetime.utcnow() + timedelta(days=keep_old_days)
    users.update_one({"_id": uid}, {"$push": {"service_api.keys": {"key_id": sa.get("key_id", key_id), "secret": old_secret, "created_at": sa.get("created_at", datetime.utcnow()), "expires_at": expires_at}}})
    # set new secret
    import secrets
    new_secret = secrets.token_urlsafe(32)
    users.update_one({"_id": uid}, {"$set": {"service_api.key_id": key_id, "service_api.secret": new_secret, "service_api.last_rotated_at": datetime.utcnow()}})
    return True, new_secret

def main():
    days = int(os.getenv("ROTATE_AFTER_DAYS", "90"))
    keep_old_days = int(os.getenv("KEEP_OLD_DAYS", "1"))
    cutoff = datetime.utcnow().timestamp() - days*24*3600
    due = users.find({"service_api.last_rotated_at": {"$lt": datetime.utcfromtimestamp(cutoff)}}, {"_id":1}).batch_size(500)
    out_path = os.getenv("ROTATE_OUTPUT", f"rotated_keys_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.csv")
    with open(out_path, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["user_id","key_id","new_secret","rotated_at_utc"])
        count = 0
        for d in due:
            ok, new_secret = rotate(str(d["_id"]), keep_old_days=keep_old_days)
            if ok:
                w.writerow([str(d["_id"]), "n8n-default", new_secret, datetime.utcnow().isoformat()+"Z"])
                count += 1
        print(f"Rotated {count} users. Output: {out_path}")

if __name__ == "__main__":
    main()