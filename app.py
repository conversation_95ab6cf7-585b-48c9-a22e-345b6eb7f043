import os
import logging
from flask import Flask, session, redirect, render_template, flash, url_for, request
from flask_wtf.csrf import CSRFProtect
from flask_session import Session
from routes import auth, linkedin, facebook, twitter, youtube, instagram, subscription, publish
from routes.auth import login_required
from dotenv import load_dotenv
from datetime import timedelta

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Set session configuration
app.config["SECRET_KEY"] = os.getenv("SECRET_KEY", "dev_key_for_testing")
app.config["SESSION_TYPE"] = "filesystem"
app.config["SESSION_PERMANENT"] = True
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(hours=1)
app.config["SESSION_USE_SIGNER"] = True
app.config["SESSION_FILE_DIR"] = os.path.join(os.getcwd(), "flask_session")
app.config["SESSION_COOKIE_SECURE"] = False  # Set to True in production with HTTPS
app.config["SESSION_COOKIE_HTTPONLY"] = True
app.config["SESSION_COOKIE_SAMESITE"] = "Lax"

# Ensure session directory exists
os.makedirs(app.config["SESSION_FILE_DIR"], exist_ok=True)

# Initialize Flask-Session
Session(app)

# Add session debugging middleware
@app.before_request
def log_session():
    import logging
    logging.info(f"Request path: {request.path}, Method: {request.method}")
    logging.info(f"Session data: {dict(session)}")
    logging.info(f"Request cookies: {request.cookies}")

# Initialize CSRF protection
csrf = CSRFProtect(app)

app.register_blueprint(auth.bp)  # Empty prefix to allow /logout directly
app.register_blueprint(linkedin.bp)
app.register_blueprint(facebook.bp)
app.register_blueprint(twitter.bp)
app.register_blueprint(youtube.bp)
app.register_blueprint(instagram.bp)
app.register_blueprint(subscription.bp)
app.register_blueprint(publish.bp)


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/connect")
def connect():
    import logging
    logging.info(f"Connect route - Session: {dict(session)}")
    
    # Check if user is logged in
    if "user_id" not in session:
        logging.error("Connect route - No user_id in session")
        flash("Please log in to access your connected accounts")
        return redirect(url_for("auth.send_otp", next=url_for("connect")))
    
    # Log the user_id to verify it's correct
    logging.info(f"Connect route - User ID: {session.get('user_id')}")
    
    return render_template("connect.html")


@app.route("/logout")
def logout():
    if "user_id" in session:
        session.pop("user_id", None)
        session.clear()
        flash("You have been successfully logged out.")
    
    return redirect("/")


if __name__ == "__main__":
    app.run(debug=True)
